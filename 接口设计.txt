# 居家养老健康管理系统接口设计

## 一、基本功能接口

| 接口名称 | 用途 | 目标 | 使用者 |
|---------|------|------|--------|
| /api/auth/register | 用户注册 | 创建新用户账号，分配不同身份角色 | 老人/家属/社区管理员 |
| /api/auth/login | 用户登录 | 验证用户身份并生成会话 | 老人/家属/社区管理员 |
| /api/auth/logout | 用户注销 | 退出系统并清除会话 | 老人/家属/社区管理员 |
| /api/user/profile | 用户信息 | 获取当前登录用户的个人信息 | 所有用户 |
| /api/user/password | 密码修改 | 修改用户密码 | 所有用户 |
| /api/notifications | 系统通知 | 获取系统推送的通知消息 | 所有用户 | 
## 二、老人端接口

| 接口名称 | 用途 | 目标 | 使用者 |
|---------|------|------|--------|
| /api/elderly/health/status | 健康状况查看 | 获取运动、睡眠、饮食、心率、血压、血糖等健康数据 | 老人 |
| /api/elderly/devices | 健康设备管理 | 查询和管理健康监测设备，进行绑定、数据同步等操作 | 老人 |
| /api/elderly/meal/menu | 餐品菜单查询 | 获取可订餐品列表 | 老人 |
| /api/elderly/meal/order | 订餐下单 | 创建新的订餐订单，选择餐食类型和配送时间 | 老人 |
| /api/elderly/records | 健康档案管理 | 查看、更新个人健康档案信息，包括病史、检查报告、用药记录 | 老人 |
| /api/elderly/article/list | 健康文章列表 | 获取健康科普文章列表 | 老人 |
| /api/elderly/article/detail | 文章详情查询 | 获取健康文章详细内容 | 老人 |
| /api/elderly/article/collection | 文章收藏管理 | 添加或删除收藏文章 | 老人 |
| /api/elderly/emergency/call | 紧急呼叫 | 发起紧急求助 | 老人 |

## 三、家属端接口

| 接口名称 | 用途 | 目标 | 使用者 |
|---------|------|------|--------|
| /api/family/monitor/vitals | 实时体征查看 | 获取老人的运动、睡眠、饮食、心率、血压、血糖等实时数据 | 家属 |
| /api/family/emergency/notifications | 紧急呼叫通知 | 接收老人发起的紧急呼叫信息 | 家属 |
| /api/family/emergency/contact | 紧急联系 | 与老人取得联系，了解情况 | 家属 |
| /api/family/records/view | 健康档案查看 | 查看老人的健康档案信息 | 家属 |
| /api/family/location/tracking | 实时位置查看 | 查看老人的当前位置信息 | 家属 |
| /api/family/profile/edit | 个人资料编辑 | 查看和修改个人资料信息 | 家属 |
| /api/family/meal/order | 远程代订餐 | 为老人远程订餐，选择适合餐食 | 家属 |

## 四、社区管理员端接口

| 接口名称 | 用途 | 目标 | 使用者 |
|---------|------|------|--------|
| /api/admin/emergency/list | 紧急呼叫列表 | 获取所有紧急呼叫信息 | 社区管理员 |
| /api/admin/emergency/handle | 紧急呼叫处理 | 处理紧急呼叫，安排救助资源 | 社区管理员 |
| /api/admin/meal/orders | 订餐订单列表 | 获取所有老人的订餐订单 | 社区管理员 |
| /api/admin/meal/delivery/assign | 送餐人员分配 | 根据订单情况合理分配送餐人员 | 社区管理员 |
| /api/admin/meal/delivery/status | 送餐状态更新 | 更新送餐状态，确保餐食按时送达 | 社区管理员 |
| /api/admin/statistics | 数据统计报表 | 生成老人健康、订餐和呼叫统计数据 | 社区管理员 |

