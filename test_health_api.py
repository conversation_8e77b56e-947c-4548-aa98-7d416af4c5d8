#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
健康科普API测试脚本
用于验证数据库修复后API是否正常工作
"""

import requests
import json
import sys

def test_api_endpoint(url, description):
    """测试API端点"""
    print(f"\n🔍 测试: {description}")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 成功: {description}")
            
            # 根据不同的API返回不同的信息
            if 'articles' in data:
                print(f"   文章数量: {len(data['articles'])}")
                if data['articles']:
                    print(f"   第一篇文章: {data['articles'][0]['title']}")
            elif 'message' in data:
                print(f"   消息: {data['message']}")
            elif 'categories' in data:
                print(f"   分类数量: {len(data['categories'])}")
                print(f"   分类列表: {', '.join(data['categories'])}")
            
            return True
        else:
            print(f"❌ 失败: HTTP {response.status_code}")
            print(f"   响应内容: {response.text[:200]}...")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络错误: {str(e)}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析错误: {str(e)}")
        print(f"   响应内容: {response.text[:200]}...")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试健康科普API...")
    
    base_url = "http://localhost:5000"
    
    # 测试用例列表
    test_cases = [
        (f"{base_url}/api/health/test", "API服务状态测试"),
        (f"{base_url}/api/health/articles", "获取所有文章"),
        (f"{base_url}/api/health/articles?category=养生", "获取养生分类文章"),
        (f"{base_url}/api/health/articles?category=急救", "获取急救分类文章"),
        (f"{base_url}/api/health/articles?page=1&per_page=3", "分页获取文章"),
        (f"{base_url}/api/health/articles/categories", "获取文章分类"),
    ]
    
    # 执行测试
    success_count = 0
    total_count = len(test_cases)
    
    for url, description in test_cases:
        if test_api_endpoint(url, description):
            success_count += 1
    
    # 输出测试结果
    print(f"\n📊 测试结果:")
    print(f"   总测试数: {total_count}")
    print(f"   成功数: {success_count}")
    print(f"   失败数: {total_count - success_count}")
    print(f"   成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("\n🎉 所有测试通过！健康科普API工作正常。")
        return 0
    else:
        print(f"\n⚠️  有 {total_count - success_count} 个测试失败，请检查:")
        print("   1. Flask应用是否正在运行")
        print("   2. 数据库连接是否正常")
        print("   3. healtharticle表是否存在并包含数据")
        return 1

if __name__ == "__main__":
    sys.exit(main())
