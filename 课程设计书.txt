
《Web程序设计》课程设计文档
1 选题及作品名称
选题：
设计类型：
作品名称：
2 要实现的需求
请注意：根据教师给出的需求文档，结合自己小组要做的需求来完成此模块的编写。
踩分点：痛点分析，解决方案
3 功能模块设计
3.1 操作流程设计
请注意：
1.用步骤形式文字描述操作流程；
2.参照下图给出期末项目的操作流程图，如果图超过一页，请分层次来表现流程，即分为功能操作流程，某功能内部操作流程等多张图来表现流程设计。

图3-1 xx图
3.2 界面设计
请注意：
1.根据流程来设计项目的主要界面，给出界面的文字说明，并阐述清楚交互操作，交互界面可以是低保真线框图，或为高保真原型图；
2.设计图不能都是方框，需要细化到界面需要的信息项和交互说明；
3.界面图上可以有交互操作注释文字；
4.界面要求简洁明了、风格和配色统一。


图3-2 xx系统的主要界面（供参考）

图3-3 xx系统的交互操作说明（供参考）

3.3 功能设计
请注意：
1.结合自己小组要做的需求来完成此模块的编写，给出功能层次图（参考图3-4）；
2.功能项的编写以列表形式展开，每项功能的书写格式和内容参考如下。

【功能项书写格式和内容参考】
?添加用户信息：本功能主要实现用户信息的添加。用户信息包括用户名、密码、性别、真实姓名、头像这5个信息项。其中用户名要求6-18位英文字符、下划线和数字，必须英文字符开头；……。


图3-4 xx系统功能层次图

3.3 数据库设计
请注意：
1.根据功能设计来设计数据存储方案，给出E-R图（参考图3-5），E-R图超过一页，可分层次（多张图）；
2.根据ER图来设计数据库表。



图3-5 xx系统的E-R图设计
表3-1 xx表设计
字段名称	数据类型	长度	字段含义	是否主键
				
				
				
				

表3-2 xx表设计
字段名称	数据类型	长度	字段含义	是否主键
				
				
				
				
……

3.4 接口设计
1.XXX接口
功能简介：
请求URL：
请求方式：
参数设置：
成功数据返回：
失败数据返回：

2.XXX接口
功能简介：
请求URL：
请求方式：
参数设置：
成功数据返回：
失败数据返回：

……
4 软件说明
4.1 开发环境说明
4.2 运行环境说明
5 开发计划
5.1 时间安排
5.2 项目分工
……
6 非原创部分说明
[1]xxx(作者，可省略), 参考资料名称, 出处
[2]……

