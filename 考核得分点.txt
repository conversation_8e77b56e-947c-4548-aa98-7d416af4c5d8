### 期末考核得分要点分项整理
一、系统设计（占比40分）
1. 功能模块覆盖
   需涵盖所有功能模块的详细设计
2. 开发模式说明 
   可选择任意开发模式（如MVC），但必须明确写出选择的模式及其设计逻辑。
3. 数据库设计 
   概念模型：需包含实体类图，展示功能模块的参数传递关系。
   关系模型：
   物理设计：明确表结构设计（如用SQL示例），需以示例形式呈现。
4. 项目架构搭建 
   包括配置的JAR包、网站界面设计、数据库连接配置等，需写清细节。

二、业务
1. 接口定义
   每个接口的名称、用途、目标需完整列出，形成接口说明表。  
2. 业务流程描述 
   用2-3段文字梳理题目业务流程，并绘制对应的**业务流程图**（无图扣分）。  

   - 需标注接口对应的使用者（用户端/管理端）及其对应的说明写出来。

三、用例分析图
    用户端与管理端各自的用例图（一定要画），体现连接关系。


四、功能模块设计
   关键模块图（图1）
    
   需与前面的接口一一对应，体现一对一关系。  


  


#### **四、功能模块设计**
1. **模块功能描述**  
   - 每个模块需写明功能及描述（如用户模块的注册、登录、订单确认）。  
   - 需与接口一一对应，体现一对一关系。  
2. **子功能细化**  
   - 每个模块的子功能需另开章节详细说明（如用户中心的个人信息管理）。  

#### **五、管理端设计**
1. **模块分类与功能**  
   - 管理端需单独分类模块（如商品管理、订单审核），并描述其功能。  
2. **界面示意图**  
   - 管理端最终设计样式的截图或示意图（如后台管理界面）。  

#### **六、报告格式与附加内容**
1. **结构要求**  
   - 按“整体模块→用户端→管理端→优化美化”顺序编写。  
2. **附加优化内容**  
   - 对界面、交互等额外优化或美化的部分需单独说明。  
3. **配置信息表**  
   - 项目所用JAR包配置表（含名称、版本号），需写入报告。  

#### **七、扣分警告项**
- 缺少开发模式说明、接口文档、图表（用例图/流程图/实体类图等）、功能模块描述、代码结构截图、配置表等，均直接扣除对应分数。  
- 业务流程描述不完整或未按格式要求编写报告，酌情扣分。

---

### 总结  
考核重点围绕**系统设计的完整性**（40分）、**图表规范性**（用例图、流程图等）、**模块功能与接口的对应关系**，以及**报告的结构清晰度**。务必确保所有必选项（尤其是图表和开发模式）无遗漏，并按逻辑分层呈现。