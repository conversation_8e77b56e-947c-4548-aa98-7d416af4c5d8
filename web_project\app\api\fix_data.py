from flask import Blueprint, jsonify

# 创建蓝图
fix_data_bp = Blueprint('fix_data_api', __name__)

@fix_data_bp.route('/fix_bindings', methods=['POST'])
def fix_user_bindings():
    """修复用户绑定关系"""
    try:
        from app import db
        from app.models.models import ElderlyUser, FamilyUser
        
        print("🔧 开始修复用户绑定关系...")
        
        # 修复E01和F01的绑定
        elderly_e01 = ElderlyUser.query.filter_by(user_id='E01').first()
        family_f01 = FamilyUser.query.filter_by(family_id='F01').first()
        family_f02 = FamilyUser.query.filter_by(family_id='F02').first()
        
        if elderly_e01:
            elderly_e01.bound_family_ids = 'F01,F02'
            print(f"✅ 修复老人E01绑定关系: {elderly_e01.bound_family_ids}")
            
        if family_f01:
            family_f01.bound_elderly_id = 'E01'
            print(f"✅ 修复家属F01绑定关系: {family_f01.bound_elderly_id}")
            
        if family_f02:
            family_f02.bound_elderly_id = 'E01'
            print(f"✅ 修复家属F02绑定关系: {family_f02.bound_elderly_id}")
        
        # 修复E02和F03、F04的绑定
        elderly_e02 = ElderlyUser.query.filter_by(user_id='E02').first()
        family_f03 = FamilyUser.query.filter_by(family_id='F03').first()
        family_f04 = FamilyUser.query.filter_by(family_id='F04').first()
        
        if elderly_e02:
            elderly_e02.bound_family_ids = 'F03,F04'
            print(f"✅ 修复老人E02绑定关系: {elderly_e02.bound_family_ids}")
            
        if family_f03:
            family_f03.bound_elderly_id = 'E02'
            print(f"✅ 修复家属F03绑定关系: {family_f03.bound_elderly_id}")
            
        if family_f04:
            family_f04.bound_elderly_id = 'E02'
            print(f"✅ 修复家属F04绑定关系: {family_f04.bound_elderly_id}")
        
        db.session.commit()
        print("✅ 数据库提交成功！")
        
        return jsonify({
            'success': True,
            'message': '用户绑定关系修复成功',
            'fixed_users': {
                'E01': elderly_e01.bound_family_ids if elderly_e01 else None,
                'F01': family_f01.bound_elderly_id if family_f01 else None,
                'F02': family_f02.bound_elderly_id if family_f02 else None,
                'E02': elderly_e02.bound_family_ids if elderly_e02 else None,
                'F03': family_f03.bound_elderly_id if family_f03 else None,
                'F04': family_f04.bound_elderly_id if family_f04 else None
            }
        }), 200
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        return jsonify({'error': f'修复失败: {str(e)}'}), 500
