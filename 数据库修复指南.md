# 数据库修复指南

## 问题描述

健康科普文章无法加载，错误信息显示：
```
Table 'elderly_care.healtharticle' doesn't exist
```

## 问题原因

数据库表名大小写不匹配：
- **模型中的表名**：`healtharticle`（小写）
- **SQL脚本中的表名**：`HealthArticle`（首字母大写）

## 解决方案

### 方案1：执行重建脚本（推荐）

1. 打开MySQL客户端或phpMyAdmin
2. 选择 `elderly_care` 数据库
3. 执行 `rebuild_database.sql` 脚本

```sql
-- 在MySQL中执行
source rebuild_database.sql;
```

### 方案2：手动创建表

如果方案1不可行，可以手动执行以下SQL：

```sql
USE elderly_care;

-- 创建healtharticle表
CREATE TABLE IF NOT EXISTS healtharticle (
    article_id VARCHAR(10) PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    category VARCHAR(20) NOT NULL,
    publish_time DATE NOT NULL,
    favorited_by_users VARCHAR(200),
    image_url VARCHAR(200),
    read_count INT DEFAULT 0,
    difficulty_level INT DEFAULT 1
);

-- 插入测试数据
INSERT INTO healtharticle (article_id, title, content, category, publish_time, favorited_by_users, image_url, read_count, difficulty_level) VALUES
('A001', '老年人如何预防跌倒', '跌倒是老年人常见的意外伤害...', '养生', '2025-04-30', 'E01,E03', '/static/images/articles/fall_prevention.jpg', 156, 1),
('A002', '突发心梗的急救处理', '心肌梗死是心血管疾病的急重症...', '急救', '2025-04-30', 'E02,E05', '/static/images/articles/heart_attack.jpg', 203, 2);
-- ... 更多数据请参考rebuild_database.sql
```

### 方案3：使用完整数据库脚本

执行更新后的 `elderly_care_complete.sql` 脚本：

```bash
mysql -u root -p elderly_care < elderly_care_complete.sql
```

## 验证修复

### 1. 检查表是否存在

```sql
USE elderly_care;
SHOW TABLES LIKE 'healtharticle';
```

### 2. 检查表结构

```sql
DESCRIBE healtharticle;
```

### 3. 检查数据

```sql
SELECT COUNT(*) FROM healtharticle;
SELECT article_id, title, category FROM healtharticle LIMIT 5;
```

### 4. 测试API

访问以下URL测试API是否正常：
- `http://localhost:5000/api/health/test`
- `http://localhost:5000/api/health/articles`

## 预期结果

修复成功后，您应该看到：

1. **数据库中有10篇健康科普文章**
2. **API返回正常的JSON数据**
3. **前端页面能够正常显示文章列表**

## 常见问题

### Q1: 执行脚本时出现权限错误
**A**: 确保使用具有CREATE、DROP、INSERT权限的数据库用户

### Q2: 表已存在但仍然报错
**A**: 可能是表名大小写问题，执行以下命令检查：
```sql
SHOW CREATE TABLE healtharticle;
```

### Q3: 数据插入失败
**A**: 检查字符编码，确保数据库使用UTF8编码：
```sql
ALTER DATABASE elderly_care CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

## 后续建议

1. **统一命名规范**：建议所有表名使用小写，与Python模型保持一致
2. **备份数据库**：在进行重大修改前，先备份数据库
3. **测试环境**：在生产环境应用前，先在测试环境验证

## 联系支持

如果按照以上步骤仍无法解决问题，请提供：
1. MySQL版本信息
2. 错误日志详细信息
3. 数据库表列表（`SHOW TABLES;`）
