const emergencyCallModule = {
    data: {
        emergencyModalInstance: null,
        emergencyStatus: '',
        currentLocation: null,
        emergencyType: null,
        countdownTimer: 10,
        emergencyMap: null,
        countdownInterval: null,
        buzzerAudio: null,
    },
    methods: {
        showEmergencyModal() {
            // Reset status
            this.emergencyType = null;
            this.emergencyStatus = '';

            if (this.emergencyModalInstance) {
                this.emergencyModalInstance.show();
            } else {
                // Fallback if instance somehow wasn't created during mounted
                const modalEl = document.getElementById('emergencyModal');
                if (modalEl) {
                    console.warn("Emergency modal instance was not ready, creating now.");
                    this.emergencyModalInstance = new bootstrap.Modal(modalEl);
                     // Re-attach listener if we are creating it here, though this path should ideally not be hit.
                    modalEl.addEventListener('hidden.bs.modal', () => {
                        this.stopBuzzer();
                        this.stopCountdown();
                        this.emergencyStatus = '';
                    });
                    this.emergencyModalInstance.show();
                } else {
                    console.error("Emergency modal element not found.");
                }
            }
        },
        initEmergencyMap(containerId = 'emergencyMap', showRescue = false) {
            // 这里使用已有的高德地图API
            try {
                const container = document.getElementById(containerId);
                if (!container) return;

                // 清除现有地图
                if (this.emergencyMap) {
                    this.emergencyMap.destroy();
                }

                // 创建地图实例
                const map = new AMap.Map(containerId, {
                    zoom: 15,
                    center: [this.currentLocation?.lng || 116.4074, this.currentLocation?.lat || 39.9042]
                });

                // 添加标记
                const marker = new AMap.Marker({
                    position: [this.currentLocation?.lng || 116.4074, this.currentLocation?.lat || 39.9042],
                    title: '当前位置'
                });

                map.add(marker);

                // 添加定位圆圈
                const circle = new AMap.Circle({
                    center: [this.currentLocation?.lng || 116.4074, this.currentLocation?.lat || 39.9042],
                    radius: 50,
                    fillColor: 'rgba(255,77,79,0.2)',
                    strokeColor: '#FF4D4F',
                    strokeWeight: 2
                });

                map.add(circle);

                // 如果显示救援信息，添加救援路线
                if (showRescue || this.emergencyStatus === 'connected') {
                    // 模拟救援路线
                    const path = [
                        [this.currentLocation?.lng || 116.4074, this.currentLocation?.lat || 39.9042],
                        [(this.currentLocation?.lng || 116.4074) + 0.005, (this.currentLocation?.lat || 39.9042) + 0.002],
                        [(this.currentLocation?.lng || 116.4074) + 0.008, (this.currentLocation?.lat || 39.9042) - 0.001]
                    ];

                    const polyline = new AMap.Polyline({
                        path: path,
                        strokeColor: '#1890FF',
                        strokeWeight: 5,
                        strokeStyle: 'solid'
                    });

                    map.add(polyline);

                    // 救援人员位置标记
                    const rescuerMarker = new AMap.Marker({
                        position: path[path.length - 1],
                        title: '救援人员',
                        content: '<div style="background-color: #1890FF; color: white; padding: 5px 10px; border-radius: 50%; box-shadow: 0 0 10px rgba(0,0,0,0.2);"><i class="bi bi-truck"></i></div>'
                    });

                    map.add(rescuerMarker);
                }

                this.emergencyMap = map;
            } catch (error) {
                console.error('初始化地图失败:', error);
            }
        },
        getAddress(lng, lat) {
            // 使用高德地图API进行逆地理编码
            if (typeof AMap !== 'undefined') {
                AMap.plugin('AMap.Geocoder', () => {
                    const geocoder = new AMap.Geocoder();
                    geocoder.getAddress([lng, lat], (status, result) => {
                        if (status === 'complete' && result.info === 'OK') {
                            const address = result.regeocode.formattedAddress;
                            document.getElementById('current-address').textContent = address;

                            // 保存地址信息用于发送
                            this.currentLocation = {
                                address: address,
                                lng: lng,
                                lat: lat
                            };
                        } else {
                            document.getElementById('current-address').textContent = '地址解析失败';
                        }
                    });
                });
            }
        },
        selectEmergencyType(type) {
            // 设置紧急情况类型
            let statusText = '';
            switch(type) {
                case 'medical':
                    statusText = '医疗紧急情况已报告，正在联系医疗人员...';
                    break;
                case 'fall':
                    statusText = '跌倒求助信息已发送，正在联系附近救援人员...';
                    break;
                case 'help':
                    statusText = '求助信息已发送，社区工作人员将尽快联系您...';
                    break;
            }
            this.emergencyStatus = statusText;
        },
        async startEmergencyCall() {
            if (!this.emergencyType) return;

            // 开始蜂鸣音效
            this.startBuzzer();

            // 更新状态
            this.emergencyStatus = 'calling';

            // 重置倒计时
            this.countdownTimer = 10;

            // 开始倒计时
            this.startCountdown();

            // 获取位置
            await this.getLocation();

            // 初始化地图
            this.$nextTick(() => {
                this.initEmergencyMap();
            });

            // 模拟3秒后连接成功
            setTimeout(async () => {
                if (this.emergencyStatus === 'calling') { // Ensure call wasn't cancelled
                    this.emergencyStatus = 'connected';
                    this.stopCountdown();

                    // 发送紧急呼叫数据到后端
                    await this.sendEmergencyCallToServer();

                    // 更新地图
                    this.$nextTick(() => {
                        // 更新同一个地图来显示救援路线
                        this.initEmergencyMap('emergencyMap', true);
                    });
                }
            }, 5000);
        },

        // 新增方法：发送紧急呼叫数据到后端
        async sendEmergencyCallToServer() {
            try {
                // 准备发送的数据
                const emergencyData = {
                    elderly_id: 'E001', // 模拟老年人ID，实际应用中应该从登录状态获取
                    elderly_name: '张建国', // 模拟老年人姓名
                    emergency_type: this.emergencyType,
                    location: this.currentLocation,
                    timestamp: new Date().toISOString()
                };

                console.log('发送紧急呼叫数据:', emergencyData);

                // 发送到后端API
                const response = await fetch('/api/emergency/event/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(emergencyData)
                });

                if (response.ok) {
                    const result = await response.json();
                    console.log('紧急呼叫发送成功:', result);
                } else {
                    console.error('紧急呼叫发送失败:', response.status);
                }
            } catch (error) {
                console.error('发送紧急呼叫时出错:', error);
            }
        },
        cancelEmergencyCall() {
            this.stopBuzzer();
            this.stopCountdown();
            this.emergencyStatus = '';

            if (this.emergencyModalInstance) {
                this.emergencyModalInstance.hide();
            }
        },
        startBuzzer() {
            if (this.buzzerAudio) {
                this.buzzerAudio.play().catch(e => console.error('无法播放音频:', e));
            }
        },
        stopBuzzer() {
            if (this.buzzerAudio) {
                this.buzzerAudio.pause();
                this.buzzerAudio.currentTime = 0;
            }
        },
        startCountdown() {
            this.stopCountdown(); // 确保先停止之前的倒计时

            this.countdownInterval = setInterval(() => {
                if (this.countdownTimer > 1) {
                    this.countdownTimer--;
                } else {
                    this.stopCountdown();
                }
            }, 1000);
        },
        stopCountdown() {
            if (this.countdownInterval) {
                clearInterval(this.countdownInterval);
                this.countdownInterval = null;
            }
        },
        async getLocation() {
            try {
                // 模拟获取位置信息
                // 实际应用中应使用浏览器的定位API
                await new Promise(resolve => setTimeout(resolve, 1500));
                this.currentLocation = {
                    lat: 30.2741,    // Changed to Hangzhou's approximate latitude
                    lng: 120.1551,   // Changed to Hangzhou's approximate longitude
                    address: '杭州市西湖区西溪路' // Address remains Hangzhou
                };
                return this.currentLocation;
            } catch (error) {
                console.error('获取位置失败:', error);
                this.currentLocation = {
                    lat: 30.2741,    // Fallback to Hangzhou's latitude
                    lng: 120.1551,   // Fallback to Hangzhou's longitude
                    address: '杭州市西湖区西溪路' // Default location address
                };
                return this.currentLocation;
            }
        },
    },
    // mounted 钩子中的初始化逻辑将通过一个单独的函数暴露出去
    initEmergencyFeatures(vueInstance) {
        // 初始化紧急呼叫音频
        vueInstance.buzzerAudio = document.getElementById('emergencyAudio');

        // 监听模态窗口事件
        const emergencyModalEl = document.getElementById('emergencyModal');
        if (emergencyModalEl) {
            vueInstance.emergencyModalInstance = new bootstrap.Modal(emergencyModalEl);
            emergencyModalEl.addEventListener('hidden.bs.modal', () => {
                // 模态窗口关闭时停止蜂鸣音效
                vueInstance.stopBuzzer();
                vueInstance.stopCountdown();
                vueInstance.emergencyStatus = '';
            });
        }
    }
};