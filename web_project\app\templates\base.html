<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}居家养老健康管理系统{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap-icons/1.10.0/font/bootstrap-icons.min.css" rel="stylesheet">
    <!-- 高德地图API -->
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=3ba2467a72df9bf471cbcbde0ebd9ea7"></script>
    <!-- Vue.js -->
    <script src="https://cdn.bootcdn.net/ajax/libs/vue/2.6.14/vue.min.js"></script>
    <!-- Axios -->
    <script src="https://cdn.bootcdn.net/ajax/libs/axios/1.4.0/axios.min.js"></script>

    <!-- 全局自定义样式 -->
    <style>
        :root {
            --primary-color: #164B87;
            --primary-light: #3A84C3;
            --success-color: #6DD400;
            --danger-color: #FF4D4F;
            --warning-color: #FAAD14;
            --info-color: #73D8FF;
            --heading-font: "Microsoft YaHei", sans-serif;
            --body-font: "Microsoft YaHei Light", sans-serif;
        }

        body {
            font-family: var(--body-font);
            background-color: #f8f9fa;
            color: #333;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            margin: 0;
            padding: 0;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: var(--heading-font);
            font-weight: 600;
        }

        /* 导航栏样式 */
        .main-navbar {
            background: linear-gradient(to right, #164B87, #3A84C3);
            padding: 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: bold;
            display: flex;
            align-items: center;
            color: white !important;
            padding: 15px 0;
        }

        .navbar-brand i {
            font-size: 2.2rem;
            margin-right: 15px;
            margin-left: -10px;
        }

        .navbar-brand span {
            font-size: 1.5rem;
            letter-spacing: 0.5px;
        }

        .main-navbar .nav-link {
            color: rgba(255, 255, 255, 0.85) !important;
            padding: 20px 15px;
            position: relative;
            font-weight: 500;
            transition: all 0.3s;
        }

        .main-navbar .nav-link:hover {
            color: white !important;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .main-navbar .nav-link.active {
            color: white !important;
            background-color: rgba(255, 255, 255, 0.15);
            box-shadow: inset 0 -3px 0 white;
        }

        .main-navbar .nav-link i {
            margin-right: 6px;
            font-size: 1.1rem;
        }

        .navbar-toggler {
            border: none;
            color: white;
        }

        .user-dropdown .dropdown-menu {
            border: none;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .user-dropdown .dropdown-item {
            padding: 10px 15px;
            transition: all 0.2s;
        }

        .user-dropdown .dropdown-item:hover {
            background-color: #f0f7ff;
            color: var(--primary-color);
        }

        .main-content {
            flex: 1;
            padding: 0;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }

        .btn-danger {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
        }

        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        /* 页脚样式 */
        .app-footer {
            background-color: #164B87;
            color: #fff;
            padding: 50px 0 20px;
            margin-top: auto;
        }

        .app-footer h5 {
            font-size: 1.25rem;
            font-weight: 600;
            color: white;
            position: relative;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }

        .app-footer h5:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 3px;
            background-color: #3A84C3;
        }

        .app-footer p,
        .app-footer li {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.8;
        }

        .app-footer a {
            color: rgba(255, 255, 255, 0.8);
            transition: all 0.3s;
        }

        .app-footer a:hover {
            color: white;
            text-decoration: none;
            padding-left: 5px;
        }

        .footer-contact i {
            width: 28px;
            height: 28px;
            background-color: rgba(255, 255, 255, 0.1);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            margin-right: 10px;
            color: white;
        }

        .footer-bottom {
            background-color: rgba(0, 0, 0, 0.1);
            padding: 15px 0;
            margin-top: 30px;
        }




    </style>

    {% block head %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark main-navbar">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('main.index') }}">
                <i class="bi bi-heart-pulse-fill"></i>
                <span>银龄守护</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.index') }}">
                            <i class="bi bi-house-fill"></i> 首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.elderly_center') }}">
                            <i class="bi bi-person-fill"></i> 老人中心
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.family_center') }}">
                            <i class="bi bi-people-fill"></i> 家属监护
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.worker_center') }}">
                            <i class="bi bi-person-badge-fill"></i> 社区管理
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item me-2">
                        <a class="nav-link theme-toggle" href="#" id="theme-toggle">
                            <i class="bi bi-moon-fill"></i>
                        </a>
                    </li>
                    <li class="nav-item dropdown user-dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i>
                            <span v-if="currentUser">[[ currentUser.name ]]</span>
                            <span v-else>用户</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <template v-if="currentUser">
                                <template v-if="currentUser.type === 'family' && elderlyList && elderlyList.length > 0">
                                    <li><h6 class="dropdown-header">监护的老人</h6></li>
                                    <li v-for="elderly in elderlyList" :key="elderly.user_id">
                                        <a class="dropdown-item" href="#" @click.prevent="switchElderly(elderly.user_id)">
                                            <i class="bi bi-people-fill me-2" :class="{'text-primary': elderly.is_primary}"></i>
                                            [[ elderly.name ]]
                                            <span v-if="elderly.is_primary" class="ms-auto badge bg-primary">当前</span>
                                        </a>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                </template>

                                <li><a class="dropdown-item" href="#">
                                    <i class="bi bi-person me-2 text-primary"></i> 个人资料
                                </a></li>
                                <li><a class="dropdown-item" href="#">
                                    <i class="bi bi-gear me-2 text-secondary"></i> 设置
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" @click.prevent="logout">
                                    <i class="bi bi-box-arrow-right me-2 text-danger"></i> 退出登录
                                </a></li>
                            </template>
                            <template v-else>
                                <li><a class="dropdown-item" href="/login">
                                    <i class="bi bi-box-arrow-in-right me-2"></i> 登录
                                </a></li>
                                <li><a class="dropdown-item" href="/register">
                                    <i class="bi bi-person-plus me-2"></i> 注册
                                </a></li>
                            </template>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主内容 -->
    <div class="main-content">
        {% block content %}{% endblock %}
    </div>

    <!-- 页脚 -->
    {% block footer %}
    <footer class="app-footer">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-4 mb-md-0">
                    <h5>关于我们</h5>
                    <p>银龄守护平台致力于为老年人提供全方位的健康管理服务，让家人安心，让老人舒心。我们用科技守护每一位长者的健康生活。</p>
                </div>
                <div class="col-md-4 mb-4 mb-md-0">
                    <h5>快速链接</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="{{ url_for('main.index') }}" class="text-decoration-none">
                            <i class="bi bi-chevron-right"></i> 首页
                        </a></li>
                        <li class="mb-2"><a href="{{ url_for('main.elderly_center') }}" class="text-decoration-none">
                            <i class="bi bi-chevron-right"></i> 老人中心
                        </a></li>
                        <li class="mb-2"><a href="{{ url_for('main.family_center') }}" class="text-decoration-none">
                            <i class="bi bi-chevron-right"></i> 家属监护
                        </a></li>
                        <li class="mb-2"><a href="{{ url_for('main.worker_center') }}" class="text-decoration-none">
                            <i class="bi bi-chevron-right"></i> 社区管理
                        </a></li>
                    </ul>
                </div>
                <div class="col-md-4 footer-contact">
                    <h5>联系我们</h5>
                    <p><i class="bi bi-geo-alt-fill"></i> 浙江省杭州市余杭区仓前街道余杭塘路2318号</p>
                    <p><i class="bi bi-telephone-fill"></i> ************</p>
                    <p><i class="bi bi-envelope-fill"></i> <EMAIL></p>
                    <div class="mt-4">
                        <a href="#" class="me-3"><i class="bi bi-wechat fs-4"></i></a>
                        <a href="#" class="me-3"><i class="bi bi-chat-dots fs-4"></i></a>
                        <a href="#"><i class="bi bi-question-circle fs-4"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom text-center">
                <p class="mb-0">© 2025 银龄守护平台 | 浙ICP备12345678号 | 浙公网安备11010502030123号</p>
            </div>
        </div>
    </footer>
    {% endblock %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>

    <!-- 全局JS -->
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script>
        // 创建Vue实例
        var vueNavbar = new Vue({
            el: '#navbarNav',
            data: {
                currentUser: null,
                elderlyList: []
            },
            delimiters: ['[[', ']]'],
            mounted() {
                // 获取当前用户信息
                this.getCurrentUser();
            },
            methods: {
                getCurrentUser() {
                    axios.get('/api/auth/current_user')
                        .then(response => {
                            if (response.data.logged_in) {
                                this.currentUser = {
                                    id: response.data.user_id,
                                    name: response.data.name,
                                    type: response.data.user_type
                                };
                                if (this.currentUser.type === 'family') {
                                    this.getElderlyList();
                                }
                            } else {
                                this.currentUser = null;
                            }
                        })
                        .catch(error => {
                            console.error('获取用户信息失败:', error);
                        });
                },
                logout() {
                    axios.post('/api/auth/logout')
                        .then(response => {
                            if (response.data.success) {
                                window.location.href = response.data.redirect_url;
                            }
                        })
                        .catch(error => {
                            console.error('退出登录失败:', error);
                            alert('退出登录失败，请重试');
                        });
                },
                getElderlyList() {
                    if (this.currentUser && this.currentUser.type === 'family') {
                        axios.get(`/api/family/bound_elderly_list/${this.currentUser.id}`)
                            .then(response => {
                                if (response.data.success) {
                                    this.elderlyList = response.data.elderly_list;
                                }
                            })
                            .catch(error => {
                                console.error('获取绑定老人列表失败:', error);
                            });
                    }
                },
                updateElderlyList(list) {
                    // 用于从其他Vue实例更新老人列表的方法
                    if (Array.isArray(list)) {
                        this.elderlyList = list;
                    }
                },
                switchElderly(elderlyId) {
                    // 获取要切换的老人信息
                    const elderly = this.elderlyList.find(e => e.user_id === elderlyId);
                    if (elderly) {
                        // 更新主绑定老人
                        axios.post('/api/family/bind', {
                            family_id: this.currentUser.id,
                            elderly_id: elderlyId
                        })
                        .then(response => {
                            if (response.data.success) {
                                // 重新加载老人列表以更新状态
                                this.getElderlyList();

                                // 如果在家属中心页面，通知更新
                                if(window.location.pathname.includes('/family')) {
                                    // 刷新页面以更新内容
                                    window.location.reload();
                                }
                            }
                        })
                        .catch(error => {
                            console.error('切换老人失败:', error);
                            alert('切换老人失败，请重试');
                        });
                    }
                }
            }
        });

        // 将Vue实例保存到全局变量，方便其他Vue实例访问
        window.vueNavbar = vueNavbar;
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>