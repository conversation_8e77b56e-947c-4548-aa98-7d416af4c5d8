1-3主题3：居家养老健康管理网页端
随着人口老龄化加剧，居家养老服务的需求日益增长。该应用旨在为老年人提供健康管理服务，包括健康信息检测、运动记录、老年食堂订餐等功能，同时方便家属和社区工作人员进行监护和管理。功能需求如下：
?用户注册与登录：老年人或其家属可注册账号，登录后查看健康信息、运动情况、睡眠情况、饮食情况等。
?健康档案管理：能方便记录老年人的基本健康信息，如血压、血糖、病史等。
?健康监测：从数据库动态获取由智能健康设备（如智能手环、血压计等）实时上传的健康数据，并生成可视化健康报告。
?健康建议与提醒：根据健康数据，系统自动推送健康建议和用药提醒。
?订餐服务：提供订餐、送餐服务，模拟在线支付。
?紧急呼叫与定位：集成紧急呼叫功能，老年人遇到突发状况时可一键求助，并通过GPS定位提供位置信息。
?家属监护：家属可通过系统查看老年人的健康数据和活动轨迹，接收紧急通知。
?社区服务管理：社区工作人员可所管辖范围内拉人的订餐送餐等信息，安排服务人员上门。
?健康科普：发布健康知识、养生文章等，老年人可在应用中浏览和收藏。

2 开发技术说明
此为本学期《Web程序设计》课程的期末考核，考核范围为一学期所学知识，设计需要涵盖前端和服务端功能的设计开发，开发技术为课程所学技术，还需支持基于Web的数据库存取，鼓励用AI插件协助开发和在设计中嵌入基于AI的应用。
?前端：HTML、CSS、JavaScript、Bootstrap、jQuery、Vue.js框架
?服务端：Flask框架
?数据库：SQLite、MySQL等
?第三方插件/Web API：需在设计书中说明