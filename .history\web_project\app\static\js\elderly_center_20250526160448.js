// 创建Vue应用
const app = new Vue({
    el: '#elderly-app',
    delimiters: ['${', '}$'], // 更改Vue默认的双花括号语法，避免与Jinja2冲突
    data: {
        navCollapsed: false,
        currentTab: 'health',
        showAssistantModal: false,
        timelineCollapsed: false,
        // 订餐系统相关数据
        cart: {
            items: [],
            totalPrice: 0
        },
        currentFood: null,
        foodDetailModalInstance: null,
        paymentModalInstance: null,
        // 紧急呼叫相关数据
        emergencyModalInstance: null,
        emergencyStatus: '',
        currentLocation: null,
        emergencyType: null,
        countdownTimer: 10,
        emergencyMap: null,
        countdownInterval: null,
        buzzerAudio: null,
        // 其他数据...
        foodList: [
            {
                id: 1,
                name: "清蒸鲈鱼",
                price: 28,
                image: "/static/images/fish.png",
                tags: ["高蛋白", "低脂"],
                info: "适合高血压人群",
                badge: "医生推荐",
                badgeClass: "bg-success",
                nutrition: {
                    protein: 20,
                    fat: 5,
                    carbs: 10,
                    salt: 2
                },
                benefits: [
                    "富含DHA，有助于心脑血管健康",
                    "清蒸烹饪法，低脂健康",
                    "优质蛋白质来源，易于消化吸收"
                ]
            },
            {
                id: 2,
                name: "五谷杂粮粥",
                price: 15,
                image: "/static/images/wugu.png",
                tags: ["高纤维", "低糖"],
                info: "促进肠道健康",
                badge: "营养均衡",
                badgeClass: "bg-primary",
                nutrition: {
                    protein: 8,
                    fat: 2,
                    carbs: 30,
                    salt: 1
                },
                benefits: [
                    "富含膳食纤维，促进肠道蠕动",
                    "多种谷物营养均衡",
                    "低糖设计，适合血糖控制"
                ]
            },
            {
                id: 3,
                name: "西兰花炒牛肉",
                price: 25,
                image: "/static/images/beaf.png",
                tags: ["高蛋白", "维生素"],
                info: "增强免疫力",
                nutrition: {
                    protein: 18,
                    fat: 8,
                    carbs: 15,
                    salt: 2.5
                },
                benefits: [
                    "牛肉富含优质蛋白和铁质",
                    "西兰花含丰富维生素C和抗氧化物",
                    "搭配科学，营养均衡"
                ]
            },
            {
                id: 4,
                name: "三文鱼沙拉",
                price: 32,
                image: "/static/images/sala.png",
                tags: ["优质脂肪", "维生素"],
                info: "心脑血管健康",
                nutrition: {
                    protein: 15,
                    fat: 12,
                    carbs: 10,
                    salt: 1.5
                },
                benefits: [
                    "富含优质Omega-3脂肪酸",
                    "新鲜蔬菜提供充足维生素",
                    "有助于降低胆固醇，保护心血管健康"
                ]
            },
            {
                id: 5,
                name: "山药排骨汤",
                price: 36,
                image: "/static/images/shanyao.png",
                tags: ["滋补", "易消化"],
                info: "健脾益胃",
                badge: "老年养生",
                badgeClass: "bg-warning",
                nutrition: {
                    protein: 18,
                    fat: 10,
                    carbs: 25,
                    salt: 2.5
                },
                benefits: [
                    "含丰富的维生素和矿物质",
                    "山药易于消化吸收，养胃健脾",
                    "排骨提供丰富蛋白质和钙质"
                ]
            },
            {
                id: 6,
                name: "清蒸南瓜",
                price: 18,
                image: "/static/images/nangua.png",
                tags: ["高纤维", "低糖"],
                info: "适合糖尿病人",
                badge: "低糖食谱",
                badgeClass: "bg-info",
                nutrition: {
                    protein: 5,
                    fat: 1,
                    carbs: 20,
                    salt: 0.5
                },
                benefits: [
                    "低糖低脂，适合糖尿病患者",
                    "富含胡萝卜素和膳食纤维",
                    "帮助控制血糖，促进肠道健康"
                ]
            }
        ],
        // 健康科普相关数据
        selectedCategory: 'all',
        articles: [],
        filteredArticles: [],
        articlesLoading: false,
        currentPage: 1,
        totalPages: 1,
        perPage: 8,
        selectedArticle: {},
        isArticleFavorited: false,
        articleExpanded: false,
        showFavorites: false,

        // 搜索相关数据
        searchKeyword: '',

        // 收藏相关数据
        favoriteArticles: [],
        ...foodOrderModule.data, // 合并订餐模块的data
        ...emergencyCallModule.data // 合并紧急呼叫模块的data
    },
    watch: {
    },
    mounted() {
        // 初始化紧急呼叫相关功能
        emergencyCallModule.initEmergencyFeatures(this);
        // 初始化订餐相关功能
        foodOrderModule.initFoodOrderingFeatures(this);

        // 将foodOrderModule设为全局可访问
        window.foodOrderManager = foodOrderModule;

        // 初始化当前标签页的图表
        this.initHeartRateGauge();
        // 初始化新增的图表
        this.initStepsChart();
        this.initSleepChart();
        this.initNutritionChart();

        // 初始化健康科普文章
        this.loadHealthArticles();

        // 初始化收藏数据
        this.loadFavoriteArticles();
    },
    methods: {
        // ... foodOrderModule.methods 将在这里合并 ...
        // ... emergencyCallModule.methods 已经合并 ...
        toggleNav() {
            this.navCollapsed = !this.navCollapsed;
        },
        changeTab(tab) {
            this.currentTab = tab;
            // 切换到健康看板时初始化心率图表
            if (tab === 'health') {
                this.$nextTick(() => {
                    this.initHeartRateGauge();
                    this.initStepsChart();
                    this.initSleepChart();
                    this.initNutritionChart();
                });
            }
            // 切换到个人档案时初始化健康图表
            else if (tab === 'profile') {
                this.$nextTick(() => {
                    this.initBloodPressureChart();
                    this.initBloodSugarChart();
                });
            }
            // 切换到健康科普时加载文章
            else if (tab === 'health_edu') {
                this.$nextTick(() => {
                    this.loadHealthArticles();
                });
            }
            // 切换到服务标签页时，确保相关模态框已初始化 (这部分现在由 initFoodOrderingFeatures 处理)
            // else if (tab === 'services') {
            //     this.$nextTick(() => {
            //         // Initialize foodDetailModal if not already done
            //         if (!this.foodDetailModalInstance) {
            //             const foodDetailModalEl = document.getElementById('foodDetailModal');
            //             if (foodDetailModalEl) {
            //                 this.foodDetailModalInstance = new bootstrap.Modal(foodDetailModalEl);
            //             } else {
            //                 console.error("DOM element #foodDetailModal not found when 'services' tab activated.");
            //             }
            //         }
            //         // Initialize paymentModal if not already done
            //         if (!this.paymentModalInstance) {
            //             const paymentModalEl = document.getElementById('paymentModal');
            //             if (paymentModalEl) {
            //                 this.paymentModalInstance = new bootstrap.Modal(paymentModalEl);
            //             } else {
            //                 console.error("DOM element #paymentModal not found when 'services' tab activated.");
            //             }
            //         }
            //     });
            // }
        },
        // 订餐系统方法
        addToCart(foodId) {
            const food = this.foodList.find(f => f.id === foodId);
            if (!food) return;

            // 查找购物车中是否已有该商品
            const existingItem = this.cart.items.find(item => item.id === foodId);

            if (existingItem) {
                // 如果已存在，数量+1
                existingItem.quantity += 1;
            } else {
                // 如果不存在，添加到购物车
                this.cart.items.push({
                    id: food.id,
                    name: food.name,
                    price: food.price,
                    image: food.image,
                    quantity: 1,
                    tags: food.tags
                });
            }

            // 更新总价
            this.updateCartTotal();

            // 直接打开食品详情模态窗口
            this.showFoodDetail(foodId);
        },

        updateCartTotal() {
            this.cart.totalPrice = this.cart.items.reduce((total, item) => {
                return total + (item.price * item.quantity);
            }, 0);
        },

        showFoodDetail(foodId) {
            // 设置当前食品
            this.currentFood = this.foodList.find(f => f.id === foodId);

            // 使用预初始化的Bootstrap modal实例打开模态窗口
            if (this.foodDetailModalInstance) {
                this.foodDetailModalInstance.show();
            } else {
                console.error("foodDetailModalInstance not initialized. Modal cannot be shown.");
            }
        },

        increaseQuantity(itemId) {
            const item = this.cart.items.find(i => i.id === itemId);
            if (item) {
                item.quantity += 1;
                this.updateCartTotal();
            }
        },

        decreaseQuantity(itemId) {
            const item = this.cart.items.find(i => i.id === itemId);
            if (item && item.quantity > 1) {
                item.quantity -= 1;
                this.updateCartTotal();
            } else if (item && item.quantity === 1) {
                // 如果数量为1，从购物车中移除
                this.removeFromCart(itemId);
            }
        },

        removeFromCart(itemId) {
            const index = this.cart.items.findIndex(i => i.id === itemId);
            if (index !== -1) {
                this.cart.items.splice(index, 1);
                this.updateCartTotal();
            }
        },

        checkout() {
            // 显示支付确认模态窗口
            if (this.paymentModalInstance) {
                this.paymentModalInstance.show();
            } else {
                console.error("paymentModalInstance not initialized. Payment modal cannot be shown.");
            }

            // 隐藏菜品详情模态窗口
            if (this.foodDetailModalInstance) {
                this.foodDetailModalInstance.hide();
            }
        },

        confirmPayment() {
            // 这里可以添加支付逻辑
            alert('订单已成功提交！');
            // 清空购物车
            this.cart.items = [];
            this.updateCartTotal();

            // 隐藏支付模态窗口
            if (this.paymentModalInstance) {
                this.paymentModalInstance.hide();
            } else {
                console.error("paymentModalInstance not initialized. Payment modal cannot be hidden.");
            }
        },

        // 显示我的订单
        showMyOrders() {
            if (window.foodOrderManager && window.foodOrderManager.showMyOrders) {
                window.foodOrderManager.showMyOrders();
            } else {
                console.error('foodOrderManager not available');
            }
        },

        initHeartRateGauge() {
            // 简单模拟心率图表
            const ctx = document.getElementById('heartRateGauge');
            if (ctx) {
                // 绘制简单的心率仪表盘
                const gradient = ctx.getContext('2d').createLinearGradient(0, 0, 0, 400);
                gradient.addColorStop(0, '#5B8FF9');
                gradient.addColorStop(1, '#5AD8A6');

                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        datasets: [{
                            data: [72, 28],
                            backgroundColor: [gradient, 'rgba(0,0,0,0.05)'],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        cutout: '75%',
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                enabled: false
                            }
                        }
                    }
                });
            }

            // 初始化血压波动曲线图表
            const fluctuationCtx = document.getElementById('bloodPressureFluctuation');
            if (fluctuationCtx) {
                new Chart(fluctuationCtx, {
                    type: 'line',
                    data: {
                        labels: ['08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00'],
                        datasets: [
                            {
                                label: '收缩压',
                                data: [125, 130, 128, 145, 153, 138, 128],
                                borderColor: '#FF4D4F',
                                backgroundColor: 'rgba(255, 77, 79, 0.1)',
                                tension: 0.3,
                                fill: false,
                                borderWidth: 2,
                                pointBackgroundColor: function(context) {
                                    var index = context.dataIndex;
                                    var value = context.dataset.data[index];
                                    return value > 140 ? '#FF4D4F' : '#5B8FF9';
                                },
                                pointRadius: function(context) {
                                    var index = context.dataIndex;
                                    var value = context.dataset.data[index];
                                    return value > 140 ? 5 : 3;
                                },
                                segment: {
                                    borderColor: function(context) {
                                        return context.p1.parsed.y > 140 ? '#FF4D4F' : '#5B8FF9';
                                    }
                                }
                            },
                            {
                                label: '舒张压',
                                data: [80, 82, 85, 88, 95, 87, 82],
                                borderColor: '#5AD8A6',
                                backgroundColor: 'rgba(90, 216, 166, 0.1)',
                                tension: 0.3,
                                fill: false,
                                borderWidth: 2
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: false,
                                min: 70,
                                max: 160,
                                grid: {
                                    drawBorder: false
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            }

            // 初始化最近三次测量对比图表
            const comparisonCtx = document.getElementById('comparisionChart');
            if (comparisonCtx) {
                new Chart(comparisonCtx, {
                    type: 'bar',
                    data: {
                        labels: ['今天上午', '昨天下午', '昨天上午'],
                        datasets: [
                            {
                                label: '收缩压',
                                data: [128, 142, 135],
                                backgroundColor: function(context) {
                                    var index = context.dataIndex;
                                    var value = context.dataset.data[index];
                                    return value > 140 ? 'rgba(255, 77, 79, 0.7)' : 'rgba(91, 143, 249, 0.7)';
                                },
                                borderWidth: 0,
                                barPercentage: 0.7
                            },
                            {
                                label: '舒张压',
                                data: [85, 92, 88],
                                backgroundColor: 'rgba(90, 216, 166, 0.7)',
                                borderWidth: 0,
                                barPercentage: 0.7
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: false,
                                min: 60,
                                max: 150,
                                grid: {
                                    drawBorder: false
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            }
        },
        // 新增：初始化步数图表
        initStepsChart() {
            const ctx = document.getElementById('stepsProgressChart');
            if (ctx) {
                const progress = (5246 / 8000) * 100; // 计算完成百分比

                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        datasets: [{
                            data: [progress, 100 - progress],
                            backgroundColor: ['#36A2EB', '#f8f9fa'],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        cutout: '75%',
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                enabled: false
                            }
                        }
                    }
                });
            }
        },
        // 新增：初始化睡眠图表
        initSleepChart() {
            const ctx = document.getElementById('sleepPhaseChart');
            if (ctx) {
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['22:00', '23:00', '00:00', '01:00', '02:00', '03:00', '04:00', '05:00', '06:00'],
                        datasets: [{
                            label: '睡眠阶段',
                            data: [1, 2, 3, 4, 3, 4, 3, 2, 1],
                            backgroundColor: [
                                'rgba(153, 102, 255, 0.5)', // 浅睡
                                'rgba(153, 102, 255, 0.7)', // 浅睡
                                'rgba(54, 162, 235, 0.7)',  // 深睡
                                'rgba(54, 162, 235, 0.9)',  // 深睡
                                'rgba(54, 162, 235, 0.7)',  // 深睡
                                'rgba(54, 162, 235, 0.9)',  // 深睡
                                'rgba(54, 162, 235, 0.7)',  // 深睡
                                'rgba(153, 102, 255, 0.7)', // 浅睡
                                'rgba(153, 102, 255, 0.5)'  // 浅睡
                            ],
                            borderColor: 'rgba(54, 162, 235, 0.1)',
                            borderWidth: 1,
                            barPercentage: 1.0,
                            categoryPercentage: 1.0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 4,
                                ticks: {
                                    callback: function(value) {
                                        const labels = ['', '浅睡', '浅睡', '深睡', '深睡'];
                                        return labels[value] || '';
                                    }
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const value = context.parsed.y;
                                        const sleepPhases = ['醒着', '浅睡', '浅睡', '深睡', '深睡'];
                                        return sleepPhases[value] || '';
                                    }
                                }
                            }
                        }
                    }
                });
            }
        },
        // 新增：初始化营养图表
        initNutritionChart() {
            const ctx = document.getElementById('nutritionChart');
            if (ctx) {
                new Chart(ctx, {
                    type: 'pie',
                    data: {
                        labels: ['蛋白质', '碳水化合物', '脂肪'],
                        datasets: [{
                            data: [65, 220, 45],
                            backgroundColor: [
                                'rgba(54, 162, 235, 0.7)',
                                'rgba(75, 192, 192, 0.7)',
                                'rgba(255, 159, 64, 0.7)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }
        },
        initBloodPressureChart() {
            const ctx = document.getElementById('bloodPressureChart');
            if (ctx) {
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                        datasets: [
                            {
                                label: '收缩压',
                                data: [130, 128, 125, 132, 126, 120, 122],
                                borderColor: '#5B8FF9',
                                backgroundColor: 'rgba(91, 143, 249, 0.2)',
                                tension: 0.3,
                                fill: false
                            },
                            {
                                label: '舒张压',
                                data: [85, 82, 80, 84, 81, 78, 80],
                                borderColor: '#5AD8A6',
                                backgroundColor: 'rgba(90, 216, 166, 0.2)',
                                tension: 0.3,
                                fill: false
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: false,
                                min: 60,
                                max: 160,
                                grid: {
                                    drawBorder: false
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                position: 'top'
                            },
                            annotation: {
                                annotations: {
                                    line1: {
                                        type: 'line',
                                        yMin: 140,
                                        yMax: 140,
                                        borderColor: 'rgba(255, 77, 79, 0.5)',
                                        borderWidth: 1,
                                        borderDash: [5, 5],
                                        label: {
                                            content: '收缩压警戒线',
                                            display: true,
                                            position: 'right'
                                        }
                                    },
                                    line2: {
                                        type: 'line',
                                        yMin: 90,
                                        yMax: 90,
                                        borderColor: 'rgba(255, 77, 79, 0.5)',
                                        borderWidth: 1,
                                        borderDash: [5, 5],
                                        label: {
                                            content: '舒张压警戒线',
                                            display: true,
                                            position: 'right'
                                        }
                                    }
                                }
                            }
                        }
                    }
                });
            }
        },
        initBloodSugarChart() {
            const ctx = document.getElementById('bloodSugarChart');
            if (ctx) {
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                        datasets: [
                            {
                                label: '空腹血糖',
                                data: [5.8, 6.1, 5.9, 6.2, 6.0, 5.7, 5.8],
                                borderColor: '#FF4D4F',
                                backgroundColor: 'rgba(255, 77, 79, 0.2)',
                                tension: 0.3,
                                fill: false
                            },
                            {
                                label: '餐后2小时',
                                data: [7.6, 7.9, 7.8, 8.1, 7.7, 7.5, 7.4],
                                borderColor: '#FAAD14',
                                backgroundColor: 'rgba(250, 173, 20, 0.2)',
                                tension: 0.3,
                                fill: false
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: false,
                                min: 4,
                                max: 10,
                                grid: {
                                    drawBorder: false
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                position: 'top'
                            }
                        }
                    }
                });
            }
        },
        toggleTimeline() {
            this.timelineCollapsed = !this.timelineCollapsed;
        },
        playAudio(audioId) {
            // 创建语音合成对象
            const synth = window.speechSynthesis;
            let text = '';

            // 根据传入的audioId获取要读取的文本
            if (audioId === 'point1') {
                text = '老年高血压患者的控制目标应为收缩压140mmHg以下，舒张压90mmHg以下';
            } else if (audioId === 'point2') {
                text = '每日食盐摄入量应控制在5克以内，膳食应减少脂肪摄入，增加蔬果比例';
            } else if (audioId === 'point3') {
                text = '高血压用药需规律服用，不可自行调整药量或停药，避免血压波动';
            }

            // 创建语音对象
            const utterThis = new SpeechSynthesisUtterance(text);

            // 设置语言为中文
            utterThis.lang = 'zh-CN';

            // 播放语音
            synth.speak(utterThis);
        },
        playVideo() {
            // 显示视频播放模态窗口
            new bootstrap.Modal(document.getElementById('videoModal')).show();
        },
        toggleBookmark() {
            // 显示收藏成功模态窗口
            new bootstrap.Modal(document.getElementById('bookmarkModal')).show();
        },
        showProgress() {
            // 显示学习进度模态窗口
            new bootstrap.Modal(document.getElementById('progressModal')).show();
        },
        showMedication() {
            // 显示用药信息模态窗口
            new bootstrap.Modal(document.getElementById('medicationModal')).show();
        },
        askQuestion() {
            // 显示提问模态窗口
            new bootstrap.Modal(document.getElementById('questionModal')).show();
        },
        adjustFontSize(delta) {
            // 调整字体大小
            this.userFontSize = Math.max(16, Math.min(32, this.userFontSize + delta));

            // 设置CSS变量
            document.documentElement.style.setProperty('--user-font-size', this.userFontSize + 'px');

            // 显示调整后的字体大小
            alert('字体大小已调整为: ' + this.userFontSize + 'px');
        },
        switchColorMode(mode) {
            // 移除所有颜色模式激活状态
            document.querySelectorAll('.color-mode').forEach(el => {
                el.classList.remove('active');
            });

            // 添加当前选中的颜色模式激活状态
            document.querySelector('.color-mode-' + mode).classList.add('active');

            // 设置颜色模式
            this.colorMode = mode;

            // 应用颜色模式样式
            const body = document.querySelector('body');
            body.classList.remove('mode-default', 'mode-high-contrast', 'mode-low-vision');
            body.classList.add('mode-' + mode);

            // 根据不同模式设置不同的样式
            if (mode === 'default') {
                document.documentElement.style.setProperty('--bg-color', '#ffffff');
                document.documentElement.style.setProperty('--text-color', '#333333');
            } else if (mode === 'high-contrast') {
                document.documentElement.style.setProperty('--bg-color', '#000000');
                document.documentElement.style.setProperty('--text-color', '#ffffff');
            } else if (mode === 'low-vision') {
                document.documentElement.style.setProperty('--bg-color', '#C7E4F2');
                document.documentElement.style.setProperty('--text-color', '#1A3658');
            }
        },
        activateVoiceSearch() {
            // 检查浏览器是否支持语音识别
            if ('webkitSpeechRecognition' in window) {
                alert('请说出您想了解的健康知识，例如"高血压"、"糖尿病"等');
                // 实际项目中这里会启动语音识别功能
            } else {
                alert('很抱歉，您的浏览器不支持语音识别功能');
            }
        },
        startReadAloud() {
            if (!this.readingAloud) {
                // 开始朗读
                this.readingAloud = true;

                // 获取文章内容
                let articleContent = '';
                const contentElements = document.querySelectorAll('.article-content p');
                contentElements.forEach(el => {
                    articleContent += el.textContent + ' ';
                });

                // 创建语音合成对象
                const synth = window.speechSynthesis;
                const utterThis = new SpeechSynthesisUtterance(articleContent);

                // 设置语言为中文
                utterThis.lang = 'zh-CN';

                // 语音播放结束回调
                utterThis.onend = () => {
                    this.readingAloud = false;
                };

                // 播放语音
                synth.speak(utterThis);

                alert('开始朗读文章');
            } else {
                // 停止朗读
                window.speechSynthesis.cancel();
                this.readingAloud = false;

                alert('停止朗读');
            }
        },

        // 健康科普相关方法
        async loadHealthArticles() {
            this.articlesLoading = true;
            // 直接使用模拟数据，确保分类筛选功能正常工作
            this.loadMockArticles();
            this.articlesLoading = false;
        },

        loadMockArticles() {
            // 模拟健康科普文章数据 - 包含所有分类
            this.articles = [
                // 营养膳食类
                {
                    article_id: '1',
                    title: '骨头汤不补钙！真正补钙的是这6种食物',
                    content: '钙是人体含量最多的矿物质元素，是人体骨骼和牙齿中无机物的主要成分，占成人体重的1.5%-2.0%，其中绝大多数钙集中在骨骼和牙齿中。',
                    category: '营养膳食',
                    publish_time: '2024-02-27',
                    read_count: 203,
                    image_url: '/static/images/articles/text1.png',
                    detailedContent: {
                        sections: [
                            {
                                title: '钙的重要作用',
                                content: '钙不仅是骨骼和牙齿的构成成分，还具有维持神经和肌肉的活动，促进细胞信息传递，促进血液凝固，调节机体酶的活性，维持细胞膜的稳定性等功能。食物是人体获得钙的主要途径，但补钙的说法五花八门，我们今天就来辨一辨。'
                            },
                            {
                                title: '这3种东西都不能补钙',
                                content: '1.吃虾皮不能补钙：尽管虾皮钙含量高达991毫克/100克，但是生物利用率相对低，也不可能天天或经常食用，所以一般不推荐通过食用虾皮补钙。\n\n2.吃芝麻酱不能补钙：芝麻酱钙含量为1170毫克/100克，但也存在生物利用率相对较低的问题，日常食用的量有限。\n\n3.喝骨头汤也不能补钙：研究发现，50克猪骨经过20分钟的熬煮，汤中的钙浓度仅为67.6毫克/升。一碗200毫升的汤中仅仅含钙13.52毫克。骨头汤中的乳白色物质并不是钙，而是溶出的脂肪。'
                            },
                            {
                                title: '真正补钙的6种食物',
                                content: '1.牛奶及奶制品：奶及奶制品是膳食钙的最好来源，生物利用率约为32.1%，是钙的主要来源。\n\n2.豆类及豆制品：豆类及其制品钙含量也较高，生物利用率达20%以上。\n\n3.深绿色叶菜：深绿色的叶菜和菜花也含有较多的钙。\n\n4.柑橘类水果：水果中除柑橘类钙含量较高，其余水果钙含量低。\n\n5.贝类食物：在动物性食物中，贝类钙含量最高，鱼类和蛋类的钙含量也较高。\n\n6.水：饮水也是钙的来源之一，硬度高的水钙含量可达60-140毫克/升。'
                            }
                        ]
                    }
                },
                {
                    article_id: '2',
                    title: '喝茶不对不仅不养生，还可能致癌？',
                    content: '茶是一种健康的饮品，与咖啡、可可并称为"世界三大饮料"，尤其受中国人的喜爱。在茶圈子中流传着这样一句话——"隔夜茶，毒如蛇"，人们普遍认为隔夜茶会对人产生危害。',
                    category: '营养膳食',
                    publish_time: '2024-03-22',
                    read_count: 134,
                    image_url: '/static/images/articles/tea-health.jpg',
                    detailedContent: {
                        sections: [
                            {
                                title: '隔夜茶真的有毒吗？',
                                content: '为了探究隔夜茶能不能喝，知名科学博主老爸评测将3壶隔夜茶送去实验室，检测各项指标。这3种茶是最常见的绿茶、红茶和乌龙茶，按照1：50的茶水比例泡好后将茶叶滤除，取出一部分空白样，剩余部分敞口放过夜，24小时后取隔夜样。检测项目包括菌落总数、硝酸盐和亚硝酸盐含量、茶多酚含量。'
                            },
                            {
                                title: '实验结果令人意外',
                                content: '实验结果显示，3种隔夜茶的菌落总数都小于10 CFU/mL，远低于《GB 5749-2006生活饮用水卫生标准》的限量标准100 CFU/mL。硝酸盐和亚硝酸盐含量都没有太大的变化，均低于16mg/kg，不会对人体产生致癌作用。茶多酚含量和刚泡好时相差不多，没有大量分解。上述实验均表明，隔夜茶是安全无毒的，完全可以放心饮用。'
                            },
                            {
                                title: '真正的喝茶禁忌',
                                content: '虽然隔夜茶安全无毒，但喝茶并非没有禁忌。忌喝烫茶：2016年，世界卫生组织将"65℃以上的饮品"列为2A类致癌物，烫茶就包含其中。忌空腹喝茶：茶水中的咖啡因会刺激中枢神经产生兴奋，促进胃液的分泌和肠胃蠕动。如果空腹喝茶，胃里没有食物，过多的胃酸就会刺激胃粘膜。'
                            }
                        ]
                    }
                },
                // 运动健身类
                {
                    article_id: '3',
                    title: '走路快 不易血压高',
                    content: '一项基于中国健康与养老追踪调查（CHARLS）项目的新研究提示，对于中国老年人来说，步行速度较快与高血压风险较低有关，在超重和肥胖老年人中尤其明显。',
                    category: '运动健身',
                    publish_time: '2023-12-05',
                    read_count: 178,
                    image_url: '/static/images/text3.png',
                    detailedContent: {
                        sections: [
                            {
                                title: '研究发现',
                                content: '作者指出，该研究结果说明，步行速度可能是高血压的早期预测因素之一，可用来作为高危人群的干预目标之一。\n\n该研究分析了2733名年龄≥60岁的老人，通过客观测量其在2.5米路程中的日常步态来评估步行速度，并根据四分位数将其分为四组。随访4年期间，26.9%的人发生高血压。'
                            },
                            {
                                title: '步行速度与血压的关系',
                                content: '校正年龄、性别、婚姻状况、接受教育程度、吸烟、饮酒、中高强度身体活动、体重指数后，分析显示，步行速度与高血压风险呈负相关，即步行速度越快，高血压的风险越低。\n\n另外，在超重和肥胖的老年人中，步行速度与高血压风险的相关性更强。然而，这种相关性在瘦人中并不明显。研究者指出，这说明体重指数在步行速度与高血压风险之间的关联中起着一定的作用。'
                            },
                            {
                                title: '血压变化趋势',
                                content: '该研究还发现，步行速度与收缩压和舒张压之间呈类似的关联趋势。随着步行速度增快，收缩压和舒张压均逐渐降低，随后逐渐趋于平稳。\n\n在该研究中，老年人的平均年龄为66.8岁，其中46.6%为女士，平均步行速度为0.65米/秒。'
                            }
                        ]
                    }
                },
                {
                    article_id: '4',
                    title: '多彩全民健身活动迎新年',
                    content: '这几日，全国各地举办各种体育活动和赛事，用全民健身的方式迎接新年的到来。其中，全国新年登高健身大会在浙江江山、新疆和硕、海南保亭、黑龙江海林、湖南张家界等31个省区市300多个举办地同步联动，近百万群众纷纷通过登高望远的形式辞旧迎新。',
                    category: '运动健身',
                    publish_time: '2024-01-02',
                    read_count: 167,
                    image_url: '/static/images/articles/xinnian.png'
                },

                // 心理健康类
                
                {
                    article_id: '6',
                    title: '如何应对老年期的孤独感',
                    content: '孤独感是老年人常见的心理问题，长期的孤独感会影响身心健康。建议老年人主动参与社交活动，如社区聚会、兴趣小组等。保持与家人朋友的联系，利用现代通讯工具与远方的亲友交流。培养新的兴趣爱好，如书法、绘画、园艺等，既能充实生活，又能结识志同道合的朋友。',
                    category: '心理健康',
                    publish_time: '2024-01-16',
                    read_count: 145,
                    image_url: '/static/images/articles/lonely.png'
                },

                // 疾病预防类
                {
                    article_id: '7',
                    title: '冬季养生：如何预防感冒',
                    content: '冬季是感冒高发季节，由于免疫力相对较弱，更容易受到感冒病毒的侵袭。预防感冒的关键在于增强体质、注意保暖、合理饮食。建议每天进行适量运动，如散步、太极拳等，增强身体抵抗力。同时要注意室内通风，保持空气新鲜。饮食方面应多吃富含维生素C的食物，如柑橘类水果、绿叶蔬菜等。',
                    category: '疾病预防',
                    publish_time: '2024-01-15',
                    read_count: 156,
                    image_url: '/static/images/articles/winter.png'
                },
                {
                    article_id: '8',
                    title: '高血压的预防与管理',
                    content: '高血压是常见的慢性疾病，需要长期管理。预防高血压要从生活方式入手：控制体重、限制钠盐摄入、适量运动、戒烟限酒。已患高血压的人要按医嘱服药，定期监测血压，保持血压稳定。同时要注意饮食调理，多吃富含钾的食物，如香蕉、橙子等，有助于降低血压。',
                    category: '疾病预防',
                    publish_time: '2024-01-18',
                    read_count: 198,
                    image_url: '/static/images/articles/gaoya.png'
                },

                // 用药安全类
                {
                    article_id: '9',
                    title: '安全用药指南',
                    content: '用药需要特别谨慎，因为随着年龄增长，药物代谢能力下降，容易出现不良反应。用药时要严格按照医嘱，不可随意增减药量或停药。同时要注意药物之间的相互作用，如果需要同时服用多种药物，应咨询医生或药师。保存药物时要注意避光、防潮，定期检查药物有效期。',
                    category: '用药安全',
                    publish_time: '2024-01-20',
                    read_count: 176,
                    image_url: '/static/images/articles/yongyao.png'
                },
                {
                    article_id: '10',
                    title: '常见药物不良反应及应对方法',
                    content: '容易出现药物不良反应，常见的包括头晕、恶心、皮疹等。出现不良反应时，应立即停药并咨询医生。为了减少不良反应的发生，建议定期复查，监测药物疗效和安全性。同时要告知医生所有正在服用的药物，包括保健品和中药，避免药物相互作用。',
                    category: '用药安全',
                    publish_time: '2024-01-22',
                    read_count: 163,
                    image_url: '/static/images/articles/buliang.png'
                },

                // 营养膳食类（继续）
                {
                    article_id: '13',
                    title: '只吃粗粮真的会更健康吗？看看你吃对了没',
                    content: '随着饮食水平和养生意识的提高，越来越多的朋友开始注重膳食平衡。然而，有一些朋友干脆把所有主食都用粗粮替代，认为这样不仅可以减肥，还更健康。',
                    category: '营养膳食',
                    publish_time: '2024-03-22',
                    read_count: 165,
                    image_url: '/static/images/articles/culiang.jpg',
                    detailedContent: {
                        sections: [
                            {
                                title: '粗粮的营养价值',
                                content: '粗粮确实含有丰富的膳食纤维、维生素B族、矿物质等营养成分，对人体健康有益。膳食纤维可以促进肠道蠕动，有助于预防便秘；维生素B族参与能量代谢，对神经系统健康很重要；矿物质如铁、锌、镁等对维持正常生理功能必不可少。'
                            },
                            {
                                title: '只吃粗粮的风险',
                                content: '然而，完全用粗粮替代精制谷物并不科学。粗粮中的植酸、单宁等抗营养因子会影响矿物质的吸收；过多的膳食纤维可能导致腹胀、腹泻等消化不良症状；对于消化功能较弱的老年人来说，大量食用粗粮可能加重胃肠负担。'
                            },
                            {
                                title: '科学的粗粮食用方法',
                                content: '建议粗粮与精制谷物合理搭配，粗粮占主食的1/3-1/2即可。可以选择燕麦、糙米、全麦面包等相对容易消化的粗粮。烹饪时适当延长时间，使粗粮更软烂易消化。对于有胃肠疾病的老年人，应在医生指导下适量食用粗粮。'
                            }
                        ]
                    }
                },
                {
                    article_id: '14',
                    title: '节日期间合理安排饮食 保持食物清淡原则',
                    content: '节日期间，家庭聚餐增多，如何合理安排饮食，保持身体健康？专家建议保持食物清淡原则，避免暴饮暴食。',
                    category: '营养膳食',
                    publish_time: '2024-01-02',
                    read_count: 143,
                    image_url: '/static/images/articles/holiday.png',
                    detailedContent: {
                        sections: [
                            {
                                title: '节日饮食的常见问题',
                                content: '节日期间，人们往往会摄入过多高脂肪、高热量、高盐分的食物，如油炸食品、甜点、腌制品等。这些食物虽然美味，但容易导致消化不良、血糖血脂升高、水钠潴留等问题，特别是对老年人的健康造成负担。'
                            },
                            {
                                title: '清淡饮食的原则',
                                content: '保持食物清淡并不意味着完全无味，而是要控制油、盐、糖的使用量。建议多采用蒸、煮、炖、拌等烹饪方式，减少油炸、烧烤等高温烹饪。选择新鲜的蔬菜、水果、瘦肉、鱼类等食材，保证营养均衡。'
                            },
                            {
                                title: '节日期间的饮食建议',
                                content: '1.控制食量，避免暴饮暴食；2.增加蔬菜水果的摄入，减少肉类比例；3.选择清淡的汤品，如蔬菜汤、瘦肉汤；4.限制酒精摄入，多喝白开水或淡茶；5.规律进餐时间，不要因为聚餐而打乱正常的饮食节奏。'
                            }
                        ]
                    }
                },

                // 运动健身类（继续）
                {
                    article_id: '15',
                    title: '重感冒多休息 剧烈运动易"伤心"',
                    content: '前不久，来自重庆的一名知名主持人因病去世，年仅42岁。据悉，他是患重感冒后未痊愈，在晚上大运动量锻炼时，诱发了心肌梗死。',
                    category: '运动健身',
                    publish_time: '2023-12-05',
                    read_count: 156,
                    image_url: '/static/images/articles/ganmao.png',
                    detailedContent: {
                        sections: [
                            {
                                title: '感冒期间运动的风险',
                                content: '感冒期间，人体免疫系统正在与病毒作斗争，身体处于相对虚弱的状态。此时进行剧烈运动，会进一步消耗体力，降低免疫力，不仅不利于感冒的恢复，还可能引发更严重的并发症。'
                            },
                            {
                                title: '运动诱发心脏问题的机制',
                                content: '感冒时，病毒可能侵犯心肌，导致心肌炎。在这种情况下进行剧烈运动，会增加心脏负担，可能诱发心律失常、心肌梗死等严重后果。特别是对于有心血管疾病风险的中老年人，更应该格外注意。'
                            },
                            {
                                title: '感冒期间的正确做法',
                                content: '1.充分休息，保证充足的睡眠；2.多喝温开水，保持身体水分；3.避免剧烈运动，可以进行轻微的散步；4.注意保暖，避免受凉；5.如果症状加重或出现胸闷、心慌等症状，应立即就医。感冒完全康复后，再逐步恢复正常的运动量。'
                            }
                        ]
                    }
                },
                {
                    article_id: '16',
                    title: '如何预防运动损伤',
                    content: '运动时容易发生损伤，预防措施包括：运动前充分热身，运动后适当放松；选择合适的运动强度，避免过度疲劳；穿着合适的运动鞋，选择安全的运动场所；如有不适立即停止运动。常见的运动损伤有肌肉拉伤、关节扭伤等，一旦发生应及时处理，必要时就医。',
                    category: '运动健身',
                    publish_time: '2024-02-08',
                    read_count: 134,
                    image_url: '/static/images/articles/run.png'
                },

                // 心理健康类（继续）
                {
                    article_id: '17',
                    title: '如何保持积极心态',
                    content: '积极的心态对身心健康至关重要。建议培养乐观的生活态度，学会感恩和知足；保持学习的热情，可以学习新技能或培养新爱好；多与他人交流，分享生活感悟；适当参与志愿服务，体现自身价值。遇到困难时，要学会寻求帮助，不要独自承受压力。',
                    category: '心理健康',
                    publish_time: '2024-02-10',
                    read_count: 128,
                    image_url: '/static/images/articles/xintai.png'
                },
                 {
                    article_id: '18',
                    title: '老年人心理健康的重要性',
                    content: '心理健康对老年人的整体健康状况有重要影响。保持积极乐观的心态，多与家人朋友交流，参加社区活动，培养兴趣爱好，都有助于维护心理健康。如果出现持续的情绪低落、焦虑等症状，应及时寻求专业帮助。良好的心理状态不仅能提高生活质量，还能增强免疫力，延缓衰老。',
                    category: '心理健康',
                    publish_time: '2024-01-05',
                    read_count: 134,
                    image_url: '/static/images/articles/xinli.png'
                },

                // 疾病预防类（继续）
                {
                    article_id: '19',
                    title: '糖尿病的预防与管理',
                    content: '糖尿病是常见的慢性疾病，预防措施包括：控制体重，保持健康的BMI；合理饮食，限制糖分和精制碳水化合物的摄入；规律运动，每周至少150分钟中等强度运动；定期体检，监测血糖水平。已患糖尿病的人要按医嘱用药，监测血糖，预防并发症的发生。',
                    category: '疾病预防',
                    publish_time: '2024-02-15',
                    read_count: 189,
                    image_url: '/static/images/articles/tangniao.png'
                },
                {
                    article_id: '20',
                    title: '冬季气温下降，如何科学应对脑卒中',
                    content: '随着冬季气温降低，卒中的风险也在增加。近一段时间，记者在北京、吉林等地的医院发现，因卒中前来就诊的患者明显增加。',
                    category: '疾病预防',
                    publish_time: '2024-02-18',
                    read_count: 234,
                    image_url: '/static/images/articles/brain.png',
                    detailedContent: {
                        sections: [
                            {
                                title: '冬季脑卒中高发的原因',
                                content: '首都医科大学宣武医院急诊科副主任马青峰提醒，脑卒中患者以老年人居多，且往往都有一些脑卒中的高危因素，比如高血压、糖尿病、高脂血症或者冠心病等，天气一旦出现变化，血压骤然增加，就可以造成血管破裂，造成脑出血。\n\n这几天，吉林长春的最低气温达到了零下十几摄氏度。在吉林大学第二医院急诊科，这位老人被家属送来时已半身瘫痪，意识模糊。患者家属张振超介绍，老人平时没有病，中午11点多出去买完东西回来后就坐地上了。家里人正好都在，找了辆车20分钟就到医院了。'
                            },
                            {
                                title: '及时救治的重要性',
                                content: '经过一系列检查后确定患者是脑梗，需要进行溶栓治疗，急诊医生立刻启动绿色通道。进行溶栓治疗20分钟后，患者意识逐渐清醒，偏瘫的状况也有所改善。\n\n吉林大学第二医院急诊与重症医学中心副主任孙宏志表示，温度骤降以后，心脑血管病发病率明显增加，近一个月脑卒中患者增加20%到30%。'
                            },
                            {
                                title: '"120"三步法：迅速识别是否中风',
                                content: '卒中，俗称为"中风"，在发病后的4.5小时之内是黄金救治时间，可以最大程度降低致残率和死亡率。那么，如果发生了中风，该如何自救、互救呢？\n\n如何判断身边的人是否中风？复旦大学临床医学院神经病学系副主任赵静介绍了中风"120"三步法：一看脸，一侧的脸歪了；二查两只手，单手不能抬了，因为中风基本上都是偏瘫；0就是聆听一句话，说不清说不明白了。三个任何一个突然发生都有可能是中风。'
                            },
                            {
                                title: '紧急救治措施',
                                content: '专家提醒，身边一旦有人出现中风的症状，要第一时间拨打120急救电话，同时让患者平躺、侧卧。此外，在4.5小时黄金时间窗内，将患者送到有卒中救治能力的医院至关重要。\n\n首都医科大学宣武医院急诊科副主任马青峰介绍，大部分医院都有脑卒中救治的绿色通道，会有相关的人员快速地给病人进行检查，进行抽血化验、CT影像以及后续的评估和治疗。'
                            },
                            {
                                title: '冬季预防脑卒中的方法',
                                content: '冬季我们该如何预防脑卒中的发生？医生介绍，高血压患者是脑卒中的高危人群，冬季应定期监测血压，一旦发现血压升高，应及时就医或调整药物。对于一些患有脑血管狭窄等疾病的老年人来说，冷暖交替会刺激血管收缩，导致脑梗。\n\n情绪波动可能导致血压升高，增加脑卒中的风险。因此，避免过度紧张和劳累，防止血压剧烈波动，对于预防脑卒中至关重要。首都医科大学附属北京中医医院主任医师王麟鹏表示，因为血压波动本身可以出现脑子供血的血流动力学的改变，在年轻的时候是可以自我调整的，血管弹性好，但是岁数大了以后调整不了。'
                            },
                            {
                                title: '生活方式调整建议',
                                content: '医生建议，对于脑卒中高危人群来说，冬季应减少高脂肪、高热量食物的摄入，同时多喝水，这有助于降低血液粘稠度。此外，还可以适当减少户外活动，避免室内外温差过大刺激血管收缩。在室内运动同样可以达到锻炼的效果。'
                            }
                        ]
                    }
                },

                // 用药安全类（继续）
                {
                    article_id: '21',
                    title: '多重用药的注意事项',
                    content: '往往患有多种疾病，需要同时服用多种药物，这增加了用药风险。多重用药的注意事项包括：定期复查，评估药物的必要性；了解药物间的相互作用；按时按量服药，不可随意增减；使用药盒分装，避免漏服或重复服用。建议建立用药档案，记录所有正在服用的药物。',
                    category: '用药安全',
                    publish_time: '2024-02-20',
                    read_count: 145,
                    image_url: '/static/images/articles/duocong.png'
                },
                {
                    article_id: '22',
                    title: '中药使用安全指南',
                    content: '中药在人群中使用广泛，但也需要注意安全性。使用中药的注意事项包括：选择正规渠道购买中药；遵医嘱使用，不可自行调整剂量；注意中药与西药的相互作用；观察用药后的反应，如有不适及时停药就医。一些中药可能含有重金属或有毒成分，长期使用需要定期检查肝肾功能。',
                    category: '用药安全',
                    publish_time: '2024-02-22',
                    read_count: 158,
                    image_url: '/static/images/articles/zhongyao.png'
                },

                // 急救常识类
                {
                    article_id: '23',
                    title: '常见急症的家庭急救',
                    content: '容易发生心脏病、中风、跌倒等急症。家属应掌握基本的急救知识：心脏病发作时让患者保持安静，服用硝酸甘油；疑似中风时立即拨打120，记录发病时间；跌倒后不要急于搀扶，先检查是否有骨折。平时要准备急救药品，熟悉急救电话，关键时刻能够正确应对。',
                    category: '急救常识',
                    publish_time: '2024-01-25',
                    read_count: 187,
                    image_url: '/static/images/articles/jijiu.png'
                },
                {
                    article_id: '24',
                    title: 'CPR心肺复苏术基础知识',
                    content: 'CPR是挽救生命的重要技能，每个人都应该掌握。CPR的操作要点包括：判断意识和呼吸、拨打急救电话、进行胸外按压和人工呼吸。按压位置在胸骨下半部，深度5-6厘米，频率100-120次/分钟。虽然骨骼较脆弱，但在生命危急时刻，正确的CPR仍然是最有效的急救措施。',
                    category: '急救常识',
                    publish_time: '2024-01-28',
                    read_count: 142,
                    image_url: '/static/images/articles/C'
                },
                {
                    article_id: '25',
                    title: '突发疾病的识别与处理',
                    content: '突发疾病时，正确的识别和处理至关重要。常见的突发疾病包括：心绞痛、心肌梗死、脑卒中、低血糖等。识别要点：胸痛、呼吸困难可能是心脏病；突然说话不清、肢体无力可能是脑卒中；出汗、心慌、饥饿感可能是低血糖。处理原则：保持冷静、立即呼救、采取相应的急救措施。',
                    category: '急救常识',
                    publish_time: '2024-02-25',
                    read_count: 176,
                    image_url: '/static/images/articles/emergency-recognition.jpg'
                },
                {
                    article_id: '26',
                    title: '家庭急救包的准备',
                    content: '家庭急救包是家庭必备的安全保障。急救包应包含：常用急救药品（硝酸甘油、速效救心丸、降压药等）；医疗器械（血压计、体温计、血糖仪等）；外伤处理用品（创可贴、纱布、碘伏等）；紧急联系卡（家属电话、医生电话、急救电话）。定期检查急救包，确保药品在有效期内，器械功能正常。',
                    category: '急救常识',
                    publish_time: '2024-02-28',
                    read_count: 163,
                    image_url: '/static/images/articles/first-aid-kit.jpg'
                }
            ];
            this.filterArticles();
            // 文章加载完成后，更新收藏列表
            this.loadFavoriteArticles();
        },

        selectCategory(category) {
            this.selectedCategory = category;
            this.currentPage = 1;
            // 重新筛选文章
            this.filterArticles();
        },

        filterArticles() {
            // 首先根据分类筛选文章
            let allFilteredArticles;
            if (this.selectedCategory === 'all') {
                allFilteredArticles = [...this.articles];
            } else {
                allFilteredArticles = this.articles.filter(article =>
                    article.category === this.selectedCategory
                );
            }

            // 然后根据搜索关键词进一步筛选
            if (this.searchKeyword.trim()) {
                const keyword = this.searchKeyword.trim().toLowerCase();
                allFilteredArticles = allFilteredArticles.filter(article => {
                    return article.title.toLowerCase().includes(keyword) ||
                           article.content.toLowerCase().includes(keyword) ||
                           article.category.toLowerCase().includes(keyword);
                });
            }

            // 重新计算分页
            this.totalPages = Math.ceil(allFilteredArticles.length / this.perPage);
            if (this.currentPage > this.totalPages && this.totalPages > 0) {
                this.currentPage = 1;
            }

            // 根据当前页面显示文章
            const startIndex = (this.currentPage - 1) * this.perPage;
            const endIndex = startIndex + this.perPage;
            this.filteredArticles = allFilteredArticles.slice(startIndex, endIndex);
        },

        // 搜索文章方法
        searchArticles() {
            this.currentPage = 1; // 重置到第一页
            this.filterArticles();
        },

        // 清除搜索
        clearSearch() {
            this.searchKeyword = '';
            this.currentPage = 1;
            this.filterArticles();
        },

        // 高亮搜索关键词
        highlightSearchKeyword(text) {
            if (!this.searchKeyword.trim() || !text) {
                return text;
            }

            const keyword = this.searchKeyword.trim();
            const regex = new RegExp(`(${keyword})`, 'gi');
            return text.replace(regex, '<mark class="bg-warning">$1</mark>');
        },

        showArticleDetail(article) {
            this.selectedArticle = article;
            this.checkIfArticleFavorited();
            this.articleExpanded = true;
            this.showFavorites = false; // 隐藏收藏页面
            // 增加阅读次数
            if (article.read_count !== undefined) {
                article.read_count += 1;
            }
        },

        closeArticle() {
            this.articleExpanded = false;
            this.selectedArticle = {};
        },

        playAudio(pointId) {
            // 语音播放功能（可以集成TTS服务）
            console.log('播放语音:', pointId);
            alert('语音播放功能开发中...');
        },

        playVideo() {
            // 视频播放功能
            console.log('播放专家视频');
            alert('专家视频播放功能开发中...');
        },

        checkIfArticleFavorited() {
            // 检查文章是否已收藏（这里可以从localStorage或API获取）
            const favoriteArticles = JSON.parse(localStorage.getItem('favoriteArticles') || '[]');
            this.isArticleFavorited = favoriteArticles.includes(this.selectedArticle.article_id);
        },

        toggleFavorite() {
            const favoriteArticles = JSON.parse(localStorage.getItem('favoriteArticles') || '[]');
            const articleId = this.selectedArticle.article_id;

            if (this.isArticleFavorited) {
                // 取消收藏
                const index = favoriteArticles.indexOf(articleId);
                if (index > -1) {
                    favoriteArticles.splice(index, 1);
                }
                this.isArticleFavorited = false;

                // 从收藏列表中移除
                const favIndex = this.favoriteArticles.findIndex(article => article.article_id === articleId);
                if (favIndex > -1) {
                    this.favoriteArticles.splice(favIndex, 1);
                }

                alert('已取消收藏');
            } else {
                // 添加收藏
                favoriteArticles.push(articleId);
                this.isArticleFavorited = true;

                // 添加到收藏列表
                this.favoriteArticles.push(this.selectedArticle);

                alert('文章已收藏');
            }

            localStorage.setItem('favoriteArticles', JSON.stringify(favoriteArticles));
        },

        loadFavoriteArticles() {
            // 从localStorage加载收藏的文章ID
            const favoriteIds = JSON.parse(localStorage.getItem('favoriteArticles') || '[]');

            // 根据ID找到对应的文章
            this.favoriteArticles = this.articles.filter(article =>
                favoriteIds.includes(article.article_id)
            );
        },

        showFavoriteArticles() {
            this.showFavorites = true;
            this.articleExpanded = false;
            // 重新加载收藏列表以确保是最新的
            this.loadFavoriteArticles();
        },

        hideFavoriteArticles() {
            this.showFavorites = false;
        },

        shareArticle() {
            // 分享文章功能
            if (navigator.share) {
                navigator.share({
                    title: this.selectedArticle.title,
                    text: this.selectedArticle.content.substring(0, 100) + '...',
                    url: window.location.href
                });
            } else {
                // 复制链接到剪贴板
                navigator.clipboard.writeText(window.location.href).then(() => {
                    alert('文章链接已复制到剪贴板');
                });
            }
        },

        changePage(page) {
            if (page >= 1 && page <= this.totalPages) {
                this.currentPage = page;
                this.filterArticles();
            }
        },

        ...foodOrderModule.methods, // 合并订餐模块的methods
        ...emergencyCallModule.methods // 合并紧急呼叫模块的methods
    },
    computed: {
        visiblePages() {
            const pages = [];
            const start = Math.max(1, this.currentPage - 2);
            const end = Math.min(this.totalPages, this.currentPage + 2);

            for (let i = start; i <= end; i++) {
                pages.push(i);
            }
            return pages;
        }
    }
});