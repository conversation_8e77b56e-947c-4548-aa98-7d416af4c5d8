#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接检查模板文件内容
"""

def check_template_file():
    """检查模板文件是否包含健康科普内容"""
    try:
        with open('web_project/app/templates/elderly_center.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键内容
        checks = [
            ('健康科普导航项', '健康科普' in content and 'health_edu' in content),
            ('健康科普页面', 'currentTab === \'health_edu\'' in content),
            ('分类筛选', '科普分类' in content),
            ('文章列表', 'filteredArticles' in content),
            ('模态窗口', 'articleDetailModal' in content)
        ]
        
        print("模板文件检查结果:")
        for check_name, result in checks:
            status = "✅" if result else "❌"
            print(f"{status} {check_name}: {result}")
        
        # 统计行数
        lines = content.split('\n')
        print(f"\n模板文件总行数: {len(lines)}")
        
        # 查找健康科普相关行
        health_edu_lines = []
        for i, line in enumerate(lines, 1):
            if '健康科普' in line or 'health_edu' in line:
                health_edu_lines.append(f"第{i}行: {line.strip()}")
        
        print(f"\n找到 {len(health_edu_lines)} 行包含健康科普相关内容:")
        for line in health_edu_lines[:10]:  # 只显示前10行
            print(line)
        
        if len(health_edu_lines) > 10:
            print(f"... 还有 {len(health_edu_lines) - 10} 行")
            
    except Exception as e:
        print(f"检查失败: {e}")

if __name__ == "__main__":
    check_template_file()
