#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模板内容的脚本
"""

import requests
import re

def test_template_content():
    """测试模板是否包含健康科普内容"""
    try:
        response = requests.get("http://127.0.0.1:5000/elderly", timeout=10)
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            
            # 检查是否包含健康科普相关内容
            health_edu_patterns = [
                r'健康科普',
                r'health_edu',
                r'bi-book-half',
                r'科普分类',
                r'营养膳食',
                r'运动健身'
            ]
            
            found_patterns = []
            for pattern in health_edu_patterns:
                if re.search(pattern, content):
                    found_patterns.append(pattern)
            
            print(f"找到的健康科普相关内容: {found_patterns}")
            
            if found_patterns:
                print("✅ 模板包含健康科普内容")
            else:
                print("❌ 模板不包含健康科普内容")
                
            # 保存HTML内容到文件以便检查
            with open('elderly_page_content.html', 'w', encoding='utf-8') as f:
                f.write(content)
            print("页面内容已保存到 elderly_page_content.html")
            
        else:
            print(f"请求失败，状态码: {response.status_code}")
            
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    test_template_content()
