{% extends "base.html" %}

{% block title %}登录 - 居家养老健康管理系统{% endblock %}

{% block head %}
<style>
    .login-container {
        max-width: 500px;
        margin: 50px auto;
    }
    
    .user-type-selector {
        margin-bottom: 20px;
    }
    
    .user-type-btn {
        width: 100%;
        text-align: left;
        padding: 15px;
        margin-bottom: 10px;
        border-radius: 8px;
        border: 2px solid #ddd;
        display: flex;
        align-items: center;
        transition: all 0.3s;
    }
    
    .user-type-btn.active {
        border-color: #3A84C3;
        background-color: rgba(0, 123, 255, 0.1);
    }
    
    .user-type-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    .user-icon {
        width: 40px;
        height: 40px;
        background-color: #e9ecef;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 1.2rem;
    }
    
    .elderly-icon { background-color: #d4edda; color: #28a745; }
    .family-icon { background-color: #cce5ff; color: #3A84C3; }
    .worker-icon { background-color: #f8d7da; color: #dc3545; }
</style>
{% endblock %}

{% block content %}
<div id="login-app" class="login-container">
    <div class="card shadow">
        <div class="card-header bg-primary text-white">
            <h3 class="mb-0">用户登录</h3>
        </div>
        <div class="card-body">
            <div v-if="!selectedUserType" class="user-type-selector">
                <h4 class="mb-3">请选择您的用户身份</h4>
                
                <button @click="selectUserType('elderly')" class="user-type-btn btn">
                    <div class="user-icon elderly-icon">👴</div>
                    <div>
                        <h5 class="mb-1">老年人用户</h5>
                        <p class="mb-0 text-muted">管理健康数据、紧急呼叫、生活服务</p>
                    </div>
                </button>
                
                <button @click="selectUserType('family')" class="user-type-btn btn">
                    <div class="user-icon family-icon">👪</div>
                    <div>
                        <h5 class="mb-1">家属用户</h5>
                        <p class="mb-0 text-muted">监护老年人健康、接收通知</p>
                    </div>
                </button>
                
                <button @click="selectUserType('worker')" class="user-type-btn btn">
                    <div class="user-icon worker-icon">👩‍⚕️</div>
                    <div>
                        <h5 class="mb-1">社区工作人员</h5>
                        <p class="mb-0 text-muted">管理服务、处理紧急情况</p>
                    </div>
                </button>
            </div>
            
            <div v-if="selectedUserType" class="login-form">
                <div class="d-flex align-items-center mb-4">
                    <div class="user-icon" :class="iconClass">
                        <span v-if="selectedUserType === 'elderly'">👴</span>
                        <span v-else-if="selectedUserType === 'family'">👪</span>
                        <span v-else-if="selectedUserType === 'worker'">👩‍⚕️</span>
                    </div>
                    <h4 class="mb-0 ms-2">[[ userTypeText ]]登录</h4>
                    <button @click="resetUserType" class="btn btn-sm btn-outline-secondary ms-auto">切换身份</button>
                </div>
                
                <form @submit.prevent="login">
                    <div class="mb-3">
                        <label for="userId" class="form-label">[[ idFieldName ]]</label>
                        <input type="text" class="form-control" id="userId" v-model="userId" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">密码</label>
                        <input type="password" class="form-control" id="password" v-model="password" required>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="rememberMe" v-model="rememberMe">
                            <label class="form-check-label" for="rememberMe">记住我</label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary" :disabled="isLoading">
                            <span v-if="!isLoading">登录</span>
                            <span v-else>登录中...</span>
                        </button>
                    </div>
                    
                    <div v-if="errorMessage" class="alert alert-danger mt-3">
                        [[ errorMessage ]]
                    </div>
                </form>
                
                <hr>
                
                <div class="text-center">
                    <p>还没有账号？ <a :href="'{{ url_for('main.register') }}?source=login&user_type=' + selectedUserType">立即注册</a></p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    new Vue({
        el: '#login-app',
        delimiters: ['[[', ']]'],
        data: {
            selectedUserType: '', // elderly, family, worker
            userId: '',
            password: '',
            rememberMe: false,
            isLoading: false,
            errorMessage: ''
        },
        computed: {
            userTypeText() {
                switch(this.selectedUserType) {
                    case 'elderly': return '老年人';
                    case 'family': return '家属';
                    case 'worker': return '社区工作人员';
                    default: return '';
                }
            },
            idFieldName() {
                switch(this.selectedUserType) {
                    case 'elderly': return '老年人ID';
                    case 'family': return '家属ID';
                    case 'worker': return '工作人员ID';
                    default: return '用户ID';
                }
            },
            iconClass() {
                switch(this.selectedUserType) {
                    case 'elderly': return 'elderly-icon';
                    case 'family': return 'family-icon';
                    case 'worker': return 'worker-icon';
                    default: return '';
                }
            }
        },
        methods: {
            selectUserType(type) {
                this.selectedUserType = type;
                this.errorMessage = '';
            },
            resetUserType() {
                this.selectedUserType = '';
                this.userId = '';
                this.password = '';
                this.errorMessage = '';
            },
            login() {
                this.isLoading = true;
                this.errorMessage = '';
                
                // 准备登录数据
                const loginData = {
                    user_type: this.selectedUserType,
                    user_id: this.userId,
                    password: this.password,
                    remember_me: this.rememberMe
                };
                
                // 发送登录请求到后端API
                fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(loginData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 登录成功，跳转到相应页面
                        window.location.href = data.redirect_url;
                    } else {
                        // 登录失败
                        this.errorMessage = data.error || '登录失败，请重试';
                    }
                })
                .catch(error => {
                    this.errorMessage = '服务器错误，请稍后重试';
                    console.error('登录错误:', error);
                })
                .finally(() => {
                    this.isLoading = false;
                });
            }
        },
        mounted() {
            const urlParams = new URLSearchParams(window.location.search);
            const userType = urlParams.get('user_type');
            if (userType) {
                this.selectUserType(userType);
            }
        }
    });
</script>
{% endblock %}