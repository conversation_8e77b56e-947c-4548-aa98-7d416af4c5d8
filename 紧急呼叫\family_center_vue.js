new Vue({
    el: '#monitor-app',
    delimiters: ['[[', ']]'],
    data: {
        currentMenu: '实时体征看板',
        currentUser: null,
        currentElderly: null,
        boundElderlyList: [],
        // 图表对象
        charts: {
            trendChart: null,
            activityRadar: null,
            contactGraph: null
        },
        // 地图对象
        map: null,
        // 当前位置信息
        currentLocationInfo: {
            name: '加载中...',
            address: '获取地址中...',
            lastUpdate: '',
            coordinates: ''
        },
        // 当前选中的老人索引
        selectedElderlyIndex: 0,

        // 紧急事件相关数据
        emergencyEvents: [],
        emergencyStats: {
            pending: 0,
            partial: 0,
            resolved: 0,
            total: 0
        },
        responding: false,
        emergencyPollingInterval: null
    },
    mounted() {
        console.log('Vue应用已挂载');

        // 获取当前用户信息和绑定的老人列表
        this.getCurrentUser();

        // 开始轮询紧急事件
        this.startEmergencyPolling();

        // 添加watch监听当前菜单变化，以便在菜单切换时初始化对应的图表
        this.$watch('currentMenu', (newMenu) => {
            console.log('菜单切换到:', newMenu);
            // 在组件更新后初始化相应的图表
            this.$nextTick(() => {
                switch(newMenu) {
                    case '历史趋势分析':
                        this.initTrendChart();
                        break;
                    case '活动行为分析':
                        this.initActivityCharts();
                        break;
                    case '实时定位监控':
                        this.initMap();
                        break;
                    case '应急响应中心':
                        console.log('加载应急响应中心');
                        this.loadEmergencyEvents();
                        break;
                }
            });
        });
    },

    beforeDestroy() {
        // 清理轮询
        if (this.emergencyPollingInterval) {
            clearInterval(this.emergencyPollingInterval);
        }
    },
    methods: {
        selectMenu(menu) {
            this.currentMenu = menu;
        },
        getCurrentUser() {
            console.log('获取当前用户信息...');
            axios.get('/api/auth/current_user')
                .then(response => {
                    console.log('用户认证响应:', response.data);
                    if (response.data.logged_in) {
                        this.currentUser = {
                            id: response.data.user_id,
                            name: response.data.name,
                            type: response.data.user_type
                        };
                        console.log('用户已登录:', this.currentUser);
                        this.getBoundElderlyList();
                    } else {
                        console.log('用户未登录');
                    }
                })
                .catch(error => {
                    console.error('获取用户信息失败:', error);
                });
        },
        getBoundElderlyList() {
            axios.get(`/api/family/elderly/${this.currentUser.id}`)
                .then(response => {
                    if (response.data.elderly_list) {
                        this.boundElderlyList = response.data.elderly_list;
                        // 设置当前选中的老人为主绑定老人或第一个老人
                        const primaryElderly = this.boundElderlyList.find(e => e.is_primary);
                        if (primaryElderly) {
                            this.currentElderly = primaryElderly;
                            this.selectedElderlyIndex = this.boundElderlyList.findIndex(e => e.is_primary);
                        } else if (this.boundElderlyList.length > 0) {
                            this.currentElderly = this.boundElderlyList[0];
                            this.selectedElderlyIndex = 0;
                        }

                        if (this.currentElderly) {
                            this.loadElderlyLocationInfo(this.currentElderly.user_id);
                        }
                    }
                })
                .catch(error => {
                    console.error('获取绑定老人列表失败:', error);
                });
        },
        loadElderlyData(elderlyId) {
            // 加载该老人的数据，更新页面内容
            console.log(`加载老人ID ${elderlyId} 的数据`);

            // 根据当前选项卡初始化相应的图表
            this.$nextTick(() => {
                switch(this.currentMenu) {
                    case '历史趋势分析':
                        this.initTrendChart();
                        break;
                    case '活动行为分析':
                        this.initActivityCharts();
                        break;
                    case '实时定位监控':
                        this.initMap();
                        break;
                }
            });
        },

        // 初始化历史趋势图表
        initTrendChart() {
            if (document.getElementById('trendChart')) {
                // 清除旧图表
                if (this.charts.trendChart) {
                    this.charts.trendChart.destroy();
                }

                const ctx = document.getElementById('trendChart').getContext('2d');
                this.charts.trendChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                        datasets: [
                            {
                                label: '收缩压',
                                data: [145, 142, 138, 146, 150, 142, 140],
                                borderColor: '#1765d5',
                                backgroundColor: 'rgba(23, 101, 213, 0.1)',
                                borderWidth: 2,
                                fill: true,
                                tension: 0.3
                            },
                            {
                                label: '舒张压',
                                data: [92, 88, 85, 90, 93, 87, 85],
                                borderColor: '#5B8FF9',
                                backgroundColor: 'rgba(91, 143, 249, 0.1)',
                                borderWidth: 2,
                                fill: true,
                                tension: 0.3
                            },
                            {
                                label: '空腹血糖',
                                data: [7.2, 7.4, 7.1, 7.6, 7.8, 7.5, 7.3],
                                borderColor: '#ff7875',
                                backgroundColor: 'rgba(255, 120, 117, 0.1)',
                                borderWidth: 2,
                                fill: true,
                                tension: 0.3,
                                yAxisID: 'y1'
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                title: {
                                    display: true,
                                    text: '血压 (mmHg)'
                                }
                            },
                            y1: {
                                position: 'right',
                                title: {
                                    display: true,
                                    text: '血糖 (mmol/L)'
                                },
                                grid: {
                                    drawOnChartArea: false
                                }
                            }
                        },
                        plugins: {
                            title: {
                                display: true,
                                text: '近7天健康指标趋势',
                                font: {
                                    size: 16
                                }
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false
                            },
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }
        },

        // 初始化活动行为分析图表
        initActivityCharts() {
            // 初始化活动雷达图
            if (document.getElementById('activityRadar')) {
                if (this.charts.activityRadar) {
                    this.charts.activityRadar.destroy();
                }

                const radarCtx = document.getElementById('activityRadar').getContext('2d');
                this.charts.activityRadar = new Chart(radarCtx, {
                    type: 'radar',
                    data: {
                        labels: ['家人聚会', '社区活动', '健康锻炼', '医疗就诊', '生活自理', '朋友交往'],
                        datasets: [{
                            label: '参与度评分',
                            data: [85, 40, 55, 75, 90, 45],
                            backgroundColor: 'rgba(23, 101, 213, 0.2)',
                            borderColor: '#1765d5',
                            pointBackgroundColor: '#1765d5',
                            pointBorderColor: '#fff',
                            pointHoverBackgroundColor: '#fff',
                            pointHoverBorderColor: '#1765d5'
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: '老人活动参与度雷达图',
                                font: {
                                    size: 16
                                }
                            }
                        },
                        scales: {
                            r: {
                                angleLines: {
                                    display: true
                                },
                                min: 0,
                                max: 100
                            }
                        }
                    }
                });
            }

            // 初始化社交关系图
            if (document.getElementById('contactGraph')) {
                if (this.charts.contactGraph) {
                    this.charts.contactGraph.destroy();
                }

                const graphCtx = document.getElementById('contactGraph').getContext('2d');
                this.charts.contactGraph = new Chart(graphCtx, {
                    type: 'polarArea',
                    data: {
                        labels: ['家庭成员', '邻居', '社区医生', '朋友', '社区工作者'],
                        datasets: [{
                            label: '互动频率',
                            data: [80, 45, 25, 35, 60],
                            backgroundColor: [
                                'rgba(23, 101, 213, 0.7)',
                                'rgba(91, 143, 249, 0.7)',
                                'rgba(96, 183, 96, 0.7)',
                                'rgba(250, 173, 20, 0.7)',
                                'rgba(255, 120, 117, 0.7)'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: '老人社交互动频率分析',
                                font: {
                                    size: 16
                                }
                            },
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }
        },

        // 初始化地图
        async initMap() {
            // 确保地图容器存在
            if (document.getElementById('amapRealtime')) {
                // 创建地图实例
                if (!this.map) {
                    this.map = new AMap.Map('amapRealtime', {
                        zoom: 14,
                        center: [120.1626, 30.2729]  // 默认杭州位置
                    });

                    // 添加工具条控件
                    this.map.plugin(["AMap.ToolBar"], function() {
                        const toolBar = new AMap.ToolBar();
                        this.map.addControl(toolBar);
                    });
                }

                // 加载老人的实时位置
                await this.loadElderlyLocation();
            }
        },

        async loadElderlyLocation() {
            try {
                // 使用当前选中的老人
                if (!this.currentElderly) {
                    console.warn('未选择老人');
                    this.showDefaultLocation();
                    return;
                }

                const response = await axios.get(`/api/family/elderly/${this.currentUser.id}/location/${this.currentElderly.user_id}`);

                if (response.data && response.data.coordinates) {
                    this.updateMapWithElderlyLocation(response.data);
                } else {
                    console.warn('未获取到老人位置信息');
                    this.showDefaultLocation();
                }
            } catch (error) {
                console.error('加载老人位置失败:', error);
                this.showDefaultLocation();
            }
        },

        showDefaultLocation() {
            // 显示默认位置
            const defaultLng = 120.1626;
            const defaultLat = 30.2729;

            this.map.setCenter([defaultLng, defaultLat]);

            const marker = new AMap.Marker({
                position: [defaultLng, defaultLat],
                title: '默认位置',
                icon: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png'
            });

            this.map.add(marker);

            // 更新位置信息显示
            this.currentLocationInfo = {
                name: '位置信息获取中...',
                address: '杭州市西湖区（默认位置）',
                lastUpdate: new Date().toLocaleString(),
                coordinates: `${defaultLat}, ${defaultLng}`
            };
        },

        getAddressFromCoordinates(lng, lat, elderlyName) {
            // 使用高德地图API进行逆地理编码
            if (typeof AMap !== 'undefined') {
                AMap.plugin('AMap.Geocoder', () => {
                    const geocoder = new AMap.Geocoder();
                    geocoder.getAddress([lng, lat], (status, result) => {
                        if (status === 'complete' && result.info === 'OK') {
                            const address = result.regeocode.formattedAddress;

                            // 更新位置信息
                            this.currentLocationInfo = {
                                name: elderlyName,
                                address: address,
                                lastUpdate: new Date().toLocaleString(),
                                coordinates: `${lat}, ${lng}`
                            };

                            // 添加信息窗口
                            const infoWindow = new AMap.InfoWindow({
                                content: `
                                    <div style="padding: 10px;">
                                        <h6>${elderlyName}的位置</h6>
                                        <p><strong>地址:</strong> ${address}</p>
                                        <p><strong>坐标:</strong> ${lat}, ${lng}</p>
                                        <p><strong>更新时间:</strong> ${new Date().toLocaleString()}</p>
                                    </div>
                                `,
                                offset: new AMap.Pixel(0, -30)
                            });

                            // 点击标记显示信息窗口
                            const markers = this.map.getAllOverlays('marker');
                            if (markers.length > 0) {
                                markers[0].on('click', () => {
                                    infoWindow.open(this.map, markers[0].getPosition());
                                });
                            }
                        } else {
                            console.error('地址解析失败');
                            this.currentLocationInfo = {
                                name: elderlyName,
                                address: '地址解析失败',
                                lastUpdate: new Date().toLocaleString(),
                                coordinates: `${lat}, ${lng}`
                            };
                        }
                    });
                });
            }
        },

        updateLocationInfo(locationData) {
            // 更新位置信息显示
            this.currentLocationInfo = {
                name: locationData.name,
                address: '正在获取地址...',
                lastUpdate: locationData.last_sync_time || new Date().toLocaleString(),
                coordinates: locationData.gps_location
            };
        },

        // 切换老人
        switchElderly() {
            if (this.boundElderlyList.length > 0) {
                this.currentElderly = this.boundElderlyList[this.selectedElderlyIndex];
                this.loadElderlyLocationInfo(this.currentElderly.user_id);

                // 如果当前在位置监控页面，更新地图
                if (this.currentMenu === '实时定位监控') {
                    this.initMap();
                }
            }
        },

        // 加载老人位置信息
        loadElderlyLocationInfo(elderlyId) {
            if (!this.currentUser || !elderlyId) return;

            axios.get(`/api/family/elderly/${this.currentUser.id}/location/${elderlyId}`)
                .then(response => {
                    const data = response.data;
                    this.currentLocationInfo = {
                        name: data.name,
                        address: data.address,
                        lastUpdate: data.last_sync_time || '位置信息暂未更新',
                        coordinates: data.coordinates || '暂无坐标信息'
                    };

                    // 如果当前在位置监控页面，更新地图
                    if (this.currentMenu === '实时定位监控' && this.map) {
                        this.updateMapWithElderlyLocation(data);
                    }
                })
                .catch(error => {
                    console.error('获取老人位置信息失败:', error);
                    this.currentLocationInfo = {
                        name: this.currentElderly ? this.currentElderly.name : '未知',
                        address: '位置信息获取失败',
                        lastUpdate: '获取失败',
                        coordinates: '暂无坐标信息'
                    };
                });
        },

        // 更新地图显示老人位置
        updateMapWithElderlyLocation(locationData) {
            if (!this.map) return;

            let lng, lat;

            // 解析坐标
            if (locationData.coordinates && locationData.coordinates.includes(',')) {
                [lng, lat] = locationData.coordinates.split(',').map(Number);
            } else {
                // 使用默认坐标（杭州市中心）
                lng = 120.1626;
                lat = 30.2729;
            }

            // 清除现有标记
            this.map.clearMap();

            // 更新地图中心
            this.map.setCenter([lng, lat]);

            // 添加老人位置标记
            const marker = new AMap.Marker({
                position: [lng, lat],
                title: `${locationData.name}的当前位置`,
                icon: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png'
            });

            this.map.add(marker);

            // 添加安全围栏
            const circle = new AMap.Circle({
                center: [lng, lat],
                radius: 500,  // 500米安全围栏
                fillColor: 'rgba(23, 101, 213, 0.1)',
                strokeColor: '#1765d5',
                strokeWeight: 2
            });

            this.map.add(circle);

            // 添加信息窗口
            const infoWindow = new AMap.InfoWindow({
                content: `
                    <div style="padding: 10px;">
                        <h6>${locationData.name}的位置</h6>
                        <p><strong>地址:</strong> ${locationData.address}</p>
                        <p><strong>坐标:</strong> ${lat}, ${lng}</p>
                        <p><strong>更新时间:</strong> ${locationData.last_sync_time || '暂未更新'}</p>
                    </div>
                `,
                offset: new AMap.Pixel(0, -30)
            });

            // 点击标记显示信息窗口
            marker.on('click', () => {
                infoWindow.open(this.map, marker.getPosition());
            });
        },

        refreshLocation() {
            // 刷新位置信息
            if (this.currentElderly) {
                this.loadElderlyLocationInfo(this.currentElderly.user_id);
            }
        },

        // 紧急事件相关方法
        initEmergencyNotificationModal() {
            const modalEl = document.getElementById('emergencyNotificationModal');
            if (modalEl) {
                this.emergencyNotificationModal = new bootstrap.Modal(modalEl);
            }
        },

        startEmergencyPolling() {
            // 立即加载一次
            this.loadEmergencyEvents();

            // 每30秒轮询一次
            this.emergencyPollingInterval = setInterval(() => {
                this.loadEmergencyEvents();
            }, 30000);
        },

        async loadEmergencyEvents() {
            console.log('加载紧急事件，当前用户:', this.currentUser);
            if (!this.currentUser) {
                console.log('用户未登录，跳过加载事件');
                return;
            }

            try {
                const response = await axios.get('/api/emergency_event/active', {
                    params: {
                        user_type: 'family',
                        user_id: this.currentUser.id
                    }
                });

                console.log('紧急事件API响应:', response.data);

                if (response.data.events) {
                    const newEvents = response.data.events;
                    console.log('获取到事件:', newEvents.length, '个');

                    // 检查是否有新的紧急事件
                    const hasNewEvents = newEvents.some(newEvent =>
                        !this.emergencyEvents.find(existingEvent =>
                            existingEvent.event_id === newEvent.event_id
                        )
                    );

                    this.emergencyEvents = newEvents;
                    this.updateEmergencyStats();
                    console.log('事件列表已更新，统计:', this.emergencyStats);

                    // 如果有新事件且不在应急响应中心页面，显示通知
                    if (hasNewEvents && this.currentMenu !== '应急响应中心' && newEvents.length > 0) {
                        this.showEmergencyNotification(newEvents[0]);
                    }
                } else {
                    console.log('API响应中没有events字段');
                }
            } catch (error) {
                console.error('加载紧急事件失败:', error);
            }
        },

        updateEmergencyStats() {
            this.emergencyStats = {
                pending: this.emergencyEvents.filter(e => e.status === '待处理').length,
                partial: this.emergencyEvents.filter(e => e.status === '部分响应').length,
                resolved: this.emergencyEvents.filter(e => e.status === '已处理').length,
                total: this.emergencyEvents.length
            };
        },

        showEmergencyNotification(event) {
            this.currentEmergencyEvent = event;
            this.responseNote = '';

            if (this.emergencyNotificationModal) {
                this.emergencyNotificationModal.show();

                // 播放警报音
                this.playEmergencyAlert();

                // 初始化地图
                this.$nextTick(() => {
                    this.initEmergencyNotificationMap();
                });
            }
        },

        async respondToEmergency() {
            if (!this.currentEmergencyEvent || this.responding) return;

            this.responding = true;

            try {
                const response = await axios.post('/api/emergency_event/respond', {
                    event_id: this.currentEmergencyEvent.event_id,
                    user_type: 'family',
                    user_id: this.currentUser.id,
                    response_note: this.responseNote
                });

                if (response.data.success) {
                    alert('响应成功！');

                    // 关闭模态窗口
                    if (this.emergencyNotificationModal) {
                        this.emergencyNotificationModal.hide();
                    }

                    // 刷新事件列表
                    this.loadEmergencyEvents();
                } else {
                    alert('响应失败：' + response.data.error);
                }
            } catch (error) {
                console.error('响应紧急事件失败:', error);
                alert('网络错误，请重试');
            } finally {
                this.responding = false;
            }
        },

        refreshEmergencyEvents() {
            this.loadEmergencyEvents();
        },

        viewEventDetails(event) {
            // 显示事件详情
            this.showEmergencyNotification(event);
        },

        showEmergencyModal(event) {
            console.log('Vue方法被调用，事件数据:', event);

            // 调用全局函数显示模态窗口
            if (typeof window.showEmergencyModal === 'function') {
                window.showEmergencyModal(event);
            } else {
                console.error('全局函数 showEmergencyModal 未定义，使用Vue方法');
                // 回退到Vue方法
                this.showEmergencyNotification(event);
            }
        },

        getEmergencyTypeText(type) {
            const typeMap = {
                'medical': '医疗紧急',
                'fall': '跌倒求助',
                'help': '其他求助'
            };
            return typeMap[type] || type;
        },

        getEmergencyTypeBadgeClass(type) {
            const classMap = {
                'medical': 'bg-danger',
                'fall': 'bg-warning',
                'help': 'bg-info'
            };
            return classMap[type] || 'bg-secondary';
        },

        getStatusText(status) {
            const statusMap = {
                '待处理': '待处理',
                '部分响应': '部分响应',
                '已处理': '已处理'
            };
            return statusMap[status] || status;
        },

        getStatusBadgeClass(status) {
            const classMap = {
                '待处理': 'bg-danger',
                '部分响应': 'bg-warning',
                '已处理': 'bg-success'
            };
            return classMap[status] || 'bg-secondary';
        },

        playEmergencyAlert() {
            const audio = document.getElementById('emergencyNotificationAudio');
            if (audio) {
                audio.play().catch(e => console.error('无法播放警报音:', e));
            }
        },

        initEmergencyNotificationMap() {
            if (!this.currentEmergencyEvent || !this.currentEmergencyEvent.location) return;

            const mapContainer = document.getElementById('emergencyNotificationMap');
            if (!mapContainer) return;

            try {
                const [lat, lng] = this.currentEmergencyEvent.location.split(',').map(Number);

                const map = new AMap.Map('emergencyNotificationMap', {
                    zoom: 15,
                    center: [lng, lat]
                });

                // 添加标记
                const marker = new AMap.Marker({
                    position: [lng, lat],
                    title: '紧急事件位置',
                    icon: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png'
                });

                map.add(marker);

                // 添加紧急区域圆圈
                const circle = new AMap.Circle({
                    center: [lng, lat],
                    radius: 100,
                    fillColor: 'rgba(255, 77, 79, 0.3)',
                    strokeColor: '#FF4D4F',
                    strokeWeight: 2
                });

                map.add(circle);
            } catch (error) {
                console.error('初始化紧急事件地图失败:', error);
            }
        },



        // 响应紧急事件
        async respondToEmergency(event) {
            console.log('点击响应按钮，事件:', event);
            console.log('当前用户:', this.currentUser);
            console.log('响应状态:', this.responding);

            if (this.responding) {
                console.log('正在响应中，忽略点击');
                return;
            }

            if (!this.currentUser) {
                console.error('用户未登录');
                alert('请先登录');
                return;
            }

            // 确认对话框
            const confirmed = confirm(`确定要响应紧急事件吗？\n\n老人：${event.elderly_name}\n类型：${this.getEmergencyTypeText(event.emergency_type)}\n时间：${this.formatDateTime(event.created_at)}\n\n响应后事件将标记为"已处理"`);

            if (!confirmed) {
                console.log('用户取消响应');
                return;
            }

            this.responding = true;
            console.log('开始响应事件...');

            try {
                const response = await axios.post('/api/emergency_event/respond', {
                    event_id: event.event_id,
                    user_type: 'family',
                    user_id: this.currentUser.id,
                    response_note: '家属已收到通知，事件已处理。'
                });

                if (response.data.success) {
                    // 显示成功消息
                    alert('✅ 响应成功！事件已标记为"已处理"。');

                    // 立即更新本地数据
                    const eventIndex = this.emergencyEvents.findIndex(e => e.event_id === event.event_id);
                    if (eventIndex !== -1) {
                        // 更新事件状态 - 家属响应直接标记为已处理
                        this.emergencyEvents[eventIndex].family_responded = true;
                        this.emergencyEvents[eventIndex].family_responder_id = this.currentUser.id;
                        this.emergencyEvents[eventIndex].family_response_time = new Date().toISOString();
                        this.emergencyEvents[eventIndex].status = '已处理';

                        console.log('✅ 本地数据已更新:', this.emergencyEvents[eventIndex]);
                    }

                    // 立即更新统计数据
                    this.updateEmergencyStats();
                    console.log('✅ 统计数据已更新:', this.emergencyStats);

                    // 延迟刷新事件列表确保数据同步
                    setTimeout(() => {
                        this.loadEmergencyEvents();
                    }, 1000);

                } else {
                    alert('❌ 响应失败: ' + response.data.error);
                }
            } catch (error) {
                console.error('响应紧急事件失败:', error);
                alert('❌ 网络错误，请重试: ' + error.message);
            } finally {
                this.responding = false;
            }
        },

        // 刷新紧急事件列表
        refreshEmergencyEvents() {
            this.loadEmergencyEvents();
        },

        // 查看事件详情
        viewEventDetails(event) {
            alert(`事件详情：\n\n事件ID: ${event.event_id}\n老人姓名: ${event.elderly_name}\n紧急类型: ${this.getEmergencyTypeText(event.emergency_type)}\n发生时间: ${this.formatDateTime(event.created_at)}\n位置: ${event.address || '位置获取中...'}\n状态: ${this.getStatusText(event.status)}\n\n家属响应: ${event.family_responded ? '已响应' : '未响应'}\n工作人员响应: ${event.worker_responded ? '已响应' : '未响应'}`);
        },

        // 获取紧急类型文本
        getEmergencyTypeText(type) {
            const typeMap = {
                'medical': '医疗紧急',
                'fall': '跌倒事故',
                'fire': '火灾报警',
                'security': '安全威胁',
                'other': '其他紧急'
            };
            return typeMap[type] || '未知类型';
        },

        // 获取紧急类型样式类
        getEmergencyTypeBadgeClass(type) {
            const classMap = {
                'medical': 'bg-danger',
                'fall': 'bg-warning',
                'fire': 'bg-danger',
                'security': 'bg-info',
                'other': 'bg-secondary'
            };
            return classMap[type] || 'bg-secondary';
        },

        // 获取状态文本
        getStatusText(status) {
            const statusMap = {
                '待处理': '待处理',
                '部分响应': '部分响应',
                '已处理': '已处理'
            };
            return statusMap[status] || status;
        },

        // 获取状态样式类
        getStatusBadgeClass(status) {
            const classMap = {
                '待处理': 'bg-danger',
                '部分响应': 'bg-warning',
                '已处理': 'bg-success'
            };
            return classMap[status] || 'bg-secondary';
        },

        // 格式化日期时间
        formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '未知时间';

            try {
                const date = new Date(dateTimeStr);
                return date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
            } catch (error) {
                return dateTimeStr;
            }
        }
    }
});