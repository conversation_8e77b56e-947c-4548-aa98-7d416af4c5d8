{% extends "base.html" %}

{% block title %}老年人中心 - 居家养老健康管理系统{% endblock %}

{% block head %}
<style>
    /* 整体布局样式 */
    .elderly-container {
        display: flex;
        min-height: calc(100vh - 56px);
    }

    /* 侧边导航栏样式 */
    .side-nav {
        width: 260px;
        background-color: #f8f9fa;
        border-right: 1px solid #dee2e6;
        transition: all 0.3s;
    }

    /* 侧边栏折叠时的样式 */
    .side-nav.collapsed {
        width: 60px;
    }

    .side-nav.collapsed .nav-text {
        display: none;
    }

    .side-nav.collapsed .nav-item {
        padding: 0.75rem 0;
        text-align: center;
    }

    /* 主内容区样式 */
    .main-content {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
    }

    /* 健康科普中心样式 */
    .sticky-sidebar {
        position: sticky;
        top: 20px;
        height: calc(100vh - 100px);
        overflow-y: auto;
    }

    .scrollable-content {
        max-height: calc(100vh - 100px);
        overflow-y: auto;
    }

    .category-pill {
        display: inline-flex;
        align-items: center;
        padding: 8px 15px;
        margin: 5px;
        border-radius: 20px;
        background-color: #f8f9fa;
        cursor: pointer;
        transition: all 0.2s;
    }

    .category-pill:hover, .category-pill.active {
        background-color: #0d6efd;
        color: white;
    }

    /* 侧边栏导航项 */
    .nav-item {
        padding: 0.75rem 1rem;
        display: flex;
        align-items: center;
        color: #495057;
        border-radius: 0.25rem;
        margin: 0.25rem 0.5rem;
        cursor: pointer;
        transition: all 0.2s;
    }

    .nav-item:hover, .nav-item.active {
        background-color: rgba(13, 110, 253, 0.1);
        color: #0d6efd;
    }

    .nav-item i {
        font-size: 1.25rem;
        min-width: 28px;
    }

    .nav-text {
        margin-left: 10px;
        font-size: 1rem;
    }

    .nav-text.menu-large {
        font-size: 1.18rem;
        font-weight: 600;
        letter-spacing: 1px;
    }

    /* 健康看板样式 */
    .health-dashboard .card {
        height: 100%;
        border-radius: 10px;
        transition: all 0.3s;
    }

    .health-dashboard .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    /* 紧急呼叫按钮样式 */
    .emergency-call-btn {
        background-color: #FF4D4F;
        color: white;
        padding: 40px;
        border-radius: 12px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s;
        box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
    }

    .emergency-call-btn:hover {
        background-color: #ff2b2e;
        transform: scale(1.05);
    }

    .emergency-call-btn i {
        font-size: 2.5rem;
        margin-bottom: 10px;
    }

    /* 设备连接状态卡片 */
    .device-card {
        border-radius: 10px;
        overflow: hidden;
        transition: all 0.3s;
    }

    .device-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .device-icon {
        font-size: 2rem;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* 健康仪表盘 */
    .gauge-container {
        position: relative;
        width: 160px;
        height: 160px;
    }

    /* 悬浮助手 */
    .floating-assistant {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 60px;
        height: 60px;
        background-color: #5B8FF9;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        cursor: pointer;
        z-index: 1000;
        transition: all 0.3s;
    }

    .floating-assistant:hover {
        transform: scale(1.1);
    }

    /* 健康日历 */
    .health-calendar .reminder-item {
        padding: 10px;
        border-radius: 8px;
        margin-bottom: 10px;
        border-left: 4px solid;
    }

    .reminder-medication { border-left-color: #5B8FF9; background-color: rgba(91, 143, 249, 0.1); }
    .reminder-checkup { border-left-color: #6DD400; background-color: rgba(109, 212, 0, 0.1); }
    .reminder-measurement { border-left-color: #FF4D4F; background-color: rgba(255, 77, 79, 0.1); }

    /* 订餐系统样式 */
    .food-menu-container {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
    }

    .food-item {
        border-radius: 12px;
        overflow: hidden;
        transition: all 0.3s;
        cursor: pointer;
        box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    }

    .food-item:hover {
        transform: translateY(-5px) scale(1.02);
        box-shadow: 0 8px 20px rgba(0,0,0,0.15);
    }

    .food-img-container {
        position: relative;
        height: 180px;
        overflow: hidden;
    }

    .food-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s;
    }

    .food-item:hover .food-img {
        transform: scale(1.1);
    }

    .nutrition-tag {
        display: inline-block;
        font-size: 0.75rem;
        padding: 2px 8px;
        border-radius: 12px;
        margin-right: 5px;
        margin-bottom: 5px;
    }

    .tag-protein { background-color: #f1c40f; color: #000; }
    .tag-fiber { background-color: #2ecc71; color: #fff; }
    .tag-vitamin { background-color: #3498db; color: #fff; }
    .tag-lowfat { background-color: #9b59b6; color: #fff; }
    .tag-lowsugar { background-color: #e74c3c; color: #fff; }

    .cart-item {
        border-bottom: 1px solid #eee;
        padding: 10px 0;
    }

    .food-preference-item {
        cursor: pointer;
        padding: 10px;
        border-radius: 8px;
        margin-bottom: 8px;
        transition: all 0.2s;
    }

    .food-preference-item:hover {
        background-color: rgba(91, 143, 249, 0.1);
    }

    .food-preference-item.active {
        background-color: rgba(91, 143, 249, 0.2);
        border-left: 3px solid #5B8FF9;
    }

    /* 个人档案样式 */
    .health-record-container {
        display: flex;
        gap: 20px;
        font-size: 1.2rem; /* 增加整体字体大小 */
    }

    .health-timeline {
        min-width: 300px;
        max-width: 300px;
        border-right: 1px solid #dee2e6;
        transition: all 0.3s ease;
    }

    .health-timeline.collapsed {
        min-width: 50px;
        max-width: 50px;
        overflow: hidden;
    }

    .health-timeline.collapsed .timeline-content {
        display: none;
    }

    .health-timeline.collapsed .timeline-toggle-btn {
        transform: rotate(180deg);
    }

    .timeline-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 15px;
    }

    .vertical-text {
        writing-mode: vertical-lr;
        /* transform: rotate(180deg); */
        text-align: center;
        white-space: nowrap;
        display: none;
    }

    .health-timeline.collapsed .vertical-text {
        display: block;
    }

    .timeline-toggle-btn {
        background: none;
        border: none;
        color: #5B8FF9;
        cursor: pointer;
        transition: transform 0.3s;
    }

    .timeline-item {
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 15px;
        cursor: pointer;
        transition: all 0.2s;
        font-size: 1.15rem; /* 增加时间轴项目字体大小 */
    }

    .timeline-item:hover {
        background-color: rgba(91, 143, 249, 0.1);
    }

    .timeline-item.active {
        background-color: rgba(91, 143, 249, 0.2);
        border-left: 3px solid #5B8FF9;
    }

    .health-data-chart {
        height: 300px;
        width: 100%;
    }

    /* 健康科普样式 */
    .article-card {
        border-radius: 12px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s;
        margin-bottom: 20px;
    }

    .article-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .article-img-container {
        height: 200px;
        overflow: hidden;
    }

    .article-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s;
    }

    .article-card:hover .article-img {
        transform: scale(1.05);
    }

    .article-category {
        display: inline-block;
        font-size: 0.8rem;
        padding: 3px 10px;
        border-radius: 15px;
        margin-right: 8px;
    }

    .category-nutrition { background-color: #f1c40f; color: #000; }
    .category-exercise { background-color: #2ecc71; color: #fff; }
    .category-mental { background-color: #3498db; color: #fff; }
    .category-disease { background-color: #e74c3c; color: #fff; }

    .article-bookmark {
        cursor: pointer;
        transition: all 0.2s;
    }

    .article-bookmark:hover {
        transform: scale(1.2);
    }

    /* 设备管理样式优化 */
    .sync-records-container {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
    }

    .sync-record-item {
        border: 1px solid #eee;
        border-radius: 10px;
        padding: 15px;
        transition: all 0.2s;
    }

    .sync-record-item:hover {
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        transform: translateY(-3px);
    }

    .sync-record-icon {
        font-size: 1.5rem;
        width: 45px;
        height: 45px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* 健康简报样式 */
    .health-brief-card {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        transition: all 0.3s;
    }

    .health-brief-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 18px rgba(0,0,0,0.1);
    }

    .health-brief-card .card-header {
        padding: 12px 15px;
        border-bottom: none;
        font-size: 1.2rem; /* 增加卡片标题字体大小 */
    }

    .abnormal-indicator {
        position: relative;
        height: 120px;
        margin-bottom: 10px;
    }

    .measure-comparison {
        position: relative;
        height: 120px;
        margin-bottom: 10px;
    }

    .monitoring-reminder {
        background-color: rgba(255, 77, 79, 0.05);
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 10px;
        border-left: 3px solid #FF4D4F;
        font-size: 1.1rem; /* 增加提醒内容字体大小 */
    }

    .monitoring-reminder.warning {
        border-left-color: #FAAD14;
        background-color: rgba(250, 173, 20, 0.05);
    }

    .monitoring-reminder h6 {
        color: #FF4D4F;
        margin-bottom: 8px;
        font-size: 1.15rem; /* 增加提醒标题字体大小 */
    }

    .monitoring-reminder.warning h6 {
        color: #FAAD14;
    }

    .countdown-progress {
        height: 6px;
        margin-top: 8px;
        margin-bottom: 5px;
        background-color: rgba(0,0,0,0.05);
    }

    .countdown-progress .progress-bar {
        background-color: #FF4D4F;
    }

    .device-status-indicator {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 5px;
    }

    .device-status-indicator.online {
        background-color: #52C41A;
    }

    .device-status-indicator.offline {
        background-color: #FF4D4F;
    }

    /* 适老化健康科普中心样式 */
    .elderly-edu-container {
        width: 100%;
        max-width: 1200px;
        margin: 0 auto;
        position: relative;
    }

    .smart-nav {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #fff;
        padding: 15px;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        margin-bottom: 25px;
        flex-wrap: wrap;
        gap: 10px;
    }

    .voice-search {
        display: flex;
        align-items: center;
        padding: 12px 20px;
        background-color: #f8f9fa;
        border: none;
        border-radius: 50px;
        color: #333;
        font-size: 1.1rem;
        cursor: pointer;
        transition: all 0.2s;
    }

    .voice-search:hover {
        background-color: #e9ecef;
    }

    .font-control {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .font-control button {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: 2px solid #dee2e6;
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        transition: all 0.2s;
    }

    .font-control button:hover {
        background-color: #f8f9fa;
        border-color: #adb5bd;
    }

    .read-aloud {
        display: flex;
        align-items: center;
        padding: 10px 20px;
        background-color: #5B8FF9;
        border: none;
        border-radius: 50px;
        color: white;
        cursor: pointer;
        transition: all 0.2s;
    }

    .read-aloud:hover {
        background-color: #4a7de0;
    }

    .edu-reading-area {
        width: 90%;
        margin: 0 auto;
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 15px rgba(0,0,0,0.06);
        padding: 30px;
        margin-bottom: 30px;
    }

    .article-title-large {
        font-size: 36px;
        font-weight: bold;
        color: #333;
        margin-bottom: 20px;
        line-height: 1.3;
    }

    .key-points {
        margin-bottom: 25px;
    }

    .key-point {
        background-color: rgba(91, 143, 249, 0.1);
        border-left: 4px solid #5B8FF9;
        padding: 15px;
        border-radius: 0 8px 8px 0;
        margin-bottom: 12px;
        position: relative;
    }

    .key-point-title {
        font-size: 1.1rem;
        font-weight: bold;
        margin-bottom: 5px;
        color: #333;
    }

    .key-point-content {
        color: #555;
        font-size: 1.05rem;
        line-height: 1.6;
    }

    .key-point-audio {
        position: absolute;
        right: 15px;
        top: 15px;
        background: none;
        border: none;
        color: #5B8FF9;
        cursor: pointer;
    }

    .article-content {
        font-size: var(--user-font-size, 22px);
        line-height: 2;
        letter-spacing: 1.2px;
        color: #333;
        margin-bottom: 30px;
    }

    .content-section {
        margin-bottom: 30px;
    }

    .content-section h3 {
        font-size: 1.5rem;
        margin-bottom: 15px;
        color: #333;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
    }

    .content-section p {
        margin-bottom: 15px;
    }

    .float-tools {
        position: fixed;
        right: 30px;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        flex-direction: column;
        gap: 15px;
        z-index: 100;
    }

    .tool-card {
        width: 70px;
        height: 70px;
        border-radius: 15px;
        background-color: white;
        box-shadow: 0 3px 15px rgba(0,0,0,0.1);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s;
    }

    .tool-card:hover {
        transform: scale(1.1);
        box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    }

    .tool-card img {
        width: 28px;
        height: 28px;
        margin-bottom: 5px;
    }

    .tool-card span {
        font-size: 0.7rem;
        text-align: center;
        color: #666;
    }

    .color-mode-switcher {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
    }

    .color-mode {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        cursor: pointer;
        opacity: 0.6;
        transition: all 0.2s;
    }

    .color-mode:hover, .color-mode.active {
        opacity: 1;
        transform: scale(1.1);
    }

    .color-mode-default {
        background-color: #fff;
        border: 2px solid #dee2e6;
    }

    .color-mode-high-contrast {
        background-color: #000;
        border: 2px solid #fff;
    }

    .color-mode-low-vision {
        background-color: #C7E4F2;
        border: 2px solid #1A3658;
    }

    .home-button {
        position: fixed;
        left: 30px;
        bottom: 30px;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: #5B8FF9;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        box-shadow: 0 3px 15px rgba(0,0,0,0.2);
        cursor: pointer;
        z-index: 100;
        transition: all 0.3s;
    }

    .home-button:hover {
        transform: scale(1.1);
        background-color: #4a7de0;
    }

    .article-difficulty {
        display: inline-flex;
        align-items: center;
        margin-bottom: 15px;
        color: #f1c40f;
    }

    .practice-steps {
        background-color: #f8f9fa;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .practice-steps h4 {
        margin-bottom: 15px;
        color: #333;
    }

    .practice-step {
        display: flex;
        align-items: flex-start;
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px dashed #dee2e6;
    }

    .practice-step:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
    }

    .practice-step-number {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #5B8FF9;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 15px;
        flex-shrink: 0;
    }

    .practice-step-content {
        flex: 1;
    }

    .practice-step-action {
        margin-left: 15px;
    }

    .highlight-sentence {
        background-color: yellow;
        padding: 2px 0;
    }

    /* 分类导航栏样式 */
    .category-nav {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 20px;
        justify-content: center;
        align-items: center;
    }

    .category-item {
        padding: 10px 20px;
        background-color: #f8f9fa;
        border-radius: 50px;
        cursor: pointer;
        transition: all 0.2s;
        font-size: 1.1rem;
        display: flex;
        align-items: center;
    }

    .category-item:hover, .category-item.active {
        background-color: #5B8FF9;
        color: white;
    }

    /* 功能面板样式 */
    .function-panel {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        padding: 25px;
        z-index: 1000;
        max-width: 500px;
        width: 90%;
        display: none;
    }

    .function-panel.show {
        display: block;
        animation: fadeIn 0.3s ease;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translate(-50%, -48%); }
        to { opacity: 1; transform: translate(-50%, -50%); }
    }

    .panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #eee;
    }

    .panel-header h4 {
        margin: 0;
        color: #333;
        display: flex;
        align-items: center;
    }

    .panel-close {
        background: none;
        border: none;
        font-size: 1.25rem;
        color: #999;
        cursor: pointer;
        transition: all 0.2s;
    }

    .panel-close:hover {
        color: #333;
        transform: scale(1.1);
    }

    .panel-content {
        max-height: 60vh;
        overflow-y: auto;
    }

    .collection-item {
        display: flex;
        padding: 12px 15px;
        border-radius: 8px;
        margin-bottom: 10px;
        background-color: #f8f9fa;
        transition: all 0.2s;
    }

    .collection-item:hover {
        background-color: rgba(91, 143, 249, 0.1);
    }

    .collection-icon {
        margin-right: 15px;
        color: #5B8FF9;
        font-size: 1.5rem;
    }

    .progress-tree {
        position: relative;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 10px;
        text-align: center;
    }

    .progress-tree img {
        max-width: 100%;
        height: 200px;
    }

    .progress-info {
        margin-top: 15px;
    }

    .medication-list {
        list-style: none;
        padding: 0;
    }

    .medication-item {
        display: flex;
        align-items: center;
        padding: 12px;
        border-radius: 8px;
        margin-bottom: 10px;
        background-color: #f8f9fa;
    }

    .medication-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 1.25rem;
    }

    .question-form {
        margin-top: 15px;
    }

    .question-form textarea {
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 15px;
        resize: none;
    }

    .question-form button {
        background-color: #5B8FF9;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        cursor: pointer;
        transition: all 0.2s;
    }

    .question-form button:hover {
        background-color: #4a7de0;
    }

    .overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0,0,0,0.5);
        z-index: 999;
        display: none;
    }

    .overlay.show {
        display: block;
    }

    /* 紧急呼叫浮动按钮样式 */
    .floating-emergency-btn {
        display: none; /* 隐藏浮动紧急呼叫按钮 */
    }

    /* 侧边栏紧急呼叫按钮样式 */
    .emergency-nav-btn {
        background-color: #FF4D4F;
        color: white !important;
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        margin: 1rem 0.5rem;
        border-radius: 0.25rem;
        cursor: pointer;
        transition: all 0.2s;
        box-shadow: 0 2px 8px rgba(255, 77, 79, 0.4);
    }

    .emergency-nav-btn:hover {
        background-color: #ff2b2e;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(255, 77, 79, 0.6);
    }

    .emergency-nav-btn .emergency-icon {
        margin-right: 10px;
        font-size: 1.5rem;
    }

    .emergency-nav-btn .emergency-text {
        display: flex;
        flex-direction: column;
    }

    .emergency-nav-btn .emergency-title {
        font-size: 1.2rem;
        font-weight: bold;
        line-height: 1.2;
    }

    .emergency-nav-btn .emergency-subtitle {
        font-size: 0.8rem;
        opacity: 0.9;
    }

    /* 紧急呼叫动画 */
    .emergency-calling-animation {
        position: relative;
        width: 100px;
        height: 100px;
        margin: 0 auto;
        background-color: rgba(255, 77, 79, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .emergency-calling-animation::before {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background-color: rgba(255, 77, 79, 0.3);
        animation: pulse-outward 1.5s infinite;
    }

    @keyframes pulse-outward {
        0% {
            transform: scale(0.9);
            opacity: 1;
        }
        100% {
            transform: scale(1.5);
            opacity: 0;
        }
    }

    /* 紧急联系人样式 */
    .contact-item {
        transition: all 0.2s;
    }

    .contact-item:hover {
        background-color: #f8f9fa;
        transform: translateY(-2px);
    }

    .floating-order-btns {
        position: fixed;
        bottom: 120px;
        right: 30px;
        display: flex;
        flex-direction: column;
        gap: 18px;
        z-index: 1200;
    }
    .order-btn {
        width: 64px;
        height: 64px;
        background: #fff;
        border-radius: 50%;
        box-shadow: 0 2px 10px rgba(0,0,0,0.12);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #5B8FF9;
        font-weight: bold;
        font-size: 1rem;
        cursor: pointer;
        border: 2px solid #e9ecef;
        transition: all 0.2s;
    }
    .order-btn:hover {
        background: #5B8FF9;
        color: #fff;
        border-color: #5B8FF9;
    }
    .order-btn span {
        font-size: 0.95rem;
        margin-top: 2px;
    }

    /* 添加GPS定位和地图相关样式 */
    .emergency-map-container {
        height: 300px;
        width: 100%;
        border-radius: 8px;
        overflow: hidden;
        margin-top: 20px;
        border: 1px solid #dee2e6;
    }

    .location-info {
        padding: 12px;
        background-color: #f8f9fa;
        border-radius: 8px;
        margin-top: 15px;
        display: flex;
        align-items: center;
        font-size: 1.1rem;
    }

    .location-info i {
        font-size: 1.5rem;
        margin-right: 10px;
        color: #5B8FF9;
    }

    .emergency-status {
        padding: 15px;
        border-radius: 8px;
        margin: 15px 0;
        background-color: rgba(255, 77, 79, 0.1);
        border-left: 4px solid #FF4D4F;
        font-size: 1.2rem;
    }

    .emergency-contacts {
        margin-top: 20px;
    }

    .contact-item {
        margin-bottom: 15px;
        font-size: 1.1rem;
    }

    .emergency-options {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 15px;
    }

    .emergency-option {
        flex: 1;
        min-width: 120px;
        padding: 15px;
        border-radius: 8px;
        text-align: center;
        background-color: #f8f9fa;
        cursor: pointer;
        transition: all 0.2s;
        font-size: 1.1rem;
    }

    .emergency-option:hover {
        background-color: #e9ecef;
        transform: translateY(-3px);
    }

    .emergency-option i {
        font-size: 1.8rem;
        margin-bottom: 8px;
        display: block;
    }

    .emergency-option.medical {
        color: #FF4D4F;
    }

    .emergency-option.fall {
        color: #FAAD14;
    }

    .emergency-option.help {
        color: #5B8FF9;
    }

    /* 添加紧急呼叫模态框样式 */
    .emergency-modal {
        border-radius: 12px;
        overflow: hidden;
    }

    .emergency-modal .modal-header {
        background-color: #FF4D4F;
        color: white;
        padding: 1.2rem;
    }

    .emergency-modal .modal-footer {
        border-top: none;
        padding: 1rem 1.5rem 1.5rem;
    }

    .emergency-buzzer {
        animation: buzzer-flash 0.5s infinite;
    }

    @keyframes buzzer-flash {
        0%, 100% {
            background-color: #FF4D4F;
        }
        50% {
            background-color: #d73435;
        }
    }

    .emergency-countdown {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background-color: #FF4D4F;
        color: white;
        font-size: 2rem;
        font-weight: bold;
        margin: 0 auto;
        animation: pulse-outward 1.5s infinite;
    }

    /* 紧急呼叫状态指示器 */
    .emergency-status-progress {
        height: 10px;
        border-radius: 5px;
        margin-top: 15px;
        margin-bottom: 5px;
        background-color: #eee;
        overflow: hidden;
    }

    .emergency-status-progress .progress-bar {
        background-color: #FF4D4F;
    }

    /* 紧急呼叫选项样式强化 */
    .emergency-options {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 15px;
    }

    .emergency-option {
        flex: 1;
        min-width: 120px;
        padding: 15px;
        border-radius: 8px;
        text-align: center;
        background-color: #f8f9fa;
        cursor: pointer;
        transition: all 0.2s;
        font-size: 1.1rem;
        border: 2px solid transparent;
    }

    .emergency-option:hover, .emergency-option.active {
        background-color: #e9ecef;
        transform: translateY(-3px);
        border-color: #007bff;
    }

    .emergency-option i {
        font-size: 1.8rem;
        margin-bottom: 8px;
        display: block;
    }

    .emergency-option.medical {
        color: #FF4D4F;
    }

    .emergency-option.fall {
        color: #FAAD14;
    }

    .emergency-option.help {
        color: #5B8FF9;
    }

    /* 新增的紧急呼叫样式 */
    .emergency-countdown {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        background-color: #FF4D4F;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.5rem;
        font-weight: bold;
        margin: 0 auto;
        position: relative;
        animation: pulse-animation 1s infinite;
    }

    @keyframes pulse-animation {
        0% {
            box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.7);
        }
        70% {
            box-shadow: 0 0 0 15px rgba(255, 77, 79, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(255, 77, 79, 0);
        }
    }

    .emergency-map-container {
        height: 250px;
        width: 100%;
        border-radius: 10px;
        overflow: hidden;
        margin: 20px 0;
        border: 1px solid #dee2e6;
    }

    .location-info {
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 8px;
        margin: 15px 0;
        display: flex;
        align-items: center;
    }

    .location-info i {
        font-size: 1.8rem;
        margin-right: 15px;
        color: #FF4D4F;
    }

    .emergency-status-progress {
        height: 8px;
        border-radius: 4px;
        margin: 20px 0;
        background-color: #eee;
        overflow: hidden;
    }

    .emergency-status-progress .progress-bar {
        background-color: #FF4D4F;
    }
</style>
{% endblock %}

{% block content %}
<div id="elderly-app" class="elderly-container">
    <!-- 侧边导航栏 -->
    <div class="side-nav" :class="{ 'collapsed': navCollapsed }">
        <div class="p-3 d-flex justify-content-between align-items-center">
            <h5 v-if="!navCollapsed" class="mb-0">老年人中心</h5>
            <button class="btn btn-sm btn-light" @click="toggleNav">
                <i class="bi" :class="navCollapsed ? 'bi-chevron-right' : 'bi-chevron-left'"></i>
            </button>
        </div>

        <div class="nav-items mt-2">
            <div class="nav-item" :class="{ 'active': currentTab === 'health' }" @click="changeTab('health')">
                <i class="bi bi-heart-pulse-fill"></i>
                <span class="nav-text menu-large">健康看板</span>
            </div>
            <div class="nav-item" :class="{ 'active': currentTab === 'devices' }" @click="changeTab('devices')">
                <i class="bi bi-smartwatch"></i>
                <span class="nav-text menu-large">设备管理</span>
            </div>
            <div class="nav-item" :class="{ 'active': currentTab === 'services' }" @click="changeTab('services')">
                <i class="bi bi-cup-hot-fill"></i>
                <span class="nav-text menu-large">订餐服务</span>
            </div>
            <div class="nav-item" :class="{ 'active': currentTab === 'profile' }" @click="changeTab('profile')">
                <i class="bi bi-person-vcard-fill"></i>
                <span class="nav-text menu-large">个人档案</span>
            </div>
            <div class="nav-item" :class="{ 'active': currentTab === 'health_edu' }" @click="changeTab('health_edu')">
                <i class="bi bi-journal-medical"></i>
                <span class="nav-text menu-large">健康科普</span>
            </div>

            <!-- 新增紧急呼叫按钮 -->
            <div class="emergency-nav-btn" @click="showEmergencyModal()">
                <div class="emergency-icon">
                    <i class="bi bi-telephone-fill"></i>
                </div>
                <div class="emergency-text">
                    <span class="emergency-title">紧急呼叫</span>
                    <span class="emergency-subtitle">Emergency Call</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 健康看板 -->
        <div v-if="currentTab === 'health'" class="health-dashboard">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="fw-bold">健康看板</h2>
                <div>
                    <button class="btn btn-outline-primary me-2">
                        <i class="bi bi-printer me-1"></i> 打印报告
                    </button>
                    <button class="btn btn-outline-secondary">
                        <i class="bi bi-grid-3x3-gap me-1"></i> 自定义布局
                    </button>
                </div>
            </div>

            <!-- 上部区域：运动、睡眠、饮食情况 -->
            <div class="row mb-4">
                <!-- 运动情况 -->
                <div class="col-md-4 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                            <h4 class="mb-0 fw-bold">运动情况</h4>
                            <i class="bi bi-person-walking fs-3"></i>
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-4">
                                <div class="position-relative d-inline-block">
                                    <canvas id="stepsProgressChart" width="180" height="180"></canvas>
                                    <div class="position-absolute top-50 start-50 translate-middle text-center">
                                        <h2 class="mb-0 fs-1">5,246</h2>
                                        <span class="fs-5">今日步数</span>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between border-bottom pb-2 mb-2">
                                <span class="fs-5">目标步数</span>
                                <span class="fs-5 fw-bold">8,000 步</span>
                            </div>
                            <div class="d-flex justify-content-between border-bottom pb-2 mb-2">
                                <span class="fs-5">活动时长</span>
                                <span class="fs-5 fw-bold">45 分钟</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span class="fs-5">消耗热量</span>
                                <span class="fs-5 fw-bold">156 千卡</span>
                            </div>

                            <div class="alert alert-info mt-3">
                                <i class="bi bi-info-circle-fill me-2"></i>
                                <span>建议每天至少走7000步，有助于心血管健康</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 睡眠情况 -->
                <div class="col-md-4 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                            <h4 class="mb-0 fw-bold">睡眠情况</h4>
                            <i class="bi bi-moon-stars-fill fs-3"></i>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="fw-bold mb-0">昨晚睡眠</h5>
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-star-fill text-warning me-1"></i>
                                    <span class="fs-5 fw-bold">7.5</span>
                                    <span class="fs-6 text-muted">/10</span>
                                </div>
                            </div>

                            <div class="sleep-phases mb-3">
                                <canvas id="sleepPhaseChart" height="100"></canvas>
                            </div>

                            <div class="row text-center mt-2">
                                <div class="col-4">
                                    <h2 class="text-primary mb-0">7.2</h2>
                                    <span class="fs-6">睡眠时长(小时)</span>
                                </div>
                                <div class="col-4">
                                    <h2 class="text-success mb-0">93%</h2>
                                    <span class="fs-6">睡眠效率</span>
                                </div>
                                <div class="col-4">
                                    <h2 class="text-info mb-0">2</h2>
                                    <span class="fs-6">起床次数</span>
                                </div>
                            </div>

                            <div class="alert alert-success mt-3">
                                <i class="bi bi-check-circle-fill me-2"></i>
                                <span>深度睡眠时间充足，有助于身体恢复</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 饮食情况 -->
                <div class="col-md-4 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                            <h4 class="mb-0 fw-bold">饮食情况</h4>
                            <i class="bi bi-cup-hot-fill fs-3"></i>
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-3">
                                <canvas id="nutritionChart" height="150"></canvas>
                            </div>

                            <div class="d-flex justify-content-around text-center mb-3">
                                <div>
                                    <h5 class="text-primary mb-1">蛋白质</h5>
                                    <span class="badge bg-primary p-2 fs-6">65g</span>
                                </div>
                                <div>
                                    <h5 class="text-success mb-1">碳水</h5>
                                    <span class="badge bg-success p-2 fs-6">220g</span>
                                </div>
                                <div>
                                    <h5 class="text-warning mb-1">脂肪</h5>
                                    <span class="badge bg-warning p-2 fs-6">45g</span>
                                </div>
                            </div>

                            <div class="border-top pt-3">
                                <h5 class="fw-bold mb-2">今日饮食建议</h5>
                                <ul class="mb-0">
                                    <li class="mb-1">增加膳食纤维摄入，可多吃蔬菜水果</li>
                                    <li class="mb-1">降低钠盐摄入，控制在5g以内</li>
                                    <li>补充钙质，建议饮用一杯低脂牛奶</li>
                                </ul>
                            </div>

                            <div class="alert alert-warning mt-3">
                                <i class="bi bi-droplet-fill me-2"></i>
                                <span>今日水分摄入不足，建议多饮水</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 中部区域：核心健康数据 -->
            <div class="row mb-4">
                <!-- 实时健康数据 -->
                <div class="col-md-6 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-primary text-white">
                            <h4 class="mb-0 fw-bold">实时健康数据</h4>
                        </div>
                        <div class="card-body">
                            <div class="d-flex flex-column align-items-center">
                                <div class="gauge-container mb-3">
                                    <canvas id="heartRateGauge" width="160" height="160"></canvas>
                                    <div class="position-absolute top-50 start-50 translate-middle text-center">
                                        <h2 class="mb-0 fs-1">72</h2>
                                        <span class="fs-5">心率/分钟</span>
                                    </div>
                                </div>

                                <div class="row w-100 mt-3">
                                    <div class="col-6">
                                        <div class="d-flex flex-column align-items-center p-3 border rounded">
                                            <h5 class="text-primary mb-2">血压</h5>
                                            <h3 class="mb-0">120/80</h3>
                                            <span class="text-success fs-5">正常</span>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="d-flex flex-column align-items-center p-3 border rounded">
                                            <h5 class="text-primary mb-2">血糖</h5>
                                            <h3 class="mb-0">5.6</h3>
                                            <span class="text-success fs-5">正常</span>
                                        </div>
                                    </div>
                                </div>

                                <p class="text-center mt-3 fs-5 text-muted">
                                    最后更新时间: 2025-05-06 10:30
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 健康日历 -->
                <div class="col-md-6 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-success text-white">
                            <h4 class="mb-0 fw-bold">用药提醒</h4>
                        </div>
                        <div class="card-body health-calendar">
                            <h5 class="mb-3 fw-bold">今日提醒</h5>

                            <div class="reminder-item reminder-medication p-3 mb-3">
                                <div class="d-flex justify-content-between">
                                    <strong class="fs-5">高血压药物</strong>
                                    <span class="fs-5">08:00</span>
                                </div>
                                <span>服用1粒，饭后服用</span>
                            </div>

                            <div class="reminder-item reminder-measurement p-3 mb-3">
                                <div class="d-flex justify-content-between">
                                    <strong class="fs-5">血压测量</strong>
                                    <span class="fs-5">10:00</span>
                                </div>
                                <span>早晨测量血压，记录数值</span>
                            </div>

                            <div class="reminder-item reminder-medication p-3 mb-3">
                                <div class="d-flex justify-content-between">
                                    <strong class="fs-5">高血压药物</strong>
                                    <span class="fs-5">18:00</span>
                                </div>
                                <span>服用1粒，饭后服用</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 下部区域：异常指标简报 -->
            <div class="card shadow-sm mb-4">
                        <div class="card-header bg-danger text-white">
                            <h4 class="mb-0 fw-bold">异常指标简报板</h4>
                        </div>
                        <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="fw-bold"><i class="bi bi-activity me-2"></i>今日血压波动曲线</h5>
                            <div class="abnormal-indicator">
                                <canvas id="bloodPressureFluctuation"></canvas>
                            </div>
                            <div class="alert alert-danger py-3 fs-5">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                10:45-11:30 期间血压偏高，建议休息
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5 class="mt-md-0 mt-4 fw-bold"><i class="bi bi-bar-chart-fill me-2"></i>最近三次测量对比</h5>
                            <div class="measure-comparison">
                                <canvas id="comparisionChart"></canvas>
                            </div>
                            <div class="text-center mt-3">
                                <button class="btn btn-outline-primary fs-5">
                                    <i class="bi bi-clipboard-data me-1"></i> 查看完整报告
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设备管理 -->
        <div v-if="currentTab === 'devices'" class="device-management">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>设备管理</h2>
                <button class="btn btn-primary">
                    <i class="bi bi-plus-lg me-1"></i> 添加新设备
                </button>
            </div>

            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="device-card card">
                        <div class="card-body">
                            <div class="d-flex">
                                <div class="device-icon bg-primary bg-opacity-10 text-primary me-3">
                                    <i class="bi bi-smartwatch"></i>
                                </div>
                                <div>
                                    <h5 class="mb-1">智能手环</h5>
                                    <p class="mb-2 text-success">
                                        <i class="bi bi-check-circle-fill me-1"></i> 已连接
                                    </p>
                                    <small class="text-muted">最后同步: 5分钟前</small>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer bg-white d-flex justify-content-between">
                            <button class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-arrow-repeat me-1"></i> 同步数据
                            </button>
                            <button class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-gear me-1"></i> 设置
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-4">
                    <div class="device-card card">
                        <div class="card-body">
                            <div class="d-flex">
                                <div class="device-icon bg-success bg-opacity-10 text-success me-3">
                                    <i class="bi bi-activity"></i>
                                </div>
                                <div>
                                    <h5 class="mb-1">血压计</h5>
                                    <p class="mb-2 text-success">
                                        <i class="bi bi-check-circle-fill me-1"></i> 已连接
                                    </p>
                                    <small class="text-muted">最后同步: 2小时前</small>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer bg-white d-flex justify-content-between">
                            <button class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-arrow-repeat me-1"></i> 同步数据
                            </button>
                            <button class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-gear me-1"></i> 设置
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-4">
                    <div class="device-card card">
                        <div class="card-body">
                            <div class="d-flex">
                                <div class="device-icon bg-warning bg-opacity-10 text-warning me-3">
                                    <i class="bi bi-droplet"></i>
                                </div>
                                <div>
                                    <h5 class="mb-1">血糖仪</h5>
                                    <p class="mb-2 text-danger">
                                        <i class="bi bi-x-circle-fill me-1"></i> 未连接
                                    </p>
                                    <small class="text-muted">最后同步: 2天前</small>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer bg-white d-flex justify-content-between">
                            <button class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-bluetooth me-1"></i> 连接
                            </button>
                            <button class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-gear me-1"></i> 设置
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">数据同步记录</h5>
                    <button class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-arrow-clockwise me-1"></i> 刷新
                    </button>
                </div>
                <div class="card-body">
                    <div class="sync-records-container">
                        <div class="sync-record-item">
                            <div class="d-flex mb-3">
                                <div class="sync-record-icon bg-primary bg-opacity-10 text-primary me-3">
                                    <i class="bi bi-smartwatch"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">智能手环数据同步</h6>
                                    <span class="badge bg-success mb-2">成功</span>
                                </div>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <p class="mb-0 small">同步心率、步数、睡眠数据成功</p>
                                </div>
                                <small class="text-muted">10:30</small>
                            </div>
                        </div>

                        <div class="sync-record-item">
                            <div class="d-flex mb-3">
                                <div class="sync-record-icon bg-success bg-opacity-10 text-success me-3">
                                    <i class="bi bi-activity"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">血压计数据同步</h6>
                                    <span class="badge bg-success mb-2">成功</span>
                                </div>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <p class="mb-0 small">血压数据: 120/80 mmHg 正常</p>
                                </div>
                                <small class="text-muted">08:15</small>
                            </div>
                        </div>

                        <!-- <div class="sync-record-item">
                            <div class="d-flex mb-3">
                                <div class="sync-record-icon bg-primary bg-opacity-10 text-primary me-3">
                                    <i class="bi bi-smartwatch"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">智能手环数据同步</h6>
                                    <span class="badge bg-success mb-2">成功</span>
                                </div>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <p class="mb-0 small">同步心率、步数、睡眠数据成功</p>
                                </div>
                                <small class="text-muted">昨天 20:45</small>
                            </div>
                        </div> -->
                    </div>

                    <div class="text-center mt-3">
                        <button class="btn btn-outline-primary">
                            <i class="bi bi-clock-history me-1"></i> 查看更多记录
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 生活服务 - 智能订餐系统 -->
        <div v-if="currentTab === 'services'">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>订餐服务 - 智能订餐系统</h2>
                <!-- 删除这些按钮
                <div>
                    <button class="btn btn-outline-primary me-2">
                        <i class="bi bi-calendar-week me-1"></i> 周餐计划
                    </button>
                    <button class="btn btn-outline-secondary">
                        <i class="bi bi-clock-history me-1"></i> 订餐历史
                    </button>
                </div>
                -->
            </div>

            <div class="row">
                <!-- 左栏：饮食导航塔（25%） -->
                <div class="col-md-3">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="bi bi-signpost-2-fill me-2"></i>饮食导航塔</h5>
                        </div>
                        <div class="card-body">
                            <!-- 本周营养计划 -->
                            <h6 class="mb-3 fw-bold"><i class="bi bi-calendar-check me-2 text-success"></i>本周营养计划</h6>
                            <div class="nav flex-column nav-pills mb-4" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                                <button class="nav-link active text-start mb-2" id="v-pills-recommended-tab" data-bs-toggle="pill" data-bs-target="#v-pills-recommended" type="button" role="tab">
                                    <i class="bi bi-star-fill me-1 text-warning"></i> 医生推荐食谱
                                </button>
                                <button class="nav-link text-start mb-2" id="v-pills-lowsalt-tab" data-bs-toggle="pill" data-bs-target="#v-pills-lowsalt" type="button" role="tab">
                                    <i class="bi bi-heart me-1 text-danger"></i> 低盐健康餐
                                </button>
                                <button class="nav-link text-start mb-2" id="v-pills-diabetic-tab" data-bs-toggle="pill" data-bs-target="#v-pills-diabetic" type="button" role="tab">
                                    <i class="bi bi-droplet me-1 text-primary"></i> 血糖友好餐
                                </button>
                            </div>

                            <!-- 智能筛选器 -->
                            <h6 class="mb-3 fw-bold"><i class="bi bi-funnel-fill me-2 text-primary"></i>忌口闪电过滤</h6>
                            <div class="form-check form-switch mb-2 fs-5">
                                <input class="form-check-input" type="checkbox" id="noSpicy">
                                <label class="form-check-label" for="noSpicy">不吃辣</label>
                            </div>
                            <div class="form-check form-switch mb-2 fs-5">
                                <input class="form-check-input" type="checkbox" id="noSeafood">
                                <label class="form-check-label" for="noSeafood">海鲜过敏</label>
                            </div>
                            <div class="form-check form-switch mb-2 fs-5">
                                <input class="form-check-input" type="checkbox" id="lowSalt">
                                <label class="form-check-label" for="lowSalt">低盐饮食</label>
                            </div>
                            <div class="form-check form-switch mb-4 fs-5">
                                <input class="form-check-input" type="checkbox" id="lowSugar">
                                <label class="form-check-label" for="lowSugar">低糖饮食</label>
                            </div>

                            <!-- 应急通道 -->
                            <h6 class="mb-3 fw-bold"><i class="bi bi-shield-plus me-2 text-danger"></i>应急通道</h6>
                            <button class="btn btn-danger btn-lg w-100 py-3 mb-3">
                                <i class="bi bi-cup-straw me-2"></i> 流食送餐
                                <div class="small">发烧/牙痛/咀嚼困难</div>
                            </button>

                            <div class="alert alert-info mt-4">
                                <i class="bi bi-info-circle-fill me-2"></i>
                                <small>根据您的血压数据，建议本周采用低盐健康饮食方案</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 中右动态区（75%） -->
                <div class="col-md-9">
                    <div class="tab-content" id="v-pills-tabContent">
                        <!-- 初始态：菜品探索广场 -->
                        <div class="tab-pane fade show active" id="v-pills-recommended" role="tabpanel">
                            <div class="card shadow-sm mb-4">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0 fs-3">今日推荐菜品</h5>
                                    <div>
                                        <button class="btn btn-lg btn-outline-primary me-2">
                                            <i class="bi bi-mic-fill me-1"></i> 语音点餐
                                        </button>
                                        <div class="btn-group">
                                            <button class="btn btn-lg btn-outline-success active">午餐</button>
                                            <button class="btn btn-lg btn-outline-success">晚餐</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="food-menu-container">
                                        <!-- 菜品1 -->
                                        <div class="food-item" @click="showFoodDetail(1)">
                                            <div class="food-img-container">
                                                <img src="/static/images/fish.png" class="food-img" alt="清蒸鲈鱼">
                                                <span class="position-absolute top-0 end-0 badge bg-success m-2 fs-6 p-2">医生推荐</span>
                                            </div>
                                            <div class="p-3">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <h4 class="mb-0 fw-bold">清蒸鲈鱼</h4>
                                                    <button class="btn btn-sm btn-outline-primary rounded-circle">
                                                        <i class="bi bi-volume-up-fill fs-5"></i>
                                                    </button>
                                                </div>
                                                <div class="mb-2">
                                                    <span class="nutrition-tag tag-protein fs-6">高蛋白</span>
                                                    <span class="nutrition-tag tag-lowfat fs-6">低脂</span>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <span class="fw-bold fs-4 me-2">¥28</span>
                                                        <button class="btn btn-primary btn-lg" @click="addToCart(1)">
                                                            <i class="bi bi-plus-lg"></i> 加入
                                                        </button>
                                                    </div>
                                                    <span class="text-success fs-6">适合高血压人群</span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 菜品2 -->
                                        <div class="food-item" @click="showFoodDetail(2)">
                                            <div class="food-img-container">
                                                <img src="/static/images/wugu.png" class="food-img" alt="五谷杂粮粥">
                                                <span class="position-absolute top-0 end-0 badge bg-primary m-2 fs-6 p-2">营养均衡</span>
                                            </div>
                                            <div class="p-3">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <h4 class="mb-0 fw-bold">五谷杂粮粥</h4>
                                                    <button class="btn btn-sm btn-outline-primary rounded-circle">
                                                        <i class="bi bi-volume-up-fill fs-5"></i>
                                                    </button>
                                                </div>
                                                <div class="mb-2">
                                                    <span class="nutrition-tag tag-fiber fs-6">高纤维</span>
                                                    <span class="nutrition-tag tag-lowsugar fs-6">低糖</span>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <span class="fw-bold fs-4 me-2">¥15</span>
                                                        <button class="btn btn-primary btn-lg" @click="addToCart(2)">
                                                            <i class="bi bi-plus-lg"></i> 加入
                                                        </button>
                                                    </div>
                                                    <span class="text-success fs-6">促进肠道健康</span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 菜品3 -->
                                        <div class="food-item" @click="showFoodDetail(3)">
                                            <div class="food-img-container">
                                                <img src="/static/images/beaf.png" class="food-img" alt="西兰花炒牛肉">
                                            </div>
                                            <div class="p-3">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <h4 class="mb-0 fw-bold">西兰花炒牛肉</h4>
                                                    <button class="btn btn-sm btn-outline-primary rounded-circle">
                                                        <i class="bi bi-volume-up-fill fs-5"></i>
                                                    </button>
                                                </div>
                                                <div class="mb-2">
                                                    <span class="nutrition-tag tag-protein fs-6">高蛋白</span>
                                                    <span class="nutrition-tag tag-vitamin fs-6">维生素</span>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <span class="fw-bold fs-4 me-2">¥25</span>
                                                        <button class="btn btn-primary btn-lg" @click="addToCart(3)">
                                                            <i class="bi bi-plus-lg"></i> 加入
                                                        </button>
                                                    </div>
                                                    <span class="text-success fs-6">增强免疫力</span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 菜品4 -->
                                        <div class="food-item" @click="showFoodDetail(4)">
                                            <div class="food-img-container">
                                                <img src="/static/images/sala.png" class="food-img" alt="三文鱼沙拉">
                                            </div>
                                            <div class="p-3">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <h4 class="mb-0 fw-bold">三文鱼沙拉</h4>
                                                    <button class="btn btn-sm btn-outline-primary rounded-circle">
                                                        <i class="bi bi-volume-up-fill fs-5"></i>
                                                    </button>
                                                </div>
                                                <div class="mb-2">
                                                    <span class="nutrition-tag tag-protein fs-6">优质脂肪</span>
                                                    <span class="nutrition-tag tag-vitamin fs-6">维生素</span>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <span class="fw-bold fs-4 me-2">¥32</span>
                                                        <button class="btn btn-primary btn-lg" @click="addToCart(4)">
                                                            <i class="bi bi-plus-lg"></i> 加入
                                                        </button>
                                                    </div>
                                                    <span class="text-success fs-6">心脑血管健康</span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 添加两个新的菜品 -->
                                        <!-- 菜品5 -->
                                        <div class="food-item" @click="showFoodDetail(5)">
                                            <div class="food-img-container">
                                                <img src="/static/images/shanyao.png" class="food-img" alt="山药排骨汤">
                                                <span class="position-absolute top-0 end-0 badge bg-warning m-2 fs-6 p-2">老年养生</span>
                                            </div>
                                            <div class="p-3">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <h4 class="mb-0 fw-bold">山药排骨汤</h4>
                                                    <button class="btn btn-sm btn-outline-primary rounded-circle">
                                                        <i class="bi bi-volume-up-fill fs-5"></i>
                                                    </button>
                                                </div>
                                                <div class="mb-2">
                                                    <span class="nutrition-tag tag-protein fs-6">滋补</span>
                                                    <span class="nutrition-tag tag-vitamin fs-6">易消化</span>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <span class="fw-bold fs-4 me-2">¥36</span>
                                                        <button class="btn btn-primary btn-lg" @click="addToCart(5)">
                                                            <i class="bi bi-plus-lg"></i> 加入
                                                        </button>
                                                    </div>
                                                    <span class="text-success fs-6">健脾益胃</span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 菜品6 -->
                                        <div class="food-item" @click="showFoodDetail(6)">
                                            <div class="food-img-container">
                                                <img src="/static/images/nangua.png" class="food-img" alt="清蒸南瓜">
                                                <span class="position-absolute top-0 end-0 badge bg-info m-2 fs-6 p-2">低糖食谱</span>
                                            </div>
                                            <div class="p-3">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <h4 class="mb-0 fw-bold">清蒸南瓜</h4>
                                                    <button class="btn btn-sm btn-outline-primary rounded-circle">
                                                        <i class="bi bi-volume-up-fill fs-5"></i>
                                                    </button>
                                                </div>
                                                <div class="mb-2">
                                                    <span class="nutrition-tag tag-fiber fs-6">高纤维</span>
                                                    <span class="nutrition-tag tag-lowsugar fs-6">低糖</span>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <span class="fw-bold fs-4 me-2">¥18</span>
                                                        <button class="btn btn-primary btn-lg" @click="addToCart(6)">
                                                            <i class="bi bi-plus-lg"></i> 加入
                                                        </button>
                                                    </div>
                                                    <span class="text-success fs-6">适合糖尿病人</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-4 text-center">
                                        <button class="btn btn-outline-primary btn-lg py-3 px-5 fs-5">
                                            <i class="bi bi-grid-3x3-gap me-1"></i> 查看更多菜品
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 低盐健康餐和血糖友好餐的选项卡内容（略） -->
                        <div class="tab-pane fade" id="v-pills-lowsalt" role="tabpanel">
                            <div class="card shadow-sm">
                                <div class="card-header">
                                    <h5 class="mb-0 fs-3">低盐健康餐</h5>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-primary">
                                        <i class="bi bi-info-circle-fill me-2"></i>
                                        这里将展示适合高血压人群的低盐健康餐选项
                                    </div>
                                    <!-- 这里可以添加类似的菜品卡片 -->
                                </div>
                            </div>
                        </div>

                        <div class="tab-pane fade" id="v-pills-diabetic" role="tabpanel">
                            <div class="card shadow-sm">
                                <div class="card-header">
                                    <h5 class="mb-0 fs-3">血糖友好餐</h5>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-primary">
                                        <i class="bi bi-info-circle-fill me-2"></i>
                                        这里将展示适合糖尿病人群的低糖餐选项
                                    </div>
                                    <!-- 这里可以添加类似的菜品卡片 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 点餐态 - 沉浸式选购（初始时隐藏） -->
            <div class="modal fade" id="foodDetailModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-xl modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h4 class="modal-title"><i class="bi bi-info-circle me-2"></i>菜品详情</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body p-0">
                            <div class="row g-0">
                                <!-- 左侧（60%）：菜品详情 -->
                                <div class="col-md-7 p-4">
                                    <div class="food-detail-container">
                                        <div class="text-center mb-3">
                                            <img v-if="currentFood" :src="currentFood.image" class="img-fluid rounded" style="max-height: 300px;" :alt="currentFood.name">
                                            <img v-else src="https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg" class="img-fluid rounded" style="max-height: 300px;" alt="食品图片">
                                            <div class="mt-2">
                                                <button class="btn btn-outline-primary me-2">
                                                    <i class="bi bi-arrows-angle-expand"></i> 放大查看
                                                </button>
                                                <button class="btn btn-outline-success">
                                                    <i class="bi bi-volume-up-fill"></i> 语音介绍
                                                </button>
                                            </div>
                                        </div>

                                        <h3 class="fw-bold mb-3" v-if="currentFood">${ currentFood.name }$</h3>
                                        <h3 class="fw-bold mb-3" v-else>菜品详情</h3>

                                        <div class="nutrition-info mb-4">
                                            <h5 class="border-bottom pb-2"><i class="bi bi-pie-chart-fill me-2 text-primary"></i>营养金字塔</h5>
                                            <div class="row text-center" v-if="currentFood && currentFood.nutrition">
                                                <div class="col-3">
                                                    <div class="py-2 px-1 bg-primary bg-opacity-10 rounded mb-1">
                                                        <span class="fw-bold d-block fs-4">${ currentFood.nutrition.protein }$g</span>
                                                        <small>蛋白质</small>
                                                    </div>
                                                    <small class="text-muted">≈ ${ Math.round(currentFood.nutrition.protein/10) }$个鸡蛋</small>
                                                </div>
                                                <div class="col-3">
                                                    <div class="py-2 px-1 bg-success bg-opacity-10 rounded mb-1">
                                                        <span class="fw-bold d-block fs-4">${ currentFood.nutrition.fat }$g</span>
                                                        <small>脂肪</small>
                                                    </div>
                                                    <small class="text-muted">≈ ${ currentFood.nutrition.fat }$茶匙油</small>
                                                </div>
                                                <div class="col-3">
                                                    <div class="py-2 px-1 bg-warning bg-opacity-10 rounded mb-1">
                                                        <span class="fw-bold d-block fs-4">${ currentFood.nutrition.carbs }$g</span>
                                                        <small>碳水</small>
                                                    </div>
                                                    <small class="text-muted">≈ ${ Math.round(currentFood.nutrition.carbs/30) }$碗米饭</small>
                                                </div>
                                                <div class="col-3">
                                                    <div class="py-2 px-1 bg-danger bg-opacity-10 rounded mb-1">
                                                        <span class="fw-bold d-block fs-4">${ currentFood.nutrition.salt }$g</span>
                                                        <small>盐分</small>
                                                    </div>
                                                    <small class="text-muted">≈ ${ currentFood.nutrition.salt }$粒花生米</small>
                                                </div>
                                            </div>
                                            <div class="row text-center" v-else>
                                                <div class="col-12">
                                                    <p class="text-muted">加载中...</p>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="health-benefits mb-4">
                                            <h5 class="border-bottom pb-2"><i class="bi bi-heart-pulse-fill me-2 text-danger"></i>健康益处</h5>
                                            <ul class="list-group list-group-flush" v-if="currentFood && currentFood.benefits">
                                                <li class="list-group-item d-flex align-items-center border-0 ps-0" v-for="(benefit, index) in currentFood.benefits" :key="'benefit-'+index">
                                                    <i class="bi bi-check-circle-fill text-success me-2 fs-5"></i>
                                                    <span class="fs-5">${ benefit }$</span>
                                                </li>
                                            </ul>
                                            <ul class="list-group list-group-flush" v-else>
                                                <li class="list-group-item d-flex align-items-center border-0 ps-0">
                                                    <span class="text-muted">加载中...</span>
                                                </li>
                                            </ul>
                                        </div>

                                        <div class="preparation-info">
                                            <h5 class="border-bottom pb-2"><i class="bi bi-info-circle-fill me-2 text-info"></i>制作与配送</h5>
                                            <p class="fs-5">制作时间：约30分钟 | 当日新鲜配送</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 右侧（40%）：购物车简版 -->
                                <div class="col-md-5 bg-light p-4">
                                    <h4 class="mb-3 fw-bold"><i class="bi bi-cart4 me-2 text-primary"></i>您的购物车</h4>

                                    <div class="cart-items-container mb-4">
                                        <div v-if="!cart.items || cart.items.length === 0" class="text-center py-4">
                                            <i class="bi bi-cart-x fs-1 text-muted"></i>
                                            <p class="fs-5 mt-2">购物车还是空的</p>
                                            <button class="btn btn-outline-primary fs-5 mt-2" data-bs-dismiss="modal">
                                                <i class="bi bi-plus-circle me-1"></i> 去选择美味佳肴
                                            </button>
                                        </div>

                                        <div v-for="item in cart.items" :key="'cart-item-'+item.id" class="cart-item bg-white p-3 rounded mb-3 shadow-sm">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <img :src="item.image" class="rounded me-3" width="60" height="60" :alt="item.name">
                                                    <div>
                                                        <h5 class="mb-0 fw-bold">${ item.name }$</h5>
                                                        <span v-for="(tag, tagIndex) in item.tags" :key="'tag-'+tagIndex" class="badge bg-primary me-1">${ tag }$</span>
                                                    </div>
                                                </div>
                                                <div class="text-end">
                                                    <span class="fw-bold fs-5">¥${ item.price }$</span>
                                                    <div class="btn-group mt-1">
                                                        <button class="btn btn-outline-secondary" @click="decreaseQuantity(item.id)">
                                                            <i class="bi bi-dash"></i>
                                                        </button>
                                                        <span class="btn btn-outline-secondary disabled">${ item.quantity }$</span>
                                                        <button class="btn btn-outline-secondary" @click="increaseQuantity(item.id)">
                                                            <i class="bi bi-plus"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div v-if="cart.items && cart.items.length > 0" class="text-center mb-3">
                                            <button class="btn btn-outline-primary fs-5" data-bs-dismiss="modal">
                                                <i class="bi bi-plus-circle me-1"></i> 继续添加其他菜品
                                            </button>
                                        </div>
                                    </div>

                                    <div class="price-summary border-top border-bottom py-3 my-3">
                                        <div class="d-flex justify-content-between fs-5 mb-2">
                                            <span>商品小计</span>
                                            <span>¥${ cart.totalPrice || 0 }$</span>
                                        </div>
                                        <div class="d-flex justify-content-between fs-5 mb-2">
                                            <span>配送费</span>
                                            <span>¥5</span>
                                        </div>
                                        <div class="d-flex justify-content-between fw-bold fs-4">
                                            <span>总计</span>
                                            <span class="text-danger">¥${ (cart.totalPrice || 0) + 5 }$</span>
                                        </div>
                                    </div>

                                    <div class="delivery-preferences mb-3">
                                        <label class="form-label fs-5 fw-bold">希望送达时间</label>
                                        <select class="form-select form-select-lg fs-5 mb-3">
                                            <option selected>11:30-12:00 (推荐)</option>
                                            <option>12:00-12:30</option>
                                            <option>12:30-13:00</option>
                                        </select>

                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="callBeforeDelivery" checked>
                                            <label class="form-check-label fs-5" for="callBeforeDelivery">
                                                送餐前电话通知
                                            </label>
                                        </div>
                                    </div>

                                    <button class="btn btn-success btn-lg w-100 py-3 fs-4" @click="checkout()" :disabled="!cart.items || cart.items.length === 0">
                                        <i class="bi bi-wallet2 me-2"></i> 去结算
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 支付态 - 无忧确认（初始时隐藏） -->
            <div class="modal fade" id="paymentModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-lg modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-success text-white">
                            <h4 class="modal-title"><i class="bi bi-wallet2 me-2"></i>订单确认</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body p-4">
                            <div class="row">
                                <div class="col-md-6">
                                    <h4 class="mb-3"><i class="bi bi-geo-alt-fill me-2 text-danger"></i>配送信息</h4>
                                    <!-- 使用语音播报的配送时间选择 -->
                                    <div class="mb-3">
                                        <label class="form-label fs-5">配送时间</label>
                                        <div class="d-flex align-items-center">
                                            <select class="form-select form-select-lg fs-5">
                                                <option selected>11:30-12:00 (推荐)</option>
                                                <option>12:00-12:30</option>
                                                <option>12:30-13:00</option>
                                            </select>
                                            <button class="btn btn-outline-primary ms-2" title="语音播报可选时段">
                                                <i class="bi bi-volume-up-fill"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-house-door-fill fs-1 text-primary me-3"></i>
                                                <div>
                                                    <h5 class="mb-1">默认地址: 和平小区5号楼3单元102室</h5>
                                                    <p class="mb-0">王大伯 (185****1234)</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label fs-5">配送备注</label>
                                        <div class="d-flex align-items-center">
                                            <select class="form-select form-select-lg fs-5">
                                                <option selected>放门口，不用按门铃</option>
                                                <option>送到家里，需要帮忙打开</option>
                                                <option>请提前电话联系</option>
                                            </select>
                                            <button class="btn btn-outline-primary ms-2">
                                                <i class="bi bi-pencil-fill"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <h4 class="mb-3"><i class="bi bi-credit-card-fill me-2 text-success"></i>支付方式</h4>
                                    <!-- 仅显示3个最常用选项 -->
                                    <div class="payment-options">
                                        <div class="card mb-2 border-primary">
                                            <div class="card-body">
                                                <div class="form-check d-flex align-items-center">
                                                    <input class="form-check-input me-3" type="radio" name="paymentMethod" id="paymentBalance" checked style="width: 25px; height: 25px;">
                                                    <label class="form-check-label d-flex align-items-center justify-content-between w-100" for="paymentBalance">
                                                        <div class="d-flex align-items-center">
                                                            <i class="bi bi-wallet2-fill fs-1 text-primary me-3"></i>
                                                            <div>
                                                                <h5 class="mb-0 fs-4">账户余额支付</h5>
                                                                <p class="mb-0">余额: ¥208.50</p>
                                                            </div>
                                                        </div>
                                                        <i class="bi bi-check-circle-fill text-primary fs-4"></i>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="card mb-2">
                                            <div class="card-body">
                                                <div class="form-check d-flex align-items-center">
                                                    <input class="form-check-input me-3" type="radio" name="paymentMethod" id="paymentFamily" style="width: 25px; height: 25px;">
                                                    <label class="form-check-label d-flex align-items-center justify-content-between w-100" for="paymentFamily">
                                                        <div class="d-flex align-items-center">
                                                            <i class="bi bi-people-fill fs-1 text-success me-3"></i>
                                                            <div>
                                                                <h5 class="mb-0 fs-4">子女代付</h5>
                                                                <p class="mb-0">微信通知家人支付</p>
                                                            </div>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="card mb-2">
                                            <div class="card-body">
                                                <div class="form-check d-flex align-items-center">
                                                    <input class="form-check-input me-3" type="radio" name="paymentMethod" id="paymentSubsidy" style="width: 25px; height: 25px;">
                                                    <label class="form-check-label d-flex align-items-center justify-content-between w-100" for="paymentSubsidy">
                                                        <div class="d-flex align-items-center">
                                                            <i class="bi bi-building-fill fs-1 text-warning me-3"></i>
                                                            <div>
                                                                <h5 class="mb-0 fs-4">政府补贴</h5>
                                                                <p class="mb-0">每月补贴剩余: ¥180</p>
                                                            </div>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 订单摘要 -->
                                    <div class="order-summary mt-4">
                                        <h5 class="border-bottom pb-2">订单摘要</h5>
                                        <div v-if="cart.items && cart.items.length > 0">
                                            <div v-for="(item, itemIndex) in cart.items" :key="'summary-item-'+itemIndex" class="d-flex justify-content-between fs-5 mb-2">
                                                <span>${ item.name }$ x${ item.quantity }$</span>
                                                <span>¥${ item.price * item.quantity }$</span>
                                            </div>
                                        </div>
                                        <div v-else>
                                            <p class="text-muted">购物车为空</p>
                                        </div>
                                        <div class="d-flex justify-content-between fs-5 mb-2">
                                            <span>配送费</span>
                                            <span>¥5</span>
                                        </div>
                                        <div class="d-flex justify-content-between fw-bold fs-4 text-danger">
                                            <span>总计</span>
                                            <span>¥${ (cart.totalPrice || 0) + 5 }$</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 应急联系浮窗 -->
                            <div class="alert alert-info d-flex align-items-center mt-4">
                                <i class="bi bi-info-circle-fill fs-4 me-3"></i>
                                <div>
                                    <h5 class="mb-1">订单通知已自动发送给家属</h5>
                                    <p class="mb-0">王女士(女儿) 将收到订单消息提醒</p>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-secondary btn-lg fs-5" data-bs-dismiss="modal">返回修改</button>
                            <button type="button" class="btn btn-success btn-lg fs-4 px-5" @click="confirmPayment()">
                                <i class="bi bi-check-circle-fill me-2"></i> 确认支付 ¥${ (cart.totalPrice || 0) + 5 }$
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 个人档案 -->
        <div v-if="currentTab === 'profile'">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>个人健康档案</h2>
                <div>
                    <button class="btn btn-outline-primary me-2">
                        <i class="bi bi-upload me-1"></i> 导入数据
                    </button>
                    <button class="btn btn-outline-secondary">
                        <i class="bi bi-printer me-1"></i> 打印档案
                    </button>
                </div>
            </div>

            <div class="health-record-container">
                <!-- 左侧时间轴 -->
                <div class="health-timeline" :class="{ 'collapsed': timelineCollapsed }">
                    <div class="timeline-header p-3">
                        <h5 class="mb-0 d-flex align-items-center">
                            <span v-if="!timelineCollapsed">病史记录时间轴</span>
                            <span class="vertical-text">病史记录</span>
                        </h5>
                        <button class="timeline-toggle-btn" @click="toggleTimeline">
                            <i class="bi" :class="timelineCollapsed ? 'bi-chevron-right' : 'bi-chevron-left'"></i>
                        </button>
                    </div>

                    <div class="timeline-content p-3">


                        <div class="timeline-item">
                            <div class="d-flex justify-content-between">
                                <h6 class="mb-1">社区医生随访</h6>
                                <small>2023-05-07</small>
                            </div>
                            <p class="mb-1 text-muted small">主诉: 近期偶有头晕</p>
                            <p class="mb-1 text-muted small">诊断: 轻度高血压，建议监测血压</p>
                            <p class="mb-1 text-muted small">医嘱: 继续服用降压药，每日测量血压</p>
                            <div class="d-flex justify-content-end">
                                <button class="btn btn-sm btn-link p-0">查看详情</button>
                            </div>
                        </div>



                        <div class="timeline-item">
                            <div class="d-flex justify-content-between">
                                <h6 class="mb-1">季度体检报告</h6>
                                <small>2023-04-25</small>
                            </div>
                            <p class="mb-1 text-muted small">检查项目: 血常规、尿常规、肝功能、肾功能</p>
                            <p class="mb-1 text-muted small">异常项: 总胆固醇轻度升高(5.8mmol/L)</p>
                            <p class="mb-1 text-muted small">医嘱: 低脂饮食，增加运动，三个月后复查</p>
                            <div class="d-flex justify-content-end">
                                <button class="btn btn-sm btn-link p-0">查看报告</button>
                            </div>
                        </div>

                        <div class="timeline-item">
                            <div class="d-flex justify-content-between">
                                <h6 class="mb-1">心脏科门诊</h6>
                                <small>2023-04-05</small>
                            </div>
                            <p class="mb-1 text-muted small">主诉: 心悸，心率不齐</p>
                            <p class="mb-1 text-muted small">检查: 心电图示窦性心律不齐</p>
                            <p class="mb-1 text-muted small">诊断: 窦性心律不齐</p>
                            <p class="mb-1 text-muted small">处方: 倍他乐克 25mg 每日1次</p>
                            <div class="d-flex justify-content-end">
                                <button class="btn btn-sm btn-link p-0">查看病历</button>
                            </div>
                        </div>

                        <button class="btn btn-sm btn-outline-primary d-block w-100 mt-3">
                            <i class="bi bi-plus me-1"></i> 添加新记录
                        </button>
                    </div>
                </div>

                <!-- 右侧健康数据详情 -->
                <div class="flex-fill">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">健康档案</h5>
                        </div>
                        <div class="card-body">
                            <ul class="nav nav-tabs mb-3" id="healthDataTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="examinations-tab" data-bs-toggle="tab" data-bs-target="#examinations" type="button">检查报告</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="medications-tab" data-bs-toggle="tab" data-bs-target="#medications" type="button">用药记录</button>
                                </li>
                            </ul>

                            <div class="tab-content" id="healthDataContent">


                                <!-- 用药记录标签页 -->
                                <div class="tab-pane fade" id="medications" role="tabpanel">
                                    <div class="alert alert-info mb-4">
                                        <i class="bi bi-info-circle-fill me-2"></i>
                                        您目前有3种长期服用的药物，包括1种心脏用药需要按时服用
                                    </div>

                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>药物名称</th>
                                                    <th>用量</th>
                                                    <th>频率</th>
                                                    <th>开始日期</th>
                                                    <th>剩余药量</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <i class="bi bi-capsule text-primary me-2"></i>
                                                            <div>
                                                                <strong>阿司匹林</strong><br>
                                                                <small class="text-muted">抗血栓</small>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>100mg</td>
                                                    <td>每日1次</td>
                                                    <td>2023-05-15</td>
                                                    <td>
                                                        <div class="progress" style="height: 8px; width: 100px;">
                                                            <div class="progress-bar bg-success" role="progressbar" style="width: 65%;" aria-valuenow="65" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                        <small>剩余19天</small>
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-sm btn-outline-primary">记录服用</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <i class="bi bi-capsule text-danger me-2"></i>
                                                            <div>
                                                                <strong>硝苯地平缓释片</strong><br>
                                                                <small class="text-muted">降血压</small>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>30mg</td>
                                                    <td>每日1次</td>
                                                    <td>2023-06-20</td>
                                                    <td>
                                                        <div class="progress" style="height: 8px; width: 100px;">
                                                            <div class="progress-bar bg-warning" role="progressbar" style="width: 25%;" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                        <small>剩余7天</small>
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-sm btn-outline-primary">记录服用</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <i class="bi bi-capsule text-info me-2"></i>
                                                            <div>
                                                                <strong>甲钴胺片</strong><br>
                                                                <small class="text-muted">神经营养</small>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>0.5mg</td>
                                                    <td>每日3次</td>
                                                    <td>2023-07-05</td>
                                                    <td>
                                                        <div class="progress" style="height: 8px; width: 100px;">
                                                            <div class="progress-bar bg-danger" role="progressbar" style="width: 10%;" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                        <small>剩余3天</small>
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-sm btn-outline-primary">记录服用</button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>

                                    <div class="d-flex justify-content-end mt-3">
                                        <button class="btn btn-primary">
                                            <i class="bi bi-bag-plus me-1"></i> 添加新药物
                                        </button>
                                    </div>
                                </div>

                                <!-- 检查报告标签页 -->
                                <div class="tab-pane fade" id="examinations" role="tabpanel">
                                    <div class="alert alert-success mb-4">
                                        <i class="bi bi-check-circle-fill me-2"></i>
                                        您已完成本季度的健康体检，整体情况良好
                                    </div>

                                    <div class="row mb-4">
                                        <div class="col-md-4">
                                            <div class="card h-100">
                                                <div class="card-body text-center">
                                                    <i class="bi bi-file-earmark-medical text-primary fs-1 mb-3"></i>
                                                    <h5>季度体检报告</h5>
                                                    <p class="text-muted small">2023年第三季度</p>
                                                    <p class="mb-0">
                                                        <span class="badge bg-success me-1">已完成</span>
                                                        <span class="small text-muted">2023-09-15</span>
                                                    </p>
                                                    <button class="btn btn-sm btn-primary mt-3">
                                                        <i class="bi bi-file-earmark-pdf me-1"></i> 查看报告
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="card h-100">
                                                <div class="card-body text-center">
                                                    <i class="bi bi-heart-pulse text-danger fs-1 mb-3"></i>
                                                    <h5>心电图检查</h5>
                                                    <p class="text-muted small">专项检查</p>
                                                    <p class="mb-0">
                                                        <span class="badge bg-success me-1">已完成</span>
                                                        <span class="small text-muted">2023-08-22</span>
                                                    </p>
                                                    <button class="btn btn-sm btn-primary mt-3">
                                                        <i class="bi bi-file-earmark-pdf me-1"></i> 查看报告
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="card h-100">
                                                <div class="card-body text-center">
                                                    <i class="bi bi-lungs text-info fs-1 mb-3"></i>
                                                    <h5>肺功能检查</h5>
                                                    <p class="text-muted small">专项检查</p>
                                                    <p class="mb-0">
                                                        <span class="badge bg-warning me-1">待完成</span>
                                                        <span class="small text-muted">2023-10-25</span>
                                                    </p>
                                                    <button class="btn btn-sm btn-outline-primary mt-3">
                                                        <i class="bi bi-calendar-check me-1"></i> 查看预约
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="mb-0">健康分析报告</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <h6 class="border-bottom pb-2 mb-3">指标异常情况</h6>
                                                    <div class="mb-3">
                                                        <div class="d-flex justify-content-between">
                                                            <span>总胆固醇</span>
                                                            <span class="text-danger">5.8 mmol/L</span>
                                                        </div>
                                                        <div class="progress mt-1" style="height: 6px;">
                                                            <div class="progress-bar bg-danger" role="progressbar" style="width: 65%;" aria-valuenow="65" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                        <small class="text-muted">参考范围: 3.1-5.2 mmol/L</small>
                                                    </div>
                                                    <div class="mb-3">
                                                        <div class="d-flex justify-content-between">
                                                            <span>空腹血糖</span>
                                                            <span class="text-warning">6.3 mmol/L</span>
                                                        </div>
                                                        <div class="progress mt-1" style="height: 6px;">
                                                            <div class="progress-bar bg-warning" role="progressbar" style="width: 45%;" aria-valuenow="45" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                        <small class="text-muted">参考范围: 3.9-6.1 mmol/L</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <h6 class="border-bottom pb-2 mb-3">医生建议</h6>
                                                    <div class="alert alert-warning">
                                                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                                        <span>您的胆固醇偏高，建议减少油腻食物摄入并增加运动。</span>
                                                    </div>
                                                    <div class="alert alert-info">
                                                        <i class="bi bi-info-circle-fill me-2"></i>
                                                        <span>血糖水平轻度升高，建议定期监测，控制碳水化合物摄入。</span>
                                                    </div>
                                                    <button class="btn btn-sm btn-outline-primary mt-2">
                                                        <i class="bi bi-telephone-fill me-1"></i> 咨询医生
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>

        <!-- 健康科普 -->
        <div v-if="currentTab === 'health_edu'">
            <div class="elderly-edu-container">
                <!-- 智能导航栏 -->
                <div class="smart-nav">
                    <button class="voice-search" @click="activateVoiceSearch">
                        <i class="bi bi-mic-fill me-2"></i> 说病症找文章
                    </button>

                    <div class="font-control">
                        <button @click="adjustFontSize(-2)">A-</button>
                        <span>字体</span>
                        <button @click="adjustFontSize(2)">A+</button>
                    </div>

                    <button class="read-aloud" @click="startReadAloud">
                        <i class="bi bi-play-fill me-1"></i> 听文章
                    </button>
                </div>

                <!-- 分类导航栏 -->
                <div class="category-nav mb-4">
                    <div class="category-item" :class="{active: selectedCategory === 'all'}" @click="filterByCategory('all')">
                        <i class="bi bi-grid-fill me-2"></i> 全部
                    </div>
                    <div class="category-item" :class="{active: selectedCategory === '养生'}" @click="filterByCategory('养生')">
                        <i class="bi bi-flower2 me-2"></i> 养生
                    </div>
                    <div class="category-item" :class="{active: selectedCategory === '急救'}" @click="filterByCategory('急救')">
                        <i class="bi bi-heart-pulse-fill me-2"></i> 急救
                    </div>
                    <div class="category-item" :class="{active: selectedCategory === '饮食'}" @click="filterByCategory('饮食')">
                        <i class="bi bi-cup-hot-fill me-2"></i> 饮食
                    </div>
                    <div class="category-item" :class="{active: selectedCategory === '运动'}" @click="filterByCategory('运动')">
                        <i class="bi bi-activity me-2"></i> 运动
                    </div>
                    <div class="category-item" :class="{active: selectedCategory === '疾病管理'}" @click="filterByCategory('疾病管理')">
                        <i class="bi bi-shield-plus-fill me-2"></i> 疾病管理
                    </div>
                    <div class="category-item" :class="{active: selectedCategory === '用药'}" @click="filterByCategory('用药')">
                        <i class="bi bi-capsule me-2"></i> 用药
                    </div>
                    <div class="category-item" :class="{active: selectedCategory === '心理'}" @click="filterByCategory('心理')">
                        <i class="bi bi-emoji-smile me-2"></i> 心理
                    </div>
                    <div class="category-item" :class="{active: selectedCategory === '保健'}" @click="filterByCategory('保健')">
                        <i class="bi bi-eye me-2"></i> 保健
                    </div>
                </div>

                <!-- 核心阅读区 -->
                <div class="edu-reading-area">
                    <!-- 未展开的文章列表 -->
                    <div v-if="!articleExpanded">
                        <!-- 加载状态 -->
                        <div v-if="articlesLoading" class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-3 text-muted">正在加载健康文章...</p>
                        </div>

                        <!-- 文章列表 -->
                        <div v-else>
                            <!-- 头条推荐文章 -->
                            <div v-if="featuredArticle" class="mb-5">
                                <h2 class="article-title-large" @click="expandArticle(featuredArticle)">
                                    <i class="bi bi-heart-pulse-fill me-2 text-danger"></i>
                                    ${featuredArticle.title}$
                                </h2>
                                <div class="article-difficulty">
                                    <i v-for="n in featuredArticle.difficulty_level" :key="n" class="bi bi-star-fill me-1"></i>
                                    <span class="ms-1 text-muted">
                                        ${featuredArticle.difficulty_level === 1 ? '简单' : featuredArticle.difficulty_level === 2 ? '适中' : '专业'}$
                                    </span>
                                    <span class="ms-2 text-muted">阅读量: ${featuredArticle.read_count}$</span>
                                </div>
                                <button class="btn btn-lg btn-outline-primary w-100 py-3 mb-3" @click="expandArticle(featuredArticle)">
                                    <i class="bi bi-arrow-down-circle me-2"></i> 点击展开阅读
                                </button>
                            </div>

                            <!-- 其他推荐文章 -->
                            <h3 class="mb-4">为您推荐的健康文章</h3>

                            <div v-if="filteredArticles.length === 0" class="text-center py-5">
                                <i class="bi bi-search fs-1 text-muted mb-3"></i>
                                <p class="text-muted">暂无相关文章</p>
                            </div>

                            <div v-for="article in filteredArticles" :key="article.article_id"
                                 class="card mb-4 border-0 shadow-sm article-card"
                                 @click="expandArticle(article)">
                                <div class="card-body p-4">
                                    <div class="row">
                                        <!-- 文章配图 -->
                                        <div class="col-md-3">
                                            <img :src="article.image_url || '/static/images/health.png'"
                                                 :alt="article.title"
                                                 class="img-fluid rounded article-thumbnail">
                                        </div>
                                        <!-- 文章信息 -->
                                        <div class="col-md-9">
                                            <h4 class="mb-2">${article.title}$</h4>
                                            <div class="article-meta mb-2">
                                                <span class="badge bg-primary me-2">${article.category}$</span>
                                                <div class="article-difficulty d-inline">
                                                    <i v-for="n in article.difficulty_level" :key="n" class="bi bi-star-fill me-1"></i>
                                                    <span class="ms-1 text-muted">
                                                        ${article.difficulty_level === 1 ? '简单' : article.difficulty_level === 2 ? '适中' : '专业'}$
                                                    </span>
                                                </div>
                                                <span class="ms-3 text-muted">
                                                    <i class="bi bi-eye me-1"></i>${article.read_count}$ 次阅读
                                                </span>
                                                <span class="ms-3 text-muted">
                                                    <i class="bi bi-calendar me-1"></i>${article.publish_time}$
                                                </span>
                                            </div>
                                            <p class="text-muted article-excerpt">
                                                ${article.content.substring(0, 100)}$...
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 分页 -->
                            <div v-if="totalPages > 1" class="d-flex justify-content-center mt-4">
                                <nav aria-label="文章分页">
                                    <ul class="pagination">
                                        <li class="page-item" :class="{disabled: currentPage === 1}">
                                            <a class="page-link" href="#" @click.prevent="changePage(currentPage - 1)">上一页</a>
                                        </li>
                                        <li v-for="page in visiblePages" :key="page"
                                            class="page-item" :class="{active: page === currentPage}">
                                            <a class="page-link" href="#" @click.prevent="changePage(page)">${page}$</a>
                                        </li>
                                        <li class="page-item" :class="{disabled: currentPage === totalPages}">
                                            <a class="page-link" href="#" @click.prevent="changePage(currentPage + 1)">下一页</a>
                                        </li>
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>

                    <!-- 展开的文章内容 -->
                    <div v-if="articleExpanded && currentArticle">
                        <button class="btn btn-outline-secondary mb-4" @click="closeArticle">
                            <i class="bi bi-arrow-left me-2"></i> 返回文章列表
                        </button>

                        <h1 class="article-title-large">${currentArticle.title}$</h1>
                        <div class="article-difficulty mb-3">
                            <i v-for="n in currentArticle.difficulty_level" :key="n" class="bi bi-star-fill me-1"></i>
                            <span class="ms-1 text-muted">
                                ${currentArticle.difficulty_level === 1 ? '简单' : currentArticle.difficulty_level === 2 ? '适中' : '专业'}$ |
                                阅读量: ${currentArticle.read_count}$ |
                                发布时间: ${currentArticle.publish_time}$
                            </span>
                        </div>

                        <!-- 文章配图 -->
                        <div v-if="currentArticle.image_url" class="mb-4">
                            <img :src="currentArticle.image_url" :alt="currentArticle.title"
                                 class="img-fluid rounded w-100" style="max-height: 400px; object-fit: cover;">
                        </div>

                        <!-- 关键知识点 -->
                        <h3 class="mb-3"><i class="bi bi-bookmark-star-fill me-2 text-primary"></i>核心知识要点</h3>
                        <div class="key-points">
                            <div class="key-point">
                                <h4 class="key-point-title">1. 血压管理目标值</h4>
                                <p class="key-point-content">老年高血压患者的控制目标应为收缩压140mmHg以下，舒张压90mmHg以下</p>
                                <button class="key-point-audio" @click="playAudio('point1')">
                                    <i class="bi bi-volume-up-fill"></i>
                                </button>
                            </div>

                            <div class="key-point">
                                <h4 class="key-point-title">2. 饮食控制重点</h4>
                                <p class="key-point-content">每日食盐摄入量应控制在5克以内，膳食应减少脂肪摄入，增加蔬果比例</p>
                                <button class="key-point-audio" @click="playAudio('point2')">
                                    <i class="bi bi-volume-up-fill"></i>
                                </button>
                            </div>

                            <div class="key-point">
                                <h4 class="key-point-title">3. 用药依从性原则</h4>
                                <p class="key-point-content">高血压用药需规律服用，不可自行调整药量或停药，避免血压波动</p>
                                <button class="key-point-audio" @click="playAudio('point3')">
                                    <i class="bi bi-volume-up-fill"></i>
                                </button>
                            </div>
                        </div>

                        <!-- 正文内容 -->
                        <div class="article-content">
                            <div class="content-section">
                                <h3><i class="bi bi-info-circle-fill me-2 text-primary"></i>文章内容</h3>
                                <div v-html="formatArticleContent(currentArticle.content)" class="article-text"></div>
                            </div>
                        </div>

                        <!-- 收藏和分享操作 -->
                        <div class="article-actions mt-4 mb-4">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <button class="btn btn-outline-primary me-2" @click="toggleFavorite(currentArticle.article_id)">
                                        <i class="bi bi-heart me-1" :class="{'bi-heart-fill text-danger': isArticleFavorited(currentArticle.article_id)}"></i>
                                        ${isArticleFavorited(currentArticle.article_id) ? '已收藏' : '收藏'}$
                                    </button>
                                    <button class="btn btn-outline-secondary me-2" @click="shareArticle(currentArticle)">
                                        <i class="bi bi-share me-1"></i> 分享
                                    </button>
                                </div>
                                <div class="text-muted">
                                    <small>分类：${currentArticle.category}$ | 阅读量：${currentArticle.read_count}$</small>
                                </div>
                            </div>
                        </div>

                            <!-- 实践步骤 -->
                            <div class="practice-steps">
                                <h4><i class="bi bi-list-check me-2"></i>高血压自我管理实践步骤</h4>

                                <div class="practice-step">
                                    <div class="practice-step-number">1</div>
                                    <div class="practice-step-content">
                                        <strong>呼吸放松训练</strong>
                                        <p>每天进行5-10分钟的深呼吸练习，能有效缓解压力，辅助降低血压</p>
                                    </div>
                                    <div class="practice-step-action">
                                        <button class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-play-circle me-1"></i> 演示
                                        </button>
                                    </div>
                                </div>

                                <div class="practice-step">
                                    <div class="practice-step-number">2</div>
                                    <div class="practice-step-content">
                                        <strong>低钠饮食烹饪方法</strong>
                                        <p>使用香草、柠檬汁等调味，减少盐的使用量，保持食物美味</p>
                                    </div>
                                    <div class="practice-step-action">
                                        <button class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-cart me-1"></i> 食谱
                                        </button>
                                    </div>
                                </div>

                                <div class="practice-step">
                                    <div class="practice-step-number">3</div>
                                    <div class="practice-step-content">
                                        <strong>适合高血压患者的缓和运动</strong>
                                        <p>太极、散步、水中运动等适合老年高血压患者的安全运动方式</p>
                                    </div>
                                    <div class="practice-step-action">
                                        <button class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-camera-video me-1"></i> 示范
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="content-section">
                                <h3><i class="bi bi-capsule me-2 text-primary"></i>药物治疗注意事项</h3>
                                <p>药物治疗是控制高血压的重要手段，尤其对于老年高血压患者：</p>
                                <ul>
                                    <li>按医嘱规律服药，不要随意停药或减量</li>
                                    <li>留意药物不良反应，如出现头晕、乏力等症状应及时就医</li>
                                    <li>多种药物联合使用时注意相互作用</li>
                                    <li>某些特殊情况（如感冒发热）可能需要调整用药，应咨询医生</li>
                                </ul>
                            </div>

                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                <strong>需要立即就医的情况：</strong> 如血压突然升高超过180/120mmHg，或伴有头痛、胸痛、呼吸困难、视力模糊等症状，应立即就医。
                            </div>
                        </div>

                        <!-- 专家视频解读 -->
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="bi bi-play-btn-fill me-2"></i>专家视频解读</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="position-relative">
                                            <img src="https://images.pexels.com/photos/8830475/pexels-photo-8830475.jpeg" class="w-100 rounded" style="max-height: 400px; object-fit: cover;" alt="专家视频">
                                            <div class="position-absolute top-50 start-50 translate-middle">
                                                <button class="btn btn-light btn-lg rounded-circle p-3" @click="playVideo">
                                                    <i class="bi bi-play-fill fs-1"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <h5 class="mt-3">《老年高血压的特点与治疗》</h5>
                                        <p class="text-muted">北京协和医院心内科主任 张教授</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>



                <!-- 浮动功能岛 -->
                <div class="float-tools">
                    <div class="tool-card" @click="toggleBookmark">
                        <i class="bi bi-bookmark-star fs-4 text-primary"></i>
                        <span>收藏文章</span>
                    </div>
                    <div class="tool-card" @click="askQuestion">
                        <i class="bi bi-chat-dots fs-4 text-warning"></i>
                        <span>提问</span>
                    </div>
                </div>

            </div>
        </div>
    </div>



    <!-- 紧急呼叫浮动按钮 -->
    <div class="floating-emergency-btn" @click="showEmergencyModal()">
        <i class="bi bi-telephone-fill floating-emergency-icon"></i>
        <span>紧急呼叫</span>
    </div>

    <!-- 健康科普中心模态窗口 -->
    <div class="modal fade" id="bookmarkModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title"><i class="bi bi-bookmark-check me-2"></i>收藏成功</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <p class="fs-5">文章已成功收藏到您的个人收藏夹</p>
                    <div class="d-flex align-items-center mt-3">
                        <span class="badge bg-light text-primary me-2 p-2">心脑血管</span>
                        <span class="badge bg-light text-primary me-2 p-2">健康管理</span>
                        <span class="badge bg-light text-primary me-2 p-2">高血压</span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary">查看收藏夹</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="progressModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title"><i class="bi bi-graph-up me-2"></i>学习进度</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="progress-tree">
                        <img src="https://via.placeholder.com/400x200" alt="学习进度树" class="img-fluid mb-3">
                        <div class="progress-info">
                            <h5>心脑血管保健系列课程</h5>
                            <div class="progress mb-3" style="height: 20px;">
                                <div class="progress-bar bg-success" role="progressbar" style="width: 65%;" aria-valuenow="65" aria-valuemin="0" aria-valuemax="100">65%</div>
                            </div>
                            <p>已完成 6/10 篇文章</p>
                        </div>
                    </div>
                    <div class="alert alert-success mt-4">
                        <i class="bi bi-award me-2"></i>
                        <strong>做得好！</strong> 继续保持，完成全部学习后可获得健康积分奖励。
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-success">继续学习</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="medicationModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title"><i class="bi bi-capsule me-2"></i>关联用药信息</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <h4 class="mb-3">您正在服用的药物</h4>
                    <ul class="medication-list">
                        <li class="medication-item">
                            <div class="medication-icon bg-danger bg-opacity-10 text-danger">
                                <i class="bi bi-heart-pulse"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">硝苯地平缓释片</h6>
                                <p class="mb-0 small text-muted">每日一次，每次一片</p>
                                <div class="progress mt-2" style="height: 5px;">
                                    <div class="progress-bar bg-danger" role="progressbar" style="width: 70%"></div>
                                </div>
                                <small class="text-muted">剩余21天用量</small>
                            </div>
                        </li>
                        <li class="medication-item mt-3">
                            <div class="medication-icon bg-primary bg-opacity-10 text-primary">
                                <i class="bi bi-droplet"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">阿司匹林肠溶片</h6>
                                <p class="mb-0 small text-muted">每日一次，每次一片</p>
                                <div class="progress mt-2" style="height: 5px;">
                                    <div class="progress-bar bg-primary" role="progressbar" style="width: 45%"></div>
                                </div>
                                <small class="text-muted">剩余14天用量</small>
                            </div>
                        </li>
                    </ul>
                    <div class="alert alert-warning mt-4">
                        <i class="bi bi-exclamation-circle me-2"></i>
                        <strong>注意：</strong> 本文内容可能与您的用药存在关联，请咨询医生了解详情。
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-danger">查看用药提醒</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="questionModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title"><i class="bi bi-chat-left-dots me-2"></i>提出问题</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <h5 class="mb-3">您对文章内容有什么疑问？</h5>
                    <div class="question-form">
                        <div class="form-floating mb-3">
                            <textarea class="form-control" placeholder="请在此输入您的问题" id="questionText" style="height: 120px"></textarea>
                            <label for="questionText">您的问题</label>
                        </div>
                        <div class="d-flex justify-content-center mb-3">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary">
                                    <i class="bi bi-mic-fill me-2"></i>语音输入
                                </button>
                                <button type="button" class="btn btn-outline-primary">
                                    <i class="bi bi-camera-fill me-2"></i>拍照提问
                                </button>
                            </div>
                        </div>
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" value="" id="contactCheck">
                            <label class="form-check-label" for="contactCheck">
                                同意社区健康管理师联系我
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-warning">提交问题</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="videoModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title"><i class="bi bi-play-btn me-2"></i>专家视频解读</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-0">
                    <div class="ratio ratio-16x9">
                        <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" title="专家视频" allowfullscreen></iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 紧急呼叫模态窗口 -->
    <div class="modal fade" id="emergencyModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h4 class="modal-title"><i class="bi bi-exclamation-triangle-fill me-2"></i> 紧急呼叫</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-0">
                    <div v-if="!emergencyStatus">
                        <div class="p-4">
                            <h5 class="mb-3">请选择紧急情况类型：</h5>
                            <div class="d-flex justify-content-between gap-3">
                                <div class="emergency-option medical text-center p-4 flex-fill" @click="emergencyType = 'medical'; startEmergencyCall()">
                                    <i class="bi bi-heart-pulse fs-1 mb-2"></i>
                                    <div>医疗紧急</div>
                                </div>
                                <div class="emergency-option fall text-center p-4 flex-fill" @click="emergencyType = 'fall'; startEmergencyCall()">
                                    <img src="/static/images/fall.png" class="fs-1 mb-2" width="60" height="60">
                                    <div>跌倒求助</div>
                                </div>
                                <div class="emergency-option help text-center p-4 flex-fill" @click="emergencyType = 'help'; startEmergencyCall()">
                                    <i class="bi bi-megaphone-fill fs-1 mb-2"></i>
                                    <div>其他求助</div>
                                </div>
                            </div>

                            <div class="alert alert-warning mt-4">
                                <i class="bi bi-info-circle-fill me-2"></i>
                                请注意：点击紧急情况类型后，系统将自动拨打相应的紧急联系人。如非必要，请勿点击。
                            </div>
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        </div>
                    </div>

                    <div v-else>
                        <div class="text-center p-4">
                            <div v-if="emergencyStatus === 'calling'">
                                <!-- 步骤2：呼叫中 -->
                                <div class="text-center mb-4">
                                    <div class="emergency-countdown mb-3">
                                        <span>${countdownTimer}$</span>
                                    </div>
                                    <h5 class="mb-3 fs-3">正在呼叫紧急救援中心...</h5>
                                    <p class="text-muted fs-5">请保持通话，不要挂断</p>
                                </div>

                                <!-- 地图组件 -->
                                <div class="emergency-map-container" id="emergencyMap"></div>

                                <div class="location-info">
                                    <i class="bi bi-geo-alt-fill"></i>
                                    <div>
                                        <p class="mb-1 fs-5" v-if="currentLocation">您的当前位置：${currentLocation.address}$</p>
                                        <p class="mb-0 fs-5" v-else>正在获取您的位置...</p>
                                    </div>
                                </div>

                                <!-- 状态进度条 -->
                                <div class="emergency-status-progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 70%"></div>
                                </div>

                                <div class="text-center mt-4">
                                    <button class="btn btn-secondary btn-lg px-4 py-2 mx-2" @click="cancelEmergencyCall">
                                        <i class="bi bi-x-circle me-2"></i> 取消呼叫
                                    </button>
                                </div>
                            </div>

                            <div v-else-if="emergencyStatus === 'connected'">
                                <!-- 步骤3：已连接 -->
                                <div class="alert alert-success fs-5 mb-4">
                                    <i class="bi bi-check-circle-fill me-2"></i>
                                    已成功连接紧急救助中心，救援人员正在赶来
                                </div>

                                <!-- 地图组件 - 使用同一个地图ID -->
                                <div class="emergency-map-container" id="emergencyMap"></div>

                                <div class="location-info">
                                    <i class="bi bi-geo-alt-fill"></i>
                                    <div>
                                        <p class="mb-1 fs-5" v-if="currentLocation">救援人员将赶往：${currentLocation.address}$</p>
                                        <p class="mb-0 text-success fs-5">预计到达时间：5-10分钟</p>
                                    </div>
                                </div>
                            </div>

                            <div v-else>
                                <div class="alert alert-info mb-3">
                                    <i class="bi bi-info-circle-fill me-2"></i>
                                    ${emergencyStatus}
                                </div>

                                <div id="emergencyMap" style="height: 200px;" class="mb-3"></div>
                            </div>
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" @click="cancelEmergencyCall">
                                <i class="bi bi-x-circle me-1"></i> 取消呼叫
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.bootcdn.net/ajax/libs/Chart.js/3.7.0/chart.min.js"></script>
<script>
    // 创建Vue应用
    const app = new Vue({
        el: '#elderly-app',
        delimiters: ['${', '}$'], // 更改Vue默认的双花括号语法，避免与Jinja2冲突
        data: {
            navCollapsed: false,
            currentTab: 'health',
            showAssistantModal: false,
            timelineCollapsed: false,
            articleExpanded: false,
            // 健康科普中心相关状态
            userFontSize: 22,
            readingAloud: false,
            voiceSearchActive: false,
            // 健康科普文章相关数据
            articles: [],
            filteredArticles: [],
            featuredArticle: null,
            currentArticle: null,
            selectedCategory: 'all',
            articlesLoading: false,
            currentPage: 1,
            totalPages: 1,
            perPage: 5,
            favoriteArticles: [],
            // 订餐系统相关数据
            cart: {
                items: [],
                totalPrice: 0
            },
            currentFood: null,
            // 紧急呼叫相关数据
            emergencyStatus: '',
            currentLocation: null,
            emergencyType: null,
            countdownTimer: 10,
            emergencyMap: null,
            countdownInterval: null,
            buzzerAudio: null,
            // 其他数据...
            foodList: [
                {
                    id: 1,
                    name: "清蒸鲈鱼",
                    price: 28,
                    image: "/static/images/fish.png",
                    tags: ["高蛋白", "低脂"],
                    info: "适合高血压人群",
                    badge: "医生推荐",
                    badgeClass: "bg-success",
                    nutrition: {
                        protein: 20,
                        fat: 5,
                        carbs: 10,
                        salt: 2
                    },
                    benefits: [
                        "富含DHA，有助于心脑血管健康",
                        "清蒸烹饪法，低脂健康",
                        "优质蛋白质来源，易于消化吸收"
                    ]
                },
                {
                    id: 2,
                    name: "五谷杂粮粥",
                    price: 15,
                    image: "/static/images/wugu.png",
                    tags: ["高纤维", "低糖"],
                    info: "促进肠道健康",
                    badge: "营养均衡",
                    badgeClass: "bg-primary",
                    nutrition: {
                        protein: 8,
                        fat: 2,
                        carbs: 30,
                        salt: 1
                    },
                    benefits: [
                        "富含膳食纤维，促进肠道蠕动",
                        "多种谷物营养均衡",
                        "低糖设计，适合血糖控制"
                    ]
                },
                {
                    id: 3,
                    name: "西兰花炒牛肉",
                    price: 25,
                    image: "/static/images/beaf.png",
                    tags: ["高蛋白", "维生素"],
                    info: "增强免疫力",
                    nutrition: {
                        protein: 18,
                        fat: 8,
                        carbs: 15,
                        salt: 2.5
                    },
                    benefits: [
                        "牛肉富含优质蛋白和铁质",
                        "西兰花含丰富维生素C和抗氧化物",
                        "搭配科学，营养均衡"
                    ]
                },
                {
                    id: 4,
                    name: "三文鱼沙拉",
                    price: 32,
                    image: "/static/images/sala.png",
                    tags: ["优质脂肪", "维生素"],
                    info: "心脑血管健康",
                    nutrition: {
                        protein: 15,
                        fat: 12,
                        carbs: 10,
                        salt: 1.5
                    },
                    benefits: [
                        "富含优质Omega-3脂肪酸",
                        "新鲜蔬菜提供充足维生素",
                        "有助于降低胆固醇，保护心血管健康"
                    ]
                },
                {
                    id: 5,
                    name: "山药排骨汤",
                    price: 36,
                    image: "/static/images/shanyao.png",
                    tags: ["滋补", "易消化"],
                    info: "健脾益胃",
                    badge: "老年养生",
                    badgeClass: "bg-warning",
                    nutrition: {
                        protein: 18,
                        fat: 10,
                        carbs: 25,
                        salt: 2.5
                    },
                    benefits: [
                        "含丰富的维生素和矿物质",
                        "山药易于消化吸收，养胃健脾",
                        "排骨提供丰富蛋白质和钙质"
                    ]
                },
                {
                    id: 6,
                    name: "清蒸南瓜",
                    price: 18,
                    image: "/static/images/nangua.png",
                    tags: ["高纤维", "低糖"],
                    info: "适合糖尿病人",
                    badge: "低糖食谱",
                    badgeClass: "bg-info",
                    nutrition: {
                        protein: 5,
                        fat: 1,
                        carbs: 20,
                        salt: 0.5
                    },
                    benefits: [
                        "低糖低脂，适合糖尿病患者",
                        "富含胡萝卜素和膳食纤维",
                        "帮助控制血糖，促进肠道健康"
                    ]
                }
            ]
        },
        watch: {
            showEmergencyModal(newVal) {
                if (newVal) {
                    // 当模态窗口显示时，初始化
                    this.$nextTick(() => {
                        const modal = new bootstrap.Modal(document.getElementById('emergencyModal'));
                        modal.show();

                        // 重置紧急呼叫相关数据
                        this.emergencyStatus = '';
                        this.emergencyType = null;
                        this.countdownTimer = 10;
                    });
                }
            }
        },
        mounted() {
            // 初始化紧急呼叫音频
            this.buzzerAudio = document.getElementById('emergencyAudio');

            // 监听模态窗口事件
            const emergencyModalEl = document.getElementById('emergencyModal');
            if (emergencyModalEl) {
                emergencyModalEl.addEventListener('hidden.bs.modal', () => {
                    // 模态窗口关闭时停止蜂鸣音效
                    this.stopBuzzer();
                    this.stopCountdown();
                    this.emergencyStatus = '';
                    this.showEmergencyModal = false;
                });
            }

            // 加载健康科普文章
            this.loadHealthArticles();
        },
        methods: {
            toggleNav() {
                this.navCollapsed = !this.navCollapsed;
            },
            changeTab(tab) {
                this.currentTab = tab;
                // 切换到健康看板时初始化心率图表
                if (tab === 'health') {
                    this.$nextTick(() => {
                        this.initHeartRateGauge();
                        this.initStepsChart();
                        this.initSleepChart();
                        this.initNutritionChart();
                    });
                }
                // 切换到个人档案时初始化健康图表
                else if (tab === 'profile') {
                    this.$nextTick(() => {
                        this.initBloodPressureChart();
                        this.initBloodSugarChart();
                    });
                }
                // 切换到健康科普时加载文章
                else if (tab === 'health_edu') {
                    if (this.articles.length === 0) {
                        this.loadHealthArticles();
                    }
                }
            },
            // 订餐系统方法
            addToCart(foodId) {
                const food = this.foodList.find(f => f.id === foodId);
                if (!food) return;

                // 查找购物车中是否已有该商品
                const existingItem = this.cart.items.find(item => item.id === foodId);

                if (existingItem) {
                    // 如果已存在，数量+1
                    existingItem.quantity += 1;
                } else {
                    // 如果不存在，添加到购物车
                    this.cart.items.push({
                        id: food.id,
                        name: food.name,
                        price: food.price,
                        image: food.image,
                        quantity: 1,
                        tags: food.tags
                    });
                }

                // 更新总价
                this.updateCartTotal();

                // 直接打开食品详情模态窗口
                this.showFoodDetail(foodId);
            },

            updateCartTotal() {
                this.cart.totalPrice = this.cart.items.reduce((total, item) => {
                    return total + (item.price * item.quantity);
                }, 0);
            },

            showFoodDetail(foodId) {
                // 设置当前食品
                this.currentFood = this.foodList.find(f => f.id === foodId);

                // 使用Bootstrap的modal方法打开模态窗口
                const foodModal = new bootstrap.Modal(document.getElementById('foodDetailModal'));
                foodModal.show();
            },

            increaseQuantity(itemId) {
                const item = this.cart.items.find(i => i.id === itemId);
                if (item) {
                    item.quantity += 1;
                    this.updateCartTotal();
                }
            },

            decreaseQuantity(itemId) {
                const item = this.cart.items.find(i => i.id === itemId);
                if (item && item.quantity > 1) {
                    item.quantity -= 1;
                    this.updateCartTotal();
                } else if (item && item.quantity === 1) {
                    // 如果数量为1，从购物车中移除
                    this.removeFromCart(itemId);
                }
            },

            removeFromCart(itemId) {
                const index = this.cart.items.findIndex(i => i.id === itemId);
                if (index !== -1) {
                    this.cart.items.splice(index, 1);
                    this.updateCartTotal();
                }
            },

            checkout() {
                // 显示支付确认模态窗口
                const paymentModal = new bootstrap.Modal(document.getElementById('paymentModal'));
                paymentModal.show();

                // 隐藏菜品详情模态窗口
                const foodModal = document.getElementById('foodDetailModal');
                const bsModal = bootstrap.Modal.getInstance(foodModal);
                if (bsModal) {
                    bsModal.hide();
                }
            },

            confirmPayment() {
                // 这里可以添加支付逻辑
                alert('订单已成功提交！');
                // 清空购物车
                this.cart.items = [];
                this.updateCartTotal();

                // 隐藏支付模态窗口
                const paymentModal = document.getElementById('paymentModal');
                const bsModal = bootstrap.Modal.getInstance(paymentModal);
                if (bsModal) {
                    bsModal.hide();
                }
            },

            initHeartRateGauge() {
                // 简单模拟心率图表
                const ctx = document.getElementById('heartRateGauge');
                if (ctx) {
                    // 绘制简单的心率仪表盘
                    const gradient = ctx.getContext('2d').createLinearGradient(0, 0, 0, 400);
                    gradient.addColorStop(0, '#5B8FF9');
                    gradient.addColorStop(1, '#5AD8A6');

                    new Chart(ctx, {
                        type: 'doughnut',
                        data: {
                            datasets: [{
                                data: [72, 28],
                                backgroundColor: [gradient, 'rgba(0,0,0,0.05)'],
                                borderWidth: 0
                            }]
                        },
                        options: {
                            cutout: '75%',
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    display: false
                                },
                                tooltip: {
                                    enabled: false
                                }
                            }
                        }
                    });
                }

                // 初始化血压波动曲线图表
                const fluctuationCtx = document.getElementById('bloodPressureFluctuation');
                if (fluctuationCtx) {
                    new Chart(fluctuationCtx, {
                        type: 'line',
                        data: {
                            labels: ['08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00'],
                            datasets: [
                                {
                                    label: '收缩压',
                                    data: [125, 130, 128, 145, 153, 138, 128],
                                    borderColor: '#FF4D4F',
                                    backgroundColor: 'rgba(255, 77, 79, 0.1)',
                                    tension: 0.3,
                                    fill: false,
                                    borderWidth: 2,
                                    pointBackgroundColor: function(context) {
                                        var index = context.dataIndex;
                                        var value = context.dataset.data[index];
                                        return value > 140 ? '#FF4D4F' : '#5B8FF9';
                                    },
                                    pointRadius: function(context) {
                                        var index = context.dataIndex;
                                        var value = context.dataset.data[index];
                                        return value > 140 ? 5 : 3;
                                    },
                                    segment: {
                                        borderColor: function(context) {
                                            return context.p1.parsed.y > 140 ? '#FF4D4F' : '#5B8FF9';
                                        }
                                    }
                                },
                                {
                                    label: '舒张压',
                                    data: [80, 82, 85, 88, 95, 87, 82],
                                    borderColor: '#5AD8A6',
                                    backgroundColor: 'rgba(90, 216, 166, 0.1)',
                                    tension: 0.3,
                                    fill: false,
                                    borderWidth: 2
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: false,
                                    min: 70,
                                    max: 160,
                                    grid: {
                                        drawBorder: false
                                    }
                                },
                                x: {
                                    grid: {
                                        display: false
                                    }
                                }
                            },
                            plugins: {
                                legend: {
                                    display: false
                                }
                            }
                        }
                    });
                }

                // 初始化最近三次测量对比图表
                const comparisonCtx = document.getElementById('comparisionChart');
                if (comparisonCtx) {
                    new Chart(comparisonCtx, {
                        type: 'bar',
                        data: {
                            labels: ['今天上午', '昨天下午', '昨天上午'],
                            datasets: [
                                {
                                    label: '收缩压',
                                    data: [128, 142, 135],
                                    backgroundColor: function(context) {
                                        var index = context.dataIndex;
                                        var value = context.dataset.data[index];
                                        return value > 140 ? 'rgba(255, 77, 79, 0.7)' : 'rgba(91, 143, 249, 0.7)';
                                    },
                                    borderWidth: 0,
                                    barPercentage: 0.7
                                },
                                {
                                    label: '舒张压',
                                    data: [85, 92, 88],
                                    backgroundColor: 'rgba(90, 216, 166, 0.7)',
                                    borderWidth: 0,
                                    barPercentage: 0.7
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: false,
                                    min: 60,
                                    max: 150,
                                    grid: {
                                        drawBorder: false
                                    }
                                },
                                x: {
                                    grid: {
                                        display: false
                                    }
                                }
                            },
                            plugins: {
                                legend: {
                                    display: false
                                }
                            }
                        }
                    });
                }
            },
            // 新增：初始化步数图表
            initStepsChart() {
                const ctx = document.getElementById('stepsProgressChart');
                if (ctx) {
                    const progress = (5246 / 8000) * 100; // 计算完成百分比

                    new Chart(ctx, {
                        type: 'doughnut',
                        data: {
                            datasets: [{
                                data: [progress, 100 - progress],
                                backgroundColor: ['#36A2EB', '#f8f9fa'],
                                borderWidth: 0
                            }]
                        },
                        options: {
                            cutout: '75%',
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    display: false
                                },
                                tooltip: {
                                    enabled: false
                                }
                            }
                        }
                    });
                }
            },
            // 新增：初始化睡眠图表
            initSleepChart() {
                const ctx = document.getElementById('sleepPhaseChart');
                if (ctx) {
                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: ['22:00', '23:00', '00:00', '01:00', '02:00', '03:00', '04:00', '05:00', '06:00'],
                            datasets: [{
                                label: '睡眠阶段',
                                data: [1, 2, 3, 4, 3, 4, 3, 2, 1],
                                backgroundColor: [
                                    'rgba(153, 102, 255, 0.5)', // 浅睡
                                    'rgba(153, 102, 255, 0.7)', // 浅睡
                                    'rgba(54, 162, 235, 0.7)',  // 深睡
                                    'rgba(54, 162, 235, 0.9)',  // 深睡
                                    'rgba(54, 162, 235, 0.7)',  // 深睡
                                    'rgba(54, 162, 235, 0.9)',  // 深睡
                                    'rgba(54, 162, 235, 0.7)',  // 深睡
                                    'rgba(153, 102, 255, 0.7)', // 浅睡
                                    'rgba(153, 102, 255, 0.5)'  // 浅睡
                                ],
                                borderColor: 'rgba(54, 162, 235, 0.1)',
                                borderWidth: 1,
                                barPercentage: 1.0,
                                categoryPercentage: 1.0
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    max: 4,
                                    ticks: {
                                        callback: function(value) {
                                            const labels = ['', '浅睡', '浅睡', '深睡', '深睡'];
                                            return labels[value] || '';
                                        }
                                    }
                                }
                            },
                            plugins: {
                                legend: {
                                    display: false
                                },
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            const value = context.parsed.y;
                                            const sleepPhases = ['醒着', '浅睡', '浅睡', '深睡', '深睡'];
                                            return sleepPhases[value] || '';
                                        }
                                    }
                                }
                            }
                        }
                    });
                }
            },
            // 新增：初始化营养图表
            initNutritionChart() {
                const ctx = document.getElementById('nutritionChart');
                if (ctx) {
                    new Chart(ctx, {
                        type: 'pie',
                        data: {
                            labels: ['蛋白质', '碳水化合物', '脂肪'],
                            datasets: [{
                                data: [65, 220, 45],
                                backgroundColor: [
                                    'rgba(54, 162, 235, 0.7)',
                                    'rgba(75, 192, 192, 0.7)',
                                    'rgba(255, 159, 64, 0.7)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'bottom'
                                }
                            }
                        }
                    });
                }
            },
            initBloodPressureChart() {
                const ctx = document.getElementById('bloodPressureChart');
                if (ctx) {
                    new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                            datasets: [
                                {
                                    label: '收缩压',
                                    data: [130, 128, 125, 132, 126, 120, 122],
                                    borderColor: '#5B8FF9',
                                    backgroundColor: 'rgba(91, 143, 249, 0.2)',
                                    tension: 0.3,
                                    fill: false
                                },
                                {
                                    label: '舒张压',
                                    data: [85, 82, 80, 84, 81, 78, 80],
                                    borderColor: '#5AD8A6',
                                    backgroundColor: 'rgba(90, 216, 166, 0.2)',
                                    tension: 0.3,
                                    fill: false
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: false,
                                    min: 60,
                                    max: 160,
                                    grid: {
                                        drawBorder: false
                                    }
                                },
                                x: {
                                    grid: {
                                        display: false
                                    }
                                }
                            },
                            plugins: {
                                legend: {
                                    position: 'top'
                                },
                                annotation: {
                                    annotations: {
                                        line1: {
                                            type: 'line',
                                            yMin: 140,
                                            yMax: 140,
                                            borderColor: 'rgba(255, 77, 79, 0.5)',
                                            borderWidth: 1,
                                            borderDash: [5, 5],
                                            label: {
                                                content: '收缩压警戒线',
                                                display: true,
                                                position: 'right'
                                            }
                                        },
                                        line2: {
                                            type: 'line',
                                            yMin: 90,
                                            yMax: 90,
                                            borderColor: 'rgba(255, 77, 79, 0.5)',
                                            borderWidth: 1,
                                            borderDash: [5, 5],
                                            label: {
                                                content: '舒张压警戒线',
                                                display: true,
                                                position: 'right'
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    });
                }
            },
            initBloodSugarChart() {
                const ctx = document.getElementById('bloodSugarChart');
                if (ctx) {
                    new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                            datasets: [
                                {
                                    label: '空腹血糖',
                                    data: [5.8, 6.1, 5.9, 6.2, 6.0, 5.7, 5.8],
                                    borderColor: '#FF4D4F',
                                    backgroundColor: 'rgba(255, 77, 79, 0.2)',
                                    tension: 0.3,
                                    fill: false
                                },
                                {
                                    label: '餐后2小时',
                                    data: [7.6, 7.9, 7.8, 8.1, 7.7, 7.5, 7.4],
                                    borderColor: '#FAAD14',
                                    backgroundColor: 'rgba(250, 173, 20, 0.2)',
                                    tension: 0.3,
                                    fill: false
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: false,
                                    min: 4,
                                    max: 10,
                                    grid: {
                                        drawBorder: false
                                    }
                                },
                                x: {
                                    grid: {
                                        display: false
                                    }
                                }
                            },
                            plugins: {
                                legend: {
                                    position: 'top'
                                }
                            }
                        }
                    });
                }
            },
            toggleTimeline() {
                this.timelineCollapsed = !this.timelineCollapsed;
            },
            expandArticle(articleId) {
                this.articleExpanded = true;
                // 在这里添加展开文章内容的逻辑
            },
            closeArticle() {
                this.articleExpanded = false;
            },
            playAudio(audioId) {
                // 创建语音合成对象
                const synth = window.speechSynthesis;
                let text = '';

                // 根据传入的audioId获取要读取的文本
                if (audioId === 'point1') {
                    text = '老年高血压患者的控制目标应为收缩压140mmHg以下，舒张压90mmHg以下';
                } else if (audioId === 'point2') {
                    text = '每日食盐摄入量应控制在5克以内，膳食应减少脂肪摄入，增加蔬果比例';
                } else if (audioId === 'point3') {
                    text = '高血压用药需规律服用，不可自行调整药量或停药，避免血压波动';
                }

                // 创建语音对象
                const utterThis = new SpeechSynthesisUtterance(text);

                // 设置语言为中文
                utterThis.lang = 'zh-CN';

                // 播放语音
                synth.speak(utterThis);
            },
            playVideo() {
                // 显示视频播放模态窗口
                new bootstrap.Modal(document.getElementById('videoModal')).show();
            },
            toggleBookmark() {
                // 显示收藏成功模态窗口
                new bootstrap.Modal(document.getElementById('bookmarkModal')).show();
            },
            showProgress() {
                // 显示学习进度模态窗口
                new bootstrap.Modal(document.getElementById('progressModal')).show();
            },
            showMedication() {
                // 显示用药信息模态窗口
                new bootstrap.Modal(document.getElementById('medicationModal')).show();
            },
            askQuestion() {
                // 显示提问模态窗口
                new bootstrap.Modal(document.getElementById('questionModal')).show();
            },
            adjustFontSize(delta) {
                // 调整字体大小
                this.userFontSize = Math.max(16, Math.min(32, this.userFontSize + delta));

                // 设置CSS变量
                document.documentElement.style.setProperty('--user-font-size', this.userFontSize + 'px');

                // 显示调整后的字体大小
                alert('字体大小已调整为: ' + this.userFontSize + 'px');
            },
            switchColorMode(mode) {
                // 移除所有颜色模式激活状态
                document.querySelectorAll('.color-mode').forEach(el => {
                    el.classList.remove('active');
                });

                // 添加当前选中的颜色模式激活状态
                document.querySelector('.color-mode-' + mode).classList.add('active');

                // 设置颜色模式
                this.colorMode = mode;

                // 应用颜色模式样式
                const body = document.querySelector('body');
                body.classList.remove('mode-default', 'mode-high-contrast', 'mode-low-vision');
                body.classList.add('mode-' + mode);

                // 根据不同模式设置不同的样式
                if (mode === 'default') {
                    document.documentElement.style.setProperty('--bg-color', '#ffffff');
                    document.documentElement.style.setProperty('--text-color', '#333333');
                } else if (mode === 'high-contrast') {
                    document.documentElement.style.setProperty('--bg-color', '#000000');
                    document.documentElement.style.setProperty('--text-color', '#ffffff');
                } else if (mode === 'low-vision') {
                    document.documentElement.style.setProperty('--bg-color', '#C7E4F2');
                    document.documentElement.style.setProperty('--text-color', '#1A3658');
                }
            },
            activateVoiceSearch() {
                // 检查浏览器是否支持语音识别
                if ('webkitSpeechRecognition' in window) {
                    alert('请说出您想了解的健康知识，例如"高血压"、"糖尿病"等');
                    // 实际项目中这里会启动语音识别功能
                } else {
                    alert('很抱歉，您的浏览器不支持语音识别功能');
                }
            },
            startReadAloud() {
                if (!this.readingAloud) {
                    // 开始朗读
                    this.readingAloud = true;

                    // 获取文章内容
                    let articleContent = '';
                    const contentElements = document.querySelectorAll('.article-content p');
                    contentElements.forEach(el => {
                        articleContent += el.textContent + ' ';
                    });

                    // 创建语音合成对象
                    const synth = window.speechSynthesis;
                    const utterThis = new SpeechSynthesisUtterance(articleContent);

                    // 设置语言为中文
                    utterThis.lang = 'zh-CN';

                    // 语音播放结束回调
                    utterThis.onend = () => {
                        this.readingAloud = false;
                    };

                    // 播放语音
                    synth.speak(utterThis);

                    alert('开始朗读文章');
                } else {
                    // 停止朗读
                    window.speechSynthesis.cancel();
                    this.readingAloud = false;

                    alert('停止朗读');
                }
            },
            showEmergencyModal() {
                // 重置状态
                this.emergencyType = null;
                this.emergencyStatus = '';

                // 显示紧急呼叫模态窗口
                new bootstrap.Modal(document.getElementById('emergencyModal')).show();
            },
            // 新增方法
            initEmergencyMap(containerId = 'emergencyMap', showRescue = false) {
                // 这里使用已有的高德地图API
                try {
                    const container = document.getElementById(containerId);
                    if (!container) return;

                    // 清除现有地图
                    if (this.emergencyMap) {
                        this.emergencyMap.destroy();
                    }

                    // 创建地图实例
                    const map = new AMap.Map(containerId, {
                        zoom: 15,
                        center: [this.currentLocation?.lng || 116.4074, this.currentLocation?.lat || 39.9042]
                    });

                    // 添加标记
                    const marker = new AMap.Marker({
                        position: [this.currentLocation?.lng || 116.4074, this.currentLocation?.lat || 39.9042],
                        title: '当前位置'
                    });

                    map.add(marker);

                    // 添加定位圆圈
                    const circle = new AMap.Circle({
                        center: [this.currentLocation?.lng || 116.4074, this.currentLocation?.lat || 39.9042],
                        radius: 50,
                        fillColor: 'rgba(255,77,79,0.2)',
                        strokeColor: '#FF4D4F',
                        strokeWeight: 2
                    });

                    map.add(circle);

                    // 如果显示救援信息，添加救援路线
                    if (showRescue || this.emergencyStatus === 'connected') {
                        // 模拟救援路线
                        const path = [
                            [this.currentLocation?.lng || 116.4074, this.currentLocation?.lat || 39.9042],
                            [(this.currentLocation?.lng || 116.4074) + 0.005, (this.currentLocation?.lat || 39.9042) + 0.002],
                            [(this.currentLocation?.lng || 116.4074) + 0.008, (this.currentLocation?.lat || 39.9042) - 0.001]
                        ];

                        const polyline = new AMap.Polyline({
                            path: path,
                            strokeColor: '#1890FF',
                            strokeWeight: 5,
                            strokeStyle: 'solid'
                        });

                        map.add(polyline);

                        // 救援人员位置标记
                        const rescuerMarker = new AMap.Marker({
                            position: path[path.length - 1],
                            title: '救援人员',
                            content: '<div style="background-color: #1890FF; color: white; padding: 5px 10px; border-radius: 50%; box-shadow: 0 0 10px rgba(0,0,0,0.2);"><i class="bi bi-truck"></i></div>'
                        });

                        map.add(rescuerMarker);
                    }

                    this.emergencyMap = map;
                } catch (error) {
                    console.error('初始化地图失败:', error);
                }
            },
            getAddress(lng, lat) {
                // 使用高德地图API进行逆地理编码
                if (typeof AMap !== 'undefined') {
                    AMap.plugin('AMap.Geocoder', () => {
                        const geocoder = new AMap.Geocoder();
                        geocoder.getAddress([lng, lat], (status, result) => {
                            if (status === 'complete' && result.info === 'OK') {
                                const address = result.regeocode.formattedAddress;
                                document.getElementById('current-address').textContent = address;

                                // 保存地址信息用于发送
                                this.currentLocation = {
                                    address: address,
                                    lng: lng,
                                    lat: lat
                                };
                            } else {
                                document.getElementById('current-address').textContent = '地址解析失败';
                            }
                        });
                    });
                }
            },
            selectEmergencyType(type) {
                // 设置紧急情况类型
                let statusText = '';
                switch(type) {
                    case 'medical':
                        statusText = '医疗紧急情况已报告，正在联系医疗人员...';
                        break;
                    case 'fall':
                        statusText = '跌倒求助信息已发送，正在联系附近救援人员...';
                        break;
                    case 'help':
                        statusText = '求助信息已发送，社区工作人员将尽快联系您...';
                        break;
                }
                this.emergencyStatus = statusText;
            },
            startEmergencyCall() {
                if (!this.emergencyType) return;

                // 开始蜂鸣音效
                this.startBuzzer();

                // 更新状态
                this.emergencyStatus = 'calling';

                // 重置倒计时
                this.countdownTimer = 10;

                // 开始倒计时
                this.startCountdown();

                // 获取位置
                this.getLocation();

                // 初始化地图
                this.$nextTick(() => {
                    this.initEmergencyMap();
                });

                // 模拟3秒后连接成功
                setTimeout(() => {
                    this.emergencyStatus = 'connected';
                    this.stopCountdown();

                    // 更新地图
                    this.$nextTick(() => {
                        // 更新同一个地图来显示救援路线
                        this.initEmergencyMap('emergencyMap', true);
                    });
                }, 5000);
            },
            cancelEmergencyCall() {
                this.stopBuzzer();
                this.stopCountdown();
                this.emergencyStatus = '';

                // 关闭模态窗口
                const modalEl = document.getElementById('emergencyModal');
                const modal = bootstrap.Modal.getInstance(modalEl);
                if (modal) modal.hide();
            },
            startBuzzer() {
                if (this.buzzerAudio) {
                    this.buzzerAudio.play().catch(e => console.error('无法播放音频:', e));
                }
            },
            stopBuzzer() {
                if (this.buzzerAudio) {
                    this.buzzerAudio.pause();
                    this.buzzerAudio.currentTime = 0;
                }
            },
            startCountdown() {
                this.stopCountdown(); // 确保先停止之前的倒计时

                this.countdownInterval = setInterval(() => {
                    if (this.countdownTimer > 1) {
                        this.countdownTimer--;
                    } else {
                        this.stopCountdown();
                    }
                }, 1000);
            },
            stopCountdown() {
                if (this.countdownInterval) {
                    clearInterval(this.countdownInterval);
                    this.countdownInterval = null;
                }
            },
            async getLocation() {
                try {
                    // 模拟获取位置信息
                    // 实际应用中应使用浏览器的定位API
                    await new Promise(resolve => setTimeout(resolve, 1500));
                    this.currentLocation = {
                        lat: 39.9042,
                        lng: 116.4074,
                        address: '杭州市西湖区西溪路'
                    };
                    return this.currentLocation;
                } catch (error) {
                    console.error('获取位置失败:', error);
                    this.currentLocation = {
                        lat: 39.9042,
                        lng: 116.4074,
                        address: '杭州市西湖区西溪路' // 默认位置
                    };
                    return this.currentLocation;
                }
            },
        },
        mounted() {
            // 初始化当前标签页的图表
            this.initHeartRateGauge();
            // 初始化新增的图表
            this.initStepsChart();
            this.initSleepChart();
            this.initNutritionChart();
        }
    });
</script>
{% endblock %}

