#!/usr/bin/env python
# -*- coding: utf-8 -*-

from flask import Flask, render_template
import os

# 创建简单的Flask应用
app = Flask(__name__, 
           static_folder='app/static',
           template_folder='app/templates')

app.config['SECRET_KEY'] = 'dev-key-for-elderly-care-system'

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/elderly')
def elderly_center():
    return render_template('elderly_center.html')

@app.route('/family')
def family_center():
    return render_template('family_center.html')

@app.route('/worker')
def worker_dashboard():
    return render_template('worker_dashboard.html')

if __name__ == '__main__':
    print("🚀 启动简单测试服务器...")
    print("📱 老人中心: http://127.0.0.1:5000/elderly")
    print("👨‍👩‍👧‍👦 家属中心: http://127.0.0.1:5000/family")
    print("👷‍♂️ 工作人员: http://127.0.0.1:5000/worker")
    app.run(debug=True, host='0.0.0.0', port=5000)
