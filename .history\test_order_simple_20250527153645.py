#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的测试订单提交脚本 - 使用urllib
"""

import urllib.request
import urllib.parse
import json

def test_submit_order():
    """测试提交订单"""
    url = "http://127.0.0.1:5000/api/food_orders/submit"
    
    # 测试E02用户的订单（地址应该是西湖区西溪路518号）
    order_data = {
        "user_id": "E02",
        "items": [
            {"id": "1", "name": "红烧肉", "price": 25, "quantity": 1},
            {"id": "2", "name": "白米饭", "price": 3, "quantity": 2}
        ],
        "total_price": 31,
        "delivery_time": "2025-01-26 12:00",
        "delivery_fee": 5,
        "payment_method": "子女代付",
        "special_notes": "请轻敲门，老人听力不好"
    }
    
    try:
        # 准备请求数据
        data = json.dumps(order_data).encode('utf-8')
        
        # 创建请求
        req = urllib.request.Request(url, data=data)
        req.add_header('Content-Type', 'application/json')
        
        # 发送请求
        with urllib.request.urlopen(req) as response:
            response_data = json.loads(response.read().decode('utf-8'))
            print(f"响应状态码: {response.status}")
            print(f"响应内容: {response_data}")
            
            if response.status == 201:
                print("✅ 订单提交成功！")
                print(f"订单ID: {response_data['order_id']}")
                print(f"送餐地址: {response_data['order']['delivery_address']}")
            else:
                print("❌ 订单提交失败")
                
    except Exception as e:
        print(f"请求失败: {e}")

def test_get_orders():
    """测试获取订单列表"""
    url = "http://127.0.0.1:5000/api/worker/meal_orders"
    
    try:
        with urllib.request.urlopen(url) as response:
            data = json.loads(response.read().decode('utf-8'))
            print(f"\n获取订单列表 - 响应状态码: {response.status}")
            
            if response.status == 200:
                print(f"✅ 获取订单成功！总数: {data['total_count']}")
                
                for order in data['meal_services']:
                    print(f"\n订单 {order['id']}:")
                    print(f"  用户: {order['name']} ({order['elderly_id']})")
                    print(f"  地址: {order['address']}")
                    print(f"  状态: {order['status']}")
            else:
                print("❌ 获取订单失败")
                
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    print("=== 测试订单地址显示 ===")
    print("1. 提交E02用户的测试订单...")
    test_submit_order()
    
    print("\n2. 获取订单列表验证地址...")
    test_get_orders()
