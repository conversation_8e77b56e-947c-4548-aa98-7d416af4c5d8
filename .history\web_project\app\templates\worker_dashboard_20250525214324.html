{% extends "base.html" %}

{% block title %}社区工作人员 - 居家养老健康管理系统{% endblock %}

{% block head %}
<style>
    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 5px;
    }

    .status-pending { background-color: #ffc107; }
    .status-responded { background-color: #17a2b8; }
    .status-resolved { background-color: #28a745; }

    .emergency-call {
        border-left: 4px solid #dc3545;
    }

    .elderly-card {
        transition: all 0.3s;
    }

    .elderly-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    }

    .map-container {
        height: 400px;
        border-radius: 8px;
        overflow: hidden;
    }

    .stats-card {
        border-radius: 8px;
        border-left: 4px solid;
        transition: all 0.2s;
    }

    .stats-card:hover {
        transform: translateY(-3px);
    }

    .stats-pending { border-left-color: #ffc107; }
    .stats-responded { border-left-color: #17a2b8; }
    .stats-resolved { border-left-color: #28a745; }
    .stats-total { border-left-color: #6c757d; }

    .meal-service-card {
        border-left: 4px solid #28a745;
        margin-bottom: 15px;
        transition: all 0.3s;
    }

    .meal-service-card:hover {
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transform: translateY(-3px);
    }

    .service-status {
        width: 100px;
        text-align: center;
        padding: 5px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: bold;
    }

    .status-pending-meal {
        background-color: rgba(255, 193, 7, 0.2);
        color: #ffc107;
    }

    .status-assigned {
        background-color: rgba(23, 162, 184, 0.2);
        color: #17a2b8;
    }

    .status-delivered {
        background-color: rgba(40, 167, 69, 0.2);
        color: #28a745;
    }

    .status-delivering {
        background-color: rgba(0, 123, 255, 0.2);
        color: #007bff;
    }

    .status-completed {
        background-color: rgba(108, 117, 125, 0.2);
        color: #6c757d;
    }

    .staff-selector {
        max-width: 250px;
    }

    .nav-tabs .nav-link {
        font-size: 1.1rem;
        font-weight: 500;
    }

    .service-item-details {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-top: 10px;
        border-left: 3px solid #6c757d;
    }

    .meal-items {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 10px;
    }

    .meal-item {
        background-color: #fff;
        padding: 5px 10px;
        border-radius: 20px;
        border: 1px solid #dee2e6;
        font-size: 0.9rem;
    }

    .assignment-form {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-top: 15px;
        border: 1px solid #dee2e6;
    }

    /* 工作人员端订单进度条样式 */
    .order-progress-worker {
        margin-top: 20px;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 10px;
        border: 1px solid #e9ecef;
    }

    .progress-steps-worker {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        position: relative;
        margin-top: 15px;
    }

    .progress-steps-worker::before {
        content: '';
        position: absolute;
        top: 20px;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #dee2e6;
        z-index: 1;
    }

    .step-worker {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        position: relative;
        flex: 1;
        z-index: 2;
    }

    .step-icon-worker {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #dee2e6;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        margin-bottom: 10px;
        transition: all 0.3s ease;
        border: 3px solid #fff;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .step-content-worker {
        max-width: 120px;
    }

    .step-content-worker h6 {
        font-size: 0.9rem;
        margin-bottom: 5px;
        font-weight: 600;
    }

    .step-content-worker small {
        font-size: 0.75rem;
        line-height: 1.2;
    }

    /* 激活状态 */
    .step-worker.active .step-icon-worker {
        background-color: #007bff;
        color: white;
        animation: pulse 2s infinite;
    }

    .step-worker.active .step-content-worker h6 {
        color: #007bff;
        font-weight: bold;
    }

    /* 完成状态 */
    .step-worker.completed .step-icon-worker {
        background-color: #28a745;
        color: white;
    }

    .step-worker.completed .step-content-worker h6 {
        color: #28a745;
        font-weight: bold;
    }

    /* 等待状态 */
    .step-worker.pending .step-icon-worker {
        background-color: #ffc107;
        color: white;
    }

    .step-worker.pending .step-content-worker h6 {
        color: #ffc107;
        font-weight: bold;
    }

    /* 脉冲动画 */
    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
        }
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .progress-steps-worker {
            flex-direction: column;
            align-items: flex-start;
        }

        .progress-steps-worker::before {
            display: none;
        }

        .step-worker {
            flex-direction: row;
            align-items: center;
            text-align: left;
            margin-bottom: 15px;
            width: 100%;
        }

        .step-icon-worker {
            margin-right: 15px;
            margin-bottom: 0;
        }

        .step-content-worker {
            max-width: none;
        }
    }
</style>
{% endblock %}

{% block content %}
<div id="worker-app" class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-body d-flex align-items-center">
                    <div class="avatar bg-primary text-white rounded-circle p-3 me-3">
                        <h2 class="mb-0">👩‍⚕️</h2>
                    </div>
                    <div>
                        <h4 class="mb-1">欢迎，刘强</h4>
                        <p class="text-muted mb-0">社区工作人员 | 负责区域: A区</p>
                    </div>
                    <div class="ms-auto">
                        <span class="badge bg-success">当前在线</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card stats-total shadow-sm">
                <div class="card-body">
                    <h6 class="text-muted">总老年人数</h6>
                    <h3 class="mb-0">24</h3>
                    <small class="text-success">+2 本月新增</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card stats-pending shadow-sm">
                <div class="card-body">
                    <h6 class="text-muted">待处理紧急呼叫</h6>
                    <h3 class="mb-0">2</h3>
                    <small class="text-danger">需要立即处理</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card stats-responded shadow-sm">
                <div class="card-body">
                    <h6 class="text-muted">已响应服务</h6>
                    <h3 class="mb-0">8</h3>
                    <small class="text-primary">正在进行中</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card stats-resolved shadow-sm">
                <div class="card-body">
                    <h6 class="text-muted">本周已解决</h6>
                    <h3 class="mb-0">15</h3>
                    <small class="text-success">完成率 93%</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">服务任务管理中心</h5>
                </div>
                <div class="card-body">
                    <!-- 任务类型选项卡 -->
                    <ul class="nav nav-tabs mb-3" id="serviceTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="emergency-tab" data-bs-toggle="tab" data-bs-target="#emergency-content" type="button" role="tab">
                                <i class="bi bi-exclamation-triangle me-1"></i> 紧急呼叫
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="meal-tab" data-bs-toggle="tab" data-bs-target="#meal-content" type="button" role="tab">
                                <i class="bi bi-cup-hot me-1"></i> 订餐送餐
                            </button>
                        </li>

                    </ul>

                    <!-- 选项卡内容 -->
                    <div class="tab-content" id="serviceTabContent">
                        <!-- 紧急呼叫选项卡内容 -->
                        <div class="tab-pane fade show active" id="emergency-content" role="tabpanel">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">紧急呼叫与服务请求</h5>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="showResolvedSwitch" v-model="showResolved">
                                    <label class="form-check-label" for="showResolvedSwitch">显示已解决</label>
                                </div>
                            </div>

                    <ul class="list-group">
                        <li class="list-group-item emergency-call">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <span class="badge bg-danger">紧急</span>
                                </div>
                                <div>
                                    <h6 class="mb-1">张建国 (E01)</h6>
                                    <p class="mb-0 text-muted small">位置: 浙江省杭州市余杭区龙湖天街1号| 时间: 2025-05-06 10:15</p>
                                </div>
                                <div class="ms-auto">
                                    <button class="btn btn-sm btn-outline-primary" @click="respondToCall('EC01')">响应</button>
                                </div>
                            </div>
                        </li>
                        <li class="list-group-item emergency-call">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <span class="badge bg-danger">紧急</span>
                                </div>
                                <div>
                                    <h6 class="mb-1">李淑兰 (E02)</h6>
                                    <p class="mb-0 text-muted small">位置: 浙江省杭州市西湖区西溪路518号 | 时间: 2025-05-06 09:42</p>
                                </div>
                                <div class="ms-auto">
                                    <button class="btn btn-sm btn-outline-primary" @click="respondToCall('EC02')">响应</button>
                                </div>
                            </div>
                        </li>
                            </ul>
                        </div>

                        <!-- 订餐送餐选项卡内容 -->
                        <div class="tab-pane fade" id="meal-content" role="tabpanel">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div>
                                    <h5 class="mb-0">今日订餐送餐服务</h5>
                                    <small class="text-muted">
                                        总计: [[ mealServicesStats.total ]] |
                                        待分配: <span class="text-warning">[[ mealServicesStats.pending ]]</span> |
                                        已分配: <span class="text-primary">[[ mealServicesStats.assigned ]]</span> |
                                        已送达: <span class="text-success">[[ mealServicesStats.delivered ]]</span>
                                    </small>
                                </div>
                            <div class="d-flex align-items-center">
                                    <div class="input-group me-2 staff-selector">
                                        <select class="form-select" v-model="selectedStaff">
                                            <option value="">选择服务人员</option>
                                            <option value="1">王小明</option>
                                            <option value="2">李玲</option>
                                            <option value="3">赵强</option>
                                        </select>
                                        <button class="btn btn-primary" @click="assignAllToStaff">分配</button>
                                    </div>
                                    <button class="btn btn-outline-secondary me-2" @click="loadMealOrders" :disabled="isLoadingMealServices">
                                        <i class="bi bi-arrow-clockwise"></i>
                                        <span v-if="isLoadingMealServices">刷新中...</span>
                                        <span v-else>刷新</span>
                                    </button>
                                    <button class="btn btn-outline-secondary">
                                        <i class="bi bi-funnel"></i> 筛选
                                    </button>
                                </div>
                            </div>

                            <!-- 加载状态 -->
                            <div v-if="isLoadingMealServices" class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2 text-muted">正在加载订餐订单...</p>
                            </div>

                            <!-- 无订单状态 -->
                            <div v-else-if="mealServices.length === 0" class="text-center py-4">
                                <i class="bi bi-inbox fs-1 text-muted"></i>
                                <p class="mt-2 text-muted">暂无订餐订单</p>
                            </div>

                            <!-- 订餐服务列表 -->
                            <div v-else v-for="service in mealServices" :key="service.id" class="meal-service-card card mb-3">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <span class="avatar text-white rounded-circle d-flex align-items-center justify-content-center"
                                                      :class="getAvatarClass(service.status)"
                                                      style="width: 40px; height: 40px;">
                                                    [[ service.user_name ? service.user_name.charAt(0) : 'U' ]]
                                                </span>
                                            </div>
                                            <div>
                                                <h6 class="mb-1">[[ service.name ]] ([[ service.elderly_id ]])</h6>
                                                <p class="mb-0 text-muted small">
                                                    <i class="bi bi-geo-alt-fill me-1"></i>[[ service.address ]]
                                                    <i class="bi bi-clock-fill ms-2 me-1"></i>[[ service.time ]]
                                                </p>
                                            </div>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <div class="service-status me-3" :class="getStatusClass(service.status)">
                                                [[ getStatusText(service.status) ]]
                                            </div>
                                            <div class="dropdown">
                                                <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    管理
                                                </button>
                                                <ul class="dropdown-menu" z-index="100">
                                                    <li><a class="dropdown-item" href="#" @click.prevent="showDetails(service.id)">查看详情</a></li>
                                                    <li v-if="service.status === 'pending'">
                                                        <a class="dropdown-item" href="#" @click.prevent="reassignService(service.id)">分配服务人员</a>
                                                    </li>
                                                    <li v-if="service.status === 'assigned'">
                                                        <a class="dropdown-item" href="#" @click.prevent="reassignService(service.id)">重新分配</a>
                                                    </li>
                                                    <li v-if="service.status === 'assigned'">
                                                        <a class="dropdown-item" href="#" @click.prevent="markAsDelivered(service.id)">标记为已送达</a>
                                                    </li>
                                                    <li v-if="service.status === 'delivered'">
                                                        <a class="dropdown-item" href="#" @click.prevent="viewFeedback(service.id)">查看反馈</a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 详情区域，初始隐藏，点击后显示 -->
                                    <div class="service-item-details" v-if="activeService === service.id">
                                        <div class="d-flex justify-content-between mb-3">
                                            <h6 class="mb-0"><i class="bi bi-card-list me-2"></i>订单详情</h6>
                                            <span class="text-muted">订单号: [[ service.id ]]</span>
                                        </div>



                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <p class="mb-2"><i class="bi bi-telephone-fill me-2 text-primary"></i>联系电话: [[ service.user_phone || '未设置' ]]</p>
                                                <p class="mb-2"><i class="bi bi-person-fill me-2 text-primary"></i>服务人员: [[ service.assigned_staff_name || '未分配' ]]</p>
                                                <p class="mb-0"><i class="bi bi-chat-left-text-fill me-2 text-primary"></i>备注: [[ service.special_notes || '无特殊备注' ]]</p>
                                            </div>
                                            <div class="col-md-6">
                                                <p class="mb-2"><i class="bi bi-calendar-check-fill me-2 text-primary"></i>订餐时间: [[ formatDateTime(service.created_at) ]]</p>
                                                <p class="mb-2"><i class="bi bi-currency-yen me-2 text-primary"></i>订单金额: ¥[[ service.final_price || service.total_price || 0 ]]</p>
                                                <p class="mb-0"><i class="bi bi-credit-card-fill me-2 text-primary"></i>支付方式: [[ service.payment_method || '未设置' ]]</p>
                                            </div>
                                        </div>

                                        <h6 class="mb-2"><i class="bi bi-basket2-fill me-2"></i>餐品列表</h6>
                                        <div class="meal-items mb-4">
                                            <div v-for="item in service.items" :key="item.id" class="meal-item">
                                                [[ item.name ]] x[[ item.quantity ]]
                                            </div>
                                        </div>



                                        <div class="assignment-form" v-if="showAssignForm === service.id">
                                            <h6 class="mb-3">[[ service.status === 'pending' ? '分配服务人员' : '重新分配服务人员' ]]</h6>
                                            <div class="row g-2">
                                                <div class="col-md-6">
                                                    <select class="form-select" v-model="newAssignedStaff">
                                                        <option value="">选择服务人员</option>
                                                        <option value="1">王小明</option>
                                                        <option value="2">李玲</option>
                                                        <option value="3">赵强</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-6 d-grid">
                                                    <button class="btn btn-primary" @click="confirmReassign(service.id)">确认分配</button>
                                                </div>
                                            </div>
                                        </div>
                                                                                <!-- 订单进度条 -->
                                                                                <div class="order-progress-worker mb-4">
                                                                                    <h6 class="mb-3"><i class="bi bi-clock-history me-2 text-info"></i>订单进度跟踪</h6>
                                                                                    <div class="progress-steps-worker">
                                                                                        <!-- 步骤1：订单提交 -->
                                                                                        <div class="step-worker" :class="getWorkerStepClass(service.status, 1)">
                                                                                            <div class="step-icon-worker">
                                                                                                <i class="bi bi-check-circle-fill"></i>
                                                                                            </div>
                                                                                            <div class="step-content-worker">
                                                                                                <h6>订单已提交</h6>
                                                                                                <small class="text-muted">[[ formatDateTime(service.created_at) ]]</small>
                                                                                            </div>
                                                                                        </div>

                                                                                        <!-- 步骤2：已分配 -->
                                                                                        <div class="step-worker" :class="getWorkerStepClass(service.status, 2)">
                                                                                            <div class="step-icon-worker">
                                                                                                <i class="bi bi-person-check-fill"></i>
                                                                                            </div>
                                                                                            <div class="step-content-worker">
                                                                                                <h6>已分配配送员</h6>
                                                                                                <small v-if="service.assigned_staff_name" class="text-success fw-bold">[[ service.assigned_staff_name ]]</small>
                                                                                                <small v-else class="text-warning">等待分配...</small>
                                                                                            </div>
                                                                                        </div>

                                                                                        <!-- 步骤3：配送中 -->
                                                                                        <div class="step-worker" :class="getWorkerStepClass(service.status, 3)">
                                                                                            <div class="step-icon-worker">
                                                                                                <i class="bi bi-truck"></i>
                                                                                            </div>
                                                                                            <div class="step-content-worker">
                                                                                                <h6>配送中</h6>
                                                                                                <small class="text-primary">配送员正在路上</small>
                                                                                            </div>
                                                                                        </div>

                                                                                        <!-- 步骤4：已送达 -->
                                                                                        <div class="step-worker" :class="getWorkerStepClass(service.status, 4)">
                                                                                            <div class="step-icon-worker">
                                                                                                <i class="bi bi-house-check-fill"></i>
                                                                                            </div>
                                                                                            <div class="step-content-worker">
                                                                                                <h6>已送达</h6>
                                                                                                <small v-if="service.status === 'delivered'" class="text-warning">
                                                                                                    <i class="bi bi-clock-fill me-1"></i>等待用户确认收货
                                                                                                </small>
                                                                                                <small v-else-if="service.status === 'completed'" class="text-success">
                                                                                                    <i class="bi bi-check-circle-fill me-1"></i>用户已确认收货
                                                                                                </small>
                                                                                                <small v-else class="text-muted">等待送达</small>
                                                                                            </div>
                                                                                        </div>

                                                                                        <!-- 步骤5：已完成 -->
                                                                                        <div class="step-worker" :class="getWorkerStepClass(service.status, 5)">
                                                                                            <div class="step-icon-worker">
                                                                                                <i class="bi bi-star-fill"></i>
                                                                                            </div>
                                                                                            <div class="step-content-worker">
                                                                                                <h6>订单完成</h6>
                                                                                                <small class="text-success">
                                                                                                    <i class="bi bi-trophy-fill me-1"></i>服务完成
                                                                                                </small>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>

                                        <!-- 用户反馈区域 -->
                                        <div class="alert alert-success mt-3" v-if="activeFeedback === service.id && service.status === 'delivered'">
                                            <h6 class="mb-2"><i class="bi bi-chat-square-text-fill me-2"></i>用户反馈</h6>
                                            <p class="mb-1">评分: <i class="bi bi-star-fill text-warning"></i><i class="bi bi-star-fill text-warning"></i><i class="bi bi-star-fill text-warning"></i><i class="bi bi-star-fill text-warning"></i><i class="bi bi-star text-warning"></i></p>
                                            <p class="mb-0">反馈: 服务态度很好，餐食温度适宜，下次还会选择。</p>
                                        </div>
                                    </div>
                                </div>
                            </div>


                        </div>


                    </div>
                </div>
            </div>

            <!-- 继续现有的地图卡片 -->
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">社区老年人分布</h5>
                </div>
                <div class="card-body">
                    <div class="map-container">
                        <div id="community-map" style="height: 100%"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="mb-0">负责区域老年人</h5>
                </div>
                <div class="card-body">
                    <div class="input-group mb-3">
                        <input type="text" class="form-control" placeholder="搜索老年人..." v-model="searchQuery">
                        <button class="btn btn-outline-secondary" type="button">搜索</button>
                    </div>

                    <div class="elderly-card card mb-3">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <span class="avatar bg-success text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">张</span>
                                </div>
                                <div>
                                    <h6 class="mb-1">张建国 (E01)</h6>
                                    <p class="mb-0 text-muted small">浙江省杭州市余杭区龙湖天街1号 | 78岁</p>
                                </div>
                                <div class="ms-auto">
                                    <div class="status-indicator status-pending" title="有待处理的紧急呼叫"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="elderly-card card mb-3">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <span class="avatar bg-info text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">李</span>
                                </div>
                                <div>
                                    <h6 class="mb-1">李淑兰 (E02)</h6>
                                    <p class="mb-0 text-muted small">浙江省杭州市西湖区西溪路518号 | 75岁</p>
                                </div>
                                <div class="ms-auto">
                                    <div class="status-indicator status-pending" title="有待处理的紧急呼叫"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="elderly-card card mb-3">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <span class="avatar bg-warning text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">王</span>
                                </div>
                                <div>
                                    <h6 class="mb-1">王福海 (E03)</h6>
                                    <p class="mb-0 text-muted small">浙江省杭州市拱墅区湖墅南路88号 | 80岁</p>
                                </div>
                                <div class="ms-auto">
                                    <div class="status-indicator status-responded" title="已响应服务"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center">
                        <button class="btn btn-outline-primary">查看更多老年人</button>
                    </div>
                </div>
            </div>

            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">健康异常预警</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="bi bi-exclamation-triangle-fill fs-4"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">李淑兰 (E02)</h6>
                                <p class="mb-0">血糖偏高: 8.2 mmol/L</p>
                                <small class="text-muted">2025-05-06 08:30</small>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-danger">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="bi bi-heart-fill fs-4"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">王福海 (E03)</h6>
                                <p class="mb-0">心率异常: 95 bpm</p>
                                <small class="text-muted">2025-05-06 09:15</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    new Vue({
        el: '#worker-app',
        delimiters: ['[[', ']]'],
        data: {
            searchQuery: '',
            emergencyCalls: [
                { id: 'EC01', elderly_id: 'E01', name: '张建国', status: '待处理' },
                { id: 'EC02', elderly_id: 'E02', name: '李淑兰', status: '待处理' }
            ],
            // 新增数据
            showResolved: false,
            activeService: null,
            showAssignForm: null,
            newAssignedStaff: '',
            selectedStaff: '',
            activeFeedback: null,
            mealServices: [], // 将从API动态加载
            isLoadingMealServices: false,
            mealServicesStats: {
                total: 0,
                pending: 0,
                assigned: 0,
                delivering: 0,
                delivered: 0,
                completed: 0
            },
            lastOrderCount: 0, // 用于检测新订单
            orderPollingInterval: null, // 订单状态轮询定时器
            previousMealServices: [], // 用于比较订单状态变化
            map: null,
            elderlyLocations: [],
            elderlyList: [],
            workers: [
                { name: '刘强', region: 'A' },
                { name: '王丽', region: 'B' }
            ],
            currentView: 'mealServices' // 当前视图
        },
        mounted() {
            this.initMap();
            this.loadMealOrders();

            // 启动订单状态轮询
            this.startOrderStatusPolling();
        },
        beforeDestroy() {
            // 清理轮询定时器
            this.stopOrderStatusPolling();
        },
        methods: {
            async initMap() {
                // 初始化高德地图
                const map = new AMap.Map('community-map', {
                    zoom: 14,
                    center: [116.4074, 39.9042]
                });

                this.map = map;

                // 从API获取老年人位置数据
                await this.loadElderlyLocations();
            },

            async loadElderlyLocations() {
                try {
                    // 获取当前工作人员负责的区域
                    const currentWorker = this.workers.find(w => w.name === '刘强'); // 这里应该从登录信息获取
                    const region = currentWorker ? currentWorker.region : '';

                    const response = await axios.get('/api/emergency/elderly/locations', {
                        params: { region: region }
                    });

                    if (response.data && response.data.locations) {
                        this.elderlyLocations = response.data.locations;
                        this.addMarkersToMap();
                        this.updateElderlyList();
                    }
                } catch (error) {
                    console.error('加载老年人位置失败:', error);
                    alert('加载老年人位置信息失败，请刷新页面重试');
                }
            },

            addMarkersToMap() {
                if (!this.map) return;

                // 清除现有标记
                this.map.clearMap();

                // 添加老年人位置标记
                this.elderlyLocations.forEach(elderly => {
                    if (elderly.gps_location) {
                        const [lat, lng] = elderly.gps_location.split(',').map(Number);

                        const marker = new AMap.Marker({
                            position: [lng, lat],
                            title: `${elderly.name} (${elderly.elderly_id})`,
                            map: this.map,
                            icon: new AMap.Icon({
                                image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
                                size: new AMap.Size(25, 34),
                                imageSize: new AMap.Size(25, 34)
                            })
                        });

                        // 添加信息窗口
                        const infoWindow = new AMap.InfoWindow({
                            content: `
                                <div style="padding: 10px;">
                                    <h6>${elderly.name} (${elderly.elderly_id})</h6>
                                    <p><strong>地址:</strong> ${elderly.address || '未设置'}</p>
                                    <p><strong>电话:</strong> ${elderly.phone || '未设置'}</p>
                                    <p><strong>紧急联系人:</strong> ${elderly.emergency_contact_name || '未设置'}</p>
                                    <p><strong>联系电话:</strong> ${elderly.emergency_contact_phone || '未设置'}</p>
                                </div>
                            `,
                            offset: new AMap.Pixel(0, -30)
                        });

                        marker.on('click', () => {
                            infoWindow.open(this.map, marker.getPosition());
                        });
                    }
                });

                // 自动调整地图视野以包含所有标记
                if (this.elderlyLocations.length > 0) {
                    const bounds = new AMap.Bounds();
                    this.elderlyLocations.forEach(elderly => {
                        if (elderly.gps_location) {
                            const [lat, lng] = elderly.gps_location.split(',').map(Number);
                            bounds.extend([lng, lat]);
                        }
                    });
                    this.map.setBounds(bounds);
                }
            },

            updateElderlyList() {
                // 更新老年人列表显示
                this.elderlyList = this.elderlyLocations.map(elderly => ({
                    id: elderly.elderly_id,
                    name: elderly.name,
                    address: elderly.address,
                    phone: elderly.phone,
                    status: '正常', // 这里可以根据实际情况设置状态
                    lastContact: '今天 09:30' // 这里可以根据实际数据设置
                }));
            },

            // 加载订餐订单数据
            async loadMealOrders() {
                if (this.isLoadingMealServices) return; // 防止重复请求

                try {
                    this.isLoadingMealServices = true;
                    console.log('正在加载订餐订单数据...');

                    const response = await fetch('/api/worker/meal_orders');

                    // 检查网络连接
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const result = await response.json();

                    if (result.success) {
                        // 检测新订单
                        const newOrderCount = result.total_count || 0;
                        if (this.lastOrderCount > 0 && newOrderCount > this.lastOrderCount) {
                            this.showNewOrderNotification(newOrderCount - this.lastOrderCount);
                        }
                        this.lastOrderCount = newOrderCount;

                        // 保存当前已完成的订单，避免被覆盖
                        const currentCompletedOrders = this.mealServices.filter(order => order.status === 'completed');

                        // 更新订单数据，但保持已完成订单的状态
                        const newServices = result.meal_services || [];

                        // 合并数据：新数据中的非完成订单 + 当前已完成的订单
                        this.mealServices = newServices.map(newOrder => {
                            // 检查是否有对应的已完成订单
                            const completedOrder = currentCompletedOrders.find(completed => completed.id === newOrder.id);
                            if (completedOrder) {
                                // 如果本地已经是完成状态，保持完成状态
                                console.log(`保持订单 ${newOrder.id} 的完成状态`);
                                return completedOrder;
                            }
                            return newOrder;
                        });

                        // 添加新完成的订单到已完成列表
                        const newlyCompleted = newServices.filter(order =>
                            order.status === 'completed' &&
                            !currentCompletedOrders.some(completed => completed.id === order.id)
                        );

                        if (newlyCompleted.length > 0) {
                            newlyCompleted.forEach(order => {
                                console.log(`订单 ${order.id} 新完成，添加到完成列表`);
                                this.showOrderCompletedNotification(order);
                            });
                        }

                        this.mealServicesStats = {
                            total: result.total_count || 0,
                            pending: result.pending_count || 0,
                            assigned: result.assigned_count || 0,
                            delivering: result.delivering_count || 0,
                            delivered: result.delivered_count || 0,
                            completed: result.completed_count || 0
                        };

                        console.log(`成功加载 ${this.mealServices.length} 个订餐订单`);

                        // 如果有新的待分配订单，显示提醒
                        if (result.pending_count > 0) {
                            console.log(`有 ${result.pending_count} 个待分配的订餐订单`);
                        }

                        // 检查是否有订单状态变化需要提醒
                        this.checkOrderStatusChanges();
                    } else {
                        console.error('加载订餐订单失败:', result.error);
                        // 不更新数据，保持当前状态
                    }
                } catch (error) {
                    console.error('加载订餐订单数据失败:', error);
                    // 网络错误时不更新数据，避免状态回退
                    if (error.message.includes('ERR_CONNECTION_REFUSED') || error.message.includes('Failed to fetch')) {
                        console.warn('网络连接失败，保持当前订单状态');
                    }
                } finally {
                    this.isLoadingMealServices = false;
                }
            },

            respondToCall(callId) {
                // 模拟响应紧急呼叫
                const call = this.emergencyCalls.find(c => c.id === callId);
                if (call) {
                    call.status = '已响应';
                    alert(`已响应${call.name}的紧急呼叫，请立即前往处理`);
                }
            },

            // 新增方法
            showDetails(serviceId) {
                // 显示/隐藏服务详情
                this.activeService = this.activeService === serviceId ? null : serviceId;
                this.showAssignForm = null; // 关闭分配表单
                this.activeFeedback = null; // 关闭反馈
            },

            reassignService(serviceId) {
                // 显示分配服务人员表单
                this.activeService = serviceId;
                this.showAssignForm = serviceId;
                this.newAssignedStaff = '';
            },

            async confirmReassign(serviceId) {
                // 确认分配服务人员
                if (!this.newAssignedStaff) {
                    alert('请选择服务人员');
                    return;
                }

                try {
                    // 获取服务人员名称
                    let staffName = '';
                    switch(this.newAssignedStaff) {
                        case '1': staffName = '王小明'; break;
                        case '2': staffName = '李玲'; break;
                        case '3': staffName = '赵强'; break;
                    }

                    // 调用后端API分配服务人员
                    const response = await fetch('/api/worker/assign_meal_order', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            order_id: serviceId,
                            assigned_staff: this.newAssignedStaff
                        })
                    });

                    const result = await response.json();

                    if (response.ok && result.success) {
                        alert(`已将订单分配给 ${result.staff_name}`);

                        // 更新本地数据
                        const service = this.mealServices.find(s => s.id === serviceId);
                        if (service) {
                            service.status = 'assigned';
                            service.staff = staffName;
                        }

                        this.showAssignForm = null;
                        this.newAssignedStaff = '';

                        // 重新加载订单数据以确保同步
                        await this.loadMealOrders();
                    } else {
                        throw new Error(result.error || '分配失败');
                    }
                } catch (error) {
                    console.error('分配服务人员失败:', error);
                    alert(`分配失败: ${error.message}`);
                }
            },

            async markAsDelivered(serviceId) {
                // 标记送餐已完成
                try {
                    const response = await fetch('/api/worker/update_meal_order_status', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            order_id: serviceId,
                            status: 'delivered'
                        })
                    });

                    const result = await response.json();

                    if (response.ok && result.success) {
                        // 更新本地数据
                        const service = this.mealServices.find(s => s.id === serviceId);
                        if (service) {
                            service.status = 'delivered';
                            alert(`已将 ${service.name} 的送餐服务标记为已送达`);
                        }

                        // 重新加载订单数据以确保同步
                        await this.loadMealOrders();
                    } else {
                        throw new Error(result.error || '状态更新失败');
                    }
                } catch (error) {
                    console.error('更新订单状态失败:', error);
                    alert(`状态更新失败: ${error.message}`);
                }
            },

            viewFeedback(serviceId) {
                // 查看用户反馈
                this.activeService = serviceId;
                this.activeFeedback = serviceId;
                this.showAssignForm = null;
            },

            async assignAllToStaff() {
                // 批量分配服务人员
                if (!this.selectedStaff) {
                    alert('请选择服务人员');
                    return;
                }

                try {
                    // 获取服务人员名称
                    let staffName = '';
                    switch(this.selectedStaff) {
                        case '1': staffName = '王小明'; break;
                        case '2': staffName = '李玲'; break;
                        case '3': staffName = '赵强'; break;
                    }

                    // 获取所有待分配的订单
                    const pendingOrders = this.mealServices.filter(service => service.status === 'pending');

                    if (pendingOrders.length === 0) {
                        alert('没有待分配的送餐服务');
                        return;
                    }

                    // 批量分配
                    let successCount = 0;
                    let failCount = 0;

                    for (const order of pendingOrders) {
                        try {
                            const response = await fetch('/api/worker/assign_meal_order', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({
                                    order_id: order.id,
                                    assigned_staff: this.selectedStaff
                                })
                            });

                            const result = await response.json();
                            if (response.ok && result.success) {
                                successCount++;
                            } else {
                                failCount++;
                                console.error(`分配订单 ${order.id} 失败:`, result.error);
                            }
                        } catch (error) {
                            failCount++;
                            console.error(`分配订单 ${order.id} 失败:`, error);
                        }
                    }

                    // 显示结果
                    if (successCount > 0) {
                        alert(`已将 ${successCount} 项待分配的送餐服务分配给 ${staffName}${failCount > 0 ? `，${failCount} 项分配失败` : ''}`);
                    } else {
                        alert('批量分配失败，请稍后重试');
                    }

                    this.selectedStaff = '';

                    // 重新加载订单数据
                    await this.loadMealOrders();
                } catch (error) {
                    console.error('批量分配失败:', error);
                    alert(`批量分配失败: ${error.message}`);
                }
            },



            // 辅助方法：获取头像背景色类
            getAvatarClass(status) {
                switch(status) {
                    case 'pending_assignment':
                    case 'pending': return 'bg-warning';
                    case 'assigned': return 'bg-primary';
                    case 'delivering': return 'bg-info';
                    case 'delivered': return 'bg-success';
                    case 'completed': return 'bg-secondary';
                    default: return 'bg-secondary';
                }
            },

            // 辅助方法：格式化日期时间
            formatDateTime(dateTimeStr) {
                if (!dateTimeStr) return '未设置';
                try {
                    const date = new Date(dateTimeStr);
                    return date.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                } catch (error) {
                    return dateTimeStr;
                }
            },

            // 显示新订单通知
            showNewOrderNotification(count) {
                // 创建通知元素
                const notification = document.createElement('div');
                notification.className = 'alert alert-success alert-dismissible fade show position-fixed';
                notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
                notification.innerHTML = `
                    <i class="bi bi-bell-fill me-2"></i>
                    <strong>新订单提醒</strong><br>
                    收到 ${count} 个新的订餐订单，请及时处理！
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                document.body.appendChild(notification);

                // 3秒后自动消失
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 3000);

                console.log(`🔔 新订单提醒：收到 ${count} 个新订单`);
            },

            // 显示订单完成通知
            showOrderCompletedNotification(order) {
                // 创建通知元素
                const notification = document.createElement('div');
                notification.className = 'alert alert-info alert-dismissible fade show position-fixed';
                notification.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 350px;';
                notification.innerHTML = `
                    <i class="bi bi-check-circle-fill me-2"></i>
                    <strong>订单完成通知</strong><br>
                    ${order.name} 的订单 ${order.id} 已确认收货完成！
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                document.body.appendChild(notification);

                // 5秒后自动消失
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 5000);

                console.log(`✅ 订单完成通知：${order.name} 的订单 ${order.id} 已完成`);
            },

            // 检查订单状态变化
            checkOrderStatusChanges() {
                // 检查是否有新完成的订单（从delivered变为completed）
                if (this.previousMealServices && this.previousMealServices.length > 0) {
                    const previousCompleted = this.previousMealServices.filter(order => order.status === 'completed');
                    const currentCompleted = this.mealServices.filter(order => order.status === 'completed');

                    // 找出新完成的订单
                    const newlyCompleted = currentCompleted.filter(current =>
                        !previousCompleted.some(prev => prev.id === current.id)
                    );

                    if (newlyCompleted.length > 0) {
                        newlyCompleted.forEach(order => {
                            this.showOrderCompletedNotification(order);
                        });
                    }

                    // 检查是否有新的已送达订单
                    const previousDelivered = this.previousMealServices.filter(order => order.status === 'delivered');
                    const currentDelivered = this.mealServices.filter(order => order.status === 'delivered');

                    const newlyDelivered = currentDelivered.filter(current =>
                        !previousDelivered.some(prev => prev.id === current.id)
                    );

                    if (newlyDelivered.length > 0) {
                        console.log(`📦 有 ${newlyDelivered.length} 个新订单已送达，等待用户确认收货`);
                    }
                }

                // 保存当前状态用于下次比较
                this.previousMealServices = JSON.parse(JSON.stringify(this.mealServices));
            },

            // 开始轮询订单状态更新
            startOrderStatusPolling() {
                // 清除之前的轮询
                if (this.orderPollingInterval) {
                    clearInterval(this.orderPollingInterval);
                }

                // 开始新的轮询，每5秒检查一次订单状态
                this.orderPollingInterval = setInterval(async () => {
                    if (this.currentView === 'mealServices') {
                        // 检查是否还有未完成的订单
                        const activeOrders = this.mealServices.filter(order =>
                            order.status !== 'completed'
                        );

                        if (activeOrders.length > 0) {
                            // 还有未完成的订单，继续轮询
                            try {
                                await this.loadMealOrders();
                            } catch (error) {
                                console.warn('轮询过程中发生错误，将在下次轮询时重试:', error);
                            }
                        } else {
                            // 所有订单都已完成，减少轮询频率
                            console.log('所有订单已完成，保持当前状态');
                        }
                    }
                }, 5000);

                console.log('已启动订单状态轮询，每5秒更新一次');
            },

            // 停止轮询订单状态更新
            stopOrderStatusPolling() {
                if (this.orderPollingInterval) {
                    clearInterval(this.orderPollingInterval);
                    this.orderPollingInterval = null;
                    console.log('已停止订单状态轮询');
                }
            },

            // 更新状态类映射
            getStatusClass(status) {
                switch(status) {
                    case 'pending_assignment':
                    case 'pending': return 'status-pending-meal';
                    case 'assigned': return 'status-assigned';
                    case 'delivering': return 'status-delivering';
                    case 'delivered': return 'status-delivered';
                    case 'completed': return 'status-completed';
                    default: return 'status-pending-meal';
                }
            },

            // 更新状态文本映射
            getStatusText(status) {
                switch(status) {
                    case 'pending_assignment':
                    case 'pending': return '待分配';
                    case 'assigned': return '已分配';
                    case 'delivering': return '配送中';
                    case 'delivered': return '已送达';
                    case 'completed': return '已完成';
                    default: return '待处理';
                }
            },

            // 获取工作人员端进度条步骤样式
            getWorkerStepClass(status, stepNumber) {
                const statusMap = {
                    'pending_assignment': 1,
                    'pending': 1,
                    'assigned': 2,
                    'delivering': 3,
                    'delivered': 4,
                    'completed': 5
                };

                const currentStep = statusMap[status] || 1;

                if (stepNumber < currentStep) {
                    return 'completed';
                } else if (stepNumber === currentStep) {
                    return 'active';
                } else {
                    return '';
                }
            }
        }
    });
</script>
{% endblock %}