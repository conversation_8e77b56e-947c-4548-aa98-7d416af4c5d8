<!-- 点餐态 - 沉浸式选购（初始时隐藏） -->
<div class="modal fade" id="foodDetailModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h4 class="modal-title"><i class="bi bi-info-circle me-2"></i>菜品详情</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
                <div class="row g-0">
                    <!-- 左侧（60%）：菜品详情 -->
                    <div class="col-md-7 p-4">
                        <div class="food-detail-container">
                            <div class="text-center mb-3">
                                <img v-if="currentFood" :src="currentFood.image" class="img-fluid rounded" style="max-height: 300px;" :alt="currentFood.name">
                                <img v-else src="https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg" class="img-fluid rounded" style="max-height: 300px;" alt="食品图片">
                                <div class="mt-2">
                                    <button class="btn btn-outline-primary me-2">
                                        <i class="bi bi-arrows-angle-expand"></i> 放大查看
                                    </button>
                                    <button class="btn btn-outline-success">
                                        <i class="bi bi-volume-up-fill"></i> 语音介绍
                                    </button>
                                </div>
                            </div>

                            <h3 class="fw-bold mb-3" v-if="currentFood">${ currentFood.name }$</h3>
                            <h3 class="fw-bold mb-3" v-else>菜品详情</h3>

                            <div class="nutrition-info mb-4">
                                <h5 class="border-bottom pb-2"><i class="bi bi-pie-chart-fill me-2 text-primary"></i>营养金字塔</h5>
                                <div class="row text-center" v-if="currentFood && currentFood.nutrition">
                                    <div class="col-3">
                                        <div class="py-2 px-1 bg-primary bg-opacity-10 rounded mb-1">
                                            <span class="fw-bold d-block fs-4">${ currentFood.nutrition.protein }$g</span>
                                            <small>蛋白质</small>
                                        </div>
                                        <small class="text-muted">≈ ${ Math.round(currentFood.nutrition.protein/10) }$个鸡蛋</small>
                                    </div>
                                    <div class="col-3">
                                        <div class="py-2 px-1 bg-success bg-opacity-10 rounded mb-1">
                                            <span class="fw-bold d-block fs-4">${ currentFood.nutrition.fat }$g</span>
                                            <small>脂肪</small>
                                        </div>
                                        <small class="text-muted">≈ ${ currentFood.nutrition.fat }$茶匙油</small>
                                    </div>
                                    <div class="col-3">
                                        <div class="py-2 px-1 bg-warning bg-opacity-10 rounded mb-1">
                                            <span class="fw-bold d-block fs-4">${ currentFood.nutrition.carbs }$g</span>
                                            <small>碳水</small>
                                        </div>
                                        <small class="text-muted">≈ ${ Math.round(currentFood.nutrition.carbs/30) }$碗米饭</small>
                                    </div>
                                    <div class="col-3">
                                        <div class="py-2 px-1 bg-danger bg-opacity-10 rounded mb-1">
                                            <span class="fw-bold d-block fs-4">${ currentFood.nutrition.salt }$g</span>
                                            <small>盐分</small>
                                        </div>
                                        <small class="text-muted">≈ ${ currentFood.nutrition.salt }$粒花生米</small>
                                    </div>
                                </div>
                                <div class="row text-center" v-else>
                                    <div class="col-12">
                                        <p class="text-muted">加载中...</p>
                                    </div>
                                </div>
                            </div>

                            <div class="health-benefits mb-4">
                                <h5 class="border-bottom pb-2"><i class="bi bi-heart-pulse-fill me-2 text-danger"></i>健康益处</h5>
                                <ul class="list-group list-group-flush" v-if="currentFood && currentFood.benefits">
                                    <li class="list-group-item d-flex align-items-center border-0 ps-0" v-for="(benefit, index) in currentFood.benefits" :key="'benefit-'+index">
                                        <i class="bi bi-check-circle-fill text-success me-2 fs-5"></i>
                                        <span class="fs-5">${ benefit }$</span>
                                    </li>
                                </ul>
                                <ul class="list-group list-group-flush" v-else>
                                    <li class="list-group-item d-flex align-items-center border-0 ps-0">
                                        <span class="text-muted">加载中...</span>
                                    </li>
                                </ul>
                            </div>

                            <div class="preparation-info">
                                <h5 class="border-bottom pb-2"><i class="bi bi-info-circle-fill me-2 text-info"></i>制作与配送</h5>
                                <p class="fs-5">制作时间：约30分钟 | 当日新鲜配送</p>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧（40%）：购物车简版 -->
                    <div class="col-md-5 bg-light p-4">
                        <h4 class="mb-3 fw-bold"><i class="bi bi-cart4 me-2 text-primary"></i>您的购物车</h4>

                        <div class="cart-items-container mb-4">
                            <div v-if="!cart.items || cart.items.length === 0" class="text-center py-4">
                                <i class="bi bi-cart-x fs-1 text-muted"></i>
                                <p class="fs-5 mt-2">购物车还是空的</p>
                                <button class="btn btn-outline-primary fs-5 mt-2" data-bs-dismiss="modal">
                                    <i class="bi bi-plus-circle me-1"></i> 去选择美味佳肴
                                </button>
                            </div>

                            <div v-for="item in cart.items" :key="'cart-item-'+item.id" class="cart-item bg-white p-3 rounded mb-3 shadow-sm">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <img :src="item.image" class="rounded me-3" width="60" height="60" :alt="item.name">
                                        <div>
                                            <h5 class="mb-0 fw-bold">${ item.name }$</h5>
                                            <span v-for="(tag, tagIndex) in item.tags" :key="'tag-'+tagIndex" class="badge bg-primary me-1">${ tag }$</span>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <span class="fw-bold fs-5">¥${ item.price }$</span>
                                        <div class="btn-group mt-1">
                                            <button class="btn btn-outline-secondary" @click="decreaseQuantity(item.id)">
                                                <i class="bi bi-dash"></i>
                                            </button>
                                            <span class="btn btn-outline-secondary disabled">${ item.quantity }$</span>
                                            <button class="btn btn-outline-secondary" @click="increaseQuantity(item.id)">
                                                <i class="bi bi-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div v-if="cart.items && cart.items.length > 0" class="text-center mb-3">
                                <button class="btn btn-outline-primary fs-5" data-bs-dismiss="modal">
                                    <i class="bi bi-plus-circle me-1"></i> 继续添加其他菜品
                                </button>
                            </div>
                        </div>

                        <div class="price-summary border-top border-bottom py-3 my-3">
                            <div class="d-flex justify-content-between fs-5 mb-2">
                                <span>商品小计</span>
                                <span>¥${ cart.totalPrice || 0 }$</span>
                            </div>
                            <div class="d-flex justify-content-between fs-5 mb-2">
                                <span>配送费</span>
                                <span>¥5</span>
                            </div>
                            <div class="d-flex justify-content-between fw-bold fs-4">
                                <span>总计</span>
                                <span class="text-danger">¥${ (cart.totalPrice || 0) + 5 }$</span>
                            </div>
                        </div>

                        <div class="delivery-preferences mb-3">
                            <label class="form-label fs-5 fw-bold">希望送达时间</label>
                            <select class="form-select form-select-lg fs-5 mb-3">
                                <option selected>11:30-12:00 (推荐)</option>
                                <option>12:00-12:30</option>
                                <option>12:30-13:00</option>
                            </select>

                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="callBeforeDelivery" checked>
                                <label class="form-check-label fs-5" for="callBeforeDelivery">
                                    送餐前电话通知
                                </label>
                            </div>
                        </div>

                        <button class="btn btn-success btn-lg w-100 py-3 fs-4" @click="checkout()" :disabled="!cart.items || cart.items.length === 0">
                            <i class="bi bi-wallet2 me-2"></i> 去结算
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 支付态 - 无忧确认（初始时隐藏） -->
<div class="modal fade" id="paymentModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h4 class="modal-title"><i class="bi bi-wallet2 me-2"></i>订单确认</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <div class="row">
                    <div class="col-md-6">
                        <h4 class="mb-3"><i class="bi bi-geo-alt-fill me-2 text-danger"></i>配送信息</h4>
                        <!-- 使用语音播报的配送时间选择 -->
                        <div class="mb-3">
                            <label class="form-label fs-5">配送时间</label>
                            <div class="d-flex align-items-center">
                                <select class="form-select form-select-lg fs-5">
                                    <option selected>11:30-12:00 (推荐)</option>
                                    <option>12:00-12:30</option>
                                    <option>12:30-13:00</option>
                                </select>
                                <button class="btn btn-outline-primary ms-2" title="语音播报可选时段">
                                    <i class="bi bi-volume-up-fill"></i>
                                </button>
                            </div>
                        </div>

                        <div class="card mb-3">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-house-door-fill fs-1 text-primary me-3"></i>
                                    <div>
                                        <h5 class="mb-1">默认地址: 和平小区5号楼3单元102室</h5>
                                        <p class="mb-0">王大伯 (185****1234)</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label fs-5">配送备注</label>
                            <div class="d-flex align-items-center">
                                <select class="form-select form-select-lg fs-5">
                                    <option selected>放门口，不用按门铃</option>
                                    <option>送到家里，需要帮忙打开</option>
                                    <option>请提前电话联系</option>
                                </select>
                                <button class="btn btn-outline-primary ms-2">
                                    <i class="bi bi-pencil-fill"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <h4 class="mb-3"><i class="bi bi-credit-card-fill me-2 text-success"></i>支付方式</h4>
                        <!-- 仅显示3个最常用选项 -->
                        <div class="payment-options">
                            <div class="card mb-2 border-primary">
                                <div class="card-body">
                                    <div class="form-check d-flex align-items-center">
                                        <input class="form-check-input me-3" type="radio" name="paymentMethod" id="paymentBalance" checked style="width: 25px; height: 25px;">
                                        <label class="form-check-label d-flex align-items-center justify-content-between w-100" for="paymentBalance">
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-wallet2-fill fs-1 text-primary me-3"></i>
                                                <div>
                                                    <h5 class="mb-0 fs-4">账户余额支付</h5>
                                                    <p class="mb-0">余额: ¥208.50</p>
                                                </div>
                                            </div>
                                            <i class="bi bi-check-circle-fill text-primary fs-4"></i>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="card mb-2">
                                <div class="card-body">
                                    <div class="form-check d-flex align-items-center">
                                        <input class="form-check-input me-3" type="radio" name="paymentMethod" id="paymentFamily" style="width: 25px; height: 25px;">
                                        <label class="form-check-label d-flex align-items-center justify-content-between w-100" for="paymentFamily">
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-people-fill fs-1 text-success me-3"></i>
                                                <div>
                                                    <h5 class="mb-0 fs-4">子女代付</h5>
                                                    <p class="mb-0">微信通知家人支付</p>
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="card mb-2">
                                <div class="card-body">
                                    <div class="form-check d-flex align-items-center">
                                        <input class="form-check-input me-3" type="radio" name="paymentMethod" id="paymentSubsidy" style="width: 25px; height: 25px;">
                                        <label class="form-check-label d-flex align-items-center justify-content-between w-100" for="paymentSubsidy">
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-building-fill fs-1 text-warning me-3"></i>
                                                <div>
                                                    <h5 class="mb-0 fs-4">政府补贴</h5>
                                                    <p class="mb-0">每月补贴剩余: ¥180</p>
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 订单摘要 -->
                        <div class="order-summary mt-4">
                            <h5 class="border-bottom pb-2">订单摘要</h5>
                            <div v-if="cart.items && cart.items.length > 0">
                                <div v-for="(item, itemIndex) in cart.items" :key="'summary-item-'+itemIndex" class="d-flex justify-content-between fs-5 mb-2">
                                    <span>${ item.name }$ x${ item.quantity }$</span>
                                    <span>¥${ item.price * item.quantity }$</span>
                                </div>
                            </div>
                            <div v-else>
                                <p class="text-muted">购物车为空</p>
                            </div>
                            <div class="d-flex justify-content-between fs-5 mb-2">
                                <span>配送费</span>
                                <span>¥5</span>
                            </div>
                            <div class="d-flex justify-content-between fw-bold fs-4 text-danger">
                                <span>总计</span>
                                <span>¥${ (cart.totalPrice || 0) + 5 }$</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 应急联系浮窗 -->
                <div class="alert alert-info d-flex align-items-center mt-4">
                    <i class="bi bi-info-circle-fill fs-4 me-3"></i>
                    <div>
                        <h5 class="mb-1">订单通知已自动发送给家属</h5>
                        <p class="mb-0">王女士(女儿) 将收到订单消息提醒</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary btn-lg fs-5" data-bs-dismiss="modal">返回修改</button>
                <button type="button" class="btn btn-success btn-lg fs-4 px-5" @click="confirmPayment()">
                    <i class="bi bi-check-circle-fill me-2"></i> 确认支付 ¥${ (cart.totalPrice || 0) + 5 }$
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 订单状态跟踪模态窗口 -->
<div class="modal fade" id="orderStatusModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h4 class="modal-title"><i class="bi bi-check-circle-fill me-2"></i>订单状态跟踪</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <!-- 订单基本信息 -->
                <div class="alert alert-success d-flex align-items-center mb-4">
                    <i class="bi bi-check-circle-fill fs-3 me-3"></i>
                    <div>
                        <h5 class="mb-1">订单提交成功！</h5>
                        <p class="mb-0">订单号：<strong id="orderNumber">加载中...</strong></p>
                    </div>
                </div>

                <!-- 订单状态进度条 -->
                <div class="order-progress mb-4">
                    <div class="progress-steps">
                        <!-- 阶段一：订单提交 -->
                        <div class="step active" id="step1">
                            <div class="step-icon">
                                <i class="bi bi-check-circle-fill"></i>
                            </div>
                            <div class="step-content">
                                <h6>订单已提交</h6>
                                <p class="text-muted small">正在等待社区人员分配配送员</p>
                                <span class="timestamp" id="submitTime">刚刚</span>
                            </div>
                        </div>

                        <!-- 阶段二：已分配配送员 -->
                        <div class="step" id="step2">
                            <div class="step-icon">
                                <i class="bi bi-person-check"></i>
                            </div>
                            <div class="step-content">
                                <h6>已分配配送员</h6>
                                <p class="text-muted small" id="deliveryInfo">等待分配配送员...</p>
                                <span class="timestamp" id="assignTime"></span>
                            </div>
                        </div>

                        <!-- 阶段三：配送中 -->
                        <div class="step" id="step3">
                            <div class="step-icon">
                                <i class="bi bi-truck"></i>
                            </div>
                            <div class="step-content">
                                <h6>配送中</h6>
                                <p class="text-muted small" id="deliveryProgress">配送员正在路上...</p>
                                <span class="timestamp" id="deliveryTime"></span>
                            </div>
                        </div>

                        <!-- 阶段四：已送达 -->
                        <div class="step" id="step4">
                            <div class="step-icon">
                                <i class="bi bi-house-check"></i>
                            </div>
                            <div class="step-content">
                                <h6>订单已送达</h6>
                                <p class="text-muted small">请确认收到餐品</p>
                                <span class="timestamp" id="arrivalTime"></span>
                                <div class="mt-2" id="confirmSection" style="display: none;">
                                    <button class="btn btn-success btn-lg" onclick="confirmReceipt()">
                                        <i class="bi bi-check-circle me-2"></i>确认收到餐品
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 阶段五：已完成 -->
                        <div class="step" id="step5">
                            <div class="step-icon">
                                <i class="bi bi-star-fill"></i>
                            </div>
                            <div class="step-content">
                                <h6>订单完成</h6>
                                <p class="text-muted small">感谢您的使用，祝您用餐愉快！</p>
                                <span class="timestamp" id="completeTime"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 订单详情 -->
                <div class="order-details">
                    <h6><i class="bi bi-receipt me-2"></i>订单详情</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <tbody id="orderItemsList">
                                <!-- 动态填充订单项目 -->
                            </tbody>
                            <tfoot>
                                <tr class="table-active">
                                    <td><strong>总计</strong></td>
                                    <td class="text-end"><strong id="orderTotal">¥0</strong></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>

                <!-- 联系信息 -->
                <div class="contact-info mt-3">
                    <div class="alert alert-info">
                        <h6><i class="bi bi-telephone-fill me-2"></i>如有问题，请联系</h6>
                        <p class="mb-1"><strong>社区服务热线：</strong>************</p>
                        <p class="mb-0"><strong>紧急联系人：</strong>王女士(女儿) 139****5678</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="refreshOrderStatus()">
                    <i class="bi bi-arrow-clockwise me-2"></i>刷新状态
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// 全局函数：确认收货
async function confirmReceipt() {
    // 尝试多种方式获取当前订单ID
    let currentOrderId = null;

    if (window.foodOrderManager && window.foodOrderManager.currentOrderId) {
        currentOrderId = window.foodOrderManager.currentOrderId;
    } else if (window.foodOrderModule && window.foodOrderModule.data.currentOrderId) {
        currentOrderId = window.foodOrderModule.data.currentOrderId;
    } else {
        // 从订单号元素获取
        const orderNumberElement = document.getElementById('orderNumber');
        if (orderNumberElement && orderNumberElement.textContent && orderNumberElement.textContent !== '加载中...') {
            currentOrderId = orderNumberElement.textContent;
        }
    }

    if (!currentOrderId) {
        console.error('无法获取当前订单ID');
        alert('无法获取订单信息，请刷新页面重试');
        return;
    }

    try {
        console.log('确认收货订单:', currentOrderId);

        const response = await fetch(`/api/food_orders/confirm_receipt/${currentOrderId}`, {
            method: 'POST'
        });

        const result = await response.json();

        if (response.ok && result.success) {
            console.log('确认收货成功:', result);

            // 立即更新状态
            if (window.foodOrderManager && window.foodOrderManager.checkOrderStatus) {
                await window.foodOrderManager.checkOrderStatus();
            } else if (window.foodOrderModule && window.foodOrderModule.methods.checkOrderStatus) {
                await window.foodOrderModule.methods.checkOrderStatus.call(window.foodOrderModule.data);
            } else {
                // 手动刷新订单状态
                refreshOrderStatus();
            }

            alert('确认收货成功！订单已完成。');
        } else {
            console.error('确认收货失败:', result.error);
            alert('确认收货失败，请稍后重试');
        }
    } catch (error) {
        console.error('确认收货失败:', error);
        alert('确认收货失败，请稍后重试');
    }
}

// 全局函数：刷新订单状态
function refreshOrderStatus() {
    if (window.foodOrderManager && window.foodOrderManager.checkOrderStatus) {
        window.foodOrderManager.checkOrderStatus();
    } else {
        console.error('checkOrderStatus 方法不可用');
    }
}

// 全局函数：打开订单状态跟踪
async function openOrderStatus(orderId) {
    try {
        // 获取订单详情
        const response = await fetch(`/api/food_orders/status/${orderId}`);
        const result = await response.json();

        if (response.ok && result.success) {
            // 尝试多种方式设置当前订单ID和显示模态窗口
            if (window.foodOrderManager) {
                window.foodOrderManager.currentOrderId = orderId;

                // 检查方法是否存在
                if (typeof window.foodOrderManager.showOrderStatusModal === 'function') {
                    window.foodOrderManager.showOrderStatusModal(result.order);
                } else {
                    // 手动显示模态窗口
                    showOrderStatusModalManually(result.order, orderId);
                }

                // 关闭我的订单模态窗口
                if (window.foodOrderManager.myOrdersModalInstance) {
                    window.foodOrderManager.myOrdersModalInstance.hide();
                }
            } else if (window.foodOrderModule) {
                window.foodOrderModule.data.currentOrderId = orderId;
                showOrderStatusModalManually(result.order, orderId);
            } else {
                // 直接手动显示
                showOrderStatusModalManually(result.order, orderId);
            }
        } else {
            alert('获取订单详情失败');
        }
    } catch (error) {
        console.error('打开订单状态失败:', error);
        alert('获取订单详情失败，请稍后重试');
    }
}

// 手动显示订单状态模态窗口
function showOrderStatusModalManually(order, orderId) {
    try {
        // 填充订单基本信息
        const orderNumberElement = document.getElementById('orderNumber');
        if (orderNumberElement) {
            orderNumberElement.textContent = order.order_id;
        }

        const submitTimeElement = document.getElementById('submitTime');
        if (submitTimeElement) {
            submitTimeElement.textContent = new Date().toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 填充订单详情
        populateOrderDetailsManually(order);

        // 更新订单进度
        updateOrderProgressManually(order);

        // 显示模态窗口
        const orderStatusModal = document.getElementById('orderStatusModal');
        if (orderStatusModal) {
            const modal = new bootstrap.Modal(orderStatusModal);
            modal.show();
        }

        // 设置全局订单ID以便确认收货使用
        if (window.foodOrderManager) {
            window.foodOrderManager.currentOrderId = orderId;
        } else if (window.foodOrderModule) {
            window.foodOrderModule.data.currentOrderId = orderId;
        }

        console.log('手动显示订单状态模态窗口成功');
    } catch (error) {
        console.error('手动显示订单状态模态窗口失败:', error);
        alert('显示订单详情失败');
    }
}

// 手动填充订单详情
function populateOrderDetailsManually(order) {
    const orderItemsList = document.getElementById('orderItemsList');
    const orderTotal = document.getElementById('orderTotal');

    if (orderItemsList) {
        // 清空现有内容
        orderItemsList.innerHTML = '';

        // 添加订单项目
        if (order.items && order.items.length > 0) {
            order.items.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item.name} x${item.quantity}</td>
                    <td class="text-end">¥${(item.price * item.quantity).toFixed(2)}</td>
                `;
                orderItemsList.appendChild(row);
            });
        }

        // 添加配送费
        const deliveryRow = document.createElement('tr');
        deliveryRow.innerHTML = `
            <td>配送费</td>
            <td class="text-end">¥${(order.delivery_fee || 5).toFixed(2)}</td>
        `;
        orderItemsList.appendChild(deliveryRow);
    }

    // 设置总价
    if (orderTotal) {
        orderTotal.textContent = `¥${(order.final_price || 0).toFixed(2)}`;
    }
}

// 手动更新订单进度
function updateOrderProgressManually(order) {
    const steps = ['step1', 'step2', 'step3', 'step4', 'step5'];

    // 重置所有步骤
    steps.forEach(stepId => {
        const step = document.getElementById(stepId);
        if (step) {
            step.classList.remove('active', 'completed');
        }
    });

    // 根据订单状态更新进度
    switch(order.status) {
        case 'pending_assignment':
            activateStepManually('step1');
            break;
        case 'assigned':
            completeStepManually('step1');
            activateStepManually('step2');
            updateDeliveryInfoManually(order);
            break;
        case 'delivering':
            completeStepManually('step1');
            completeStepManually('step2');
            activateStepManually('step3');
            updateDeliveryProgressManually();
            break;
        case 'delivered':
            completeStepManually('step1');
            completeStepManually('step2');
            completeStepManually('step3');
            activateStepManually('step4');
            showConfirmSectionManually();
            break;
        case 'completed':
            completeStepManually('step1');
            completeStepManually('step2');
            completeStepManually('step3');
            completeStepManually('step4');
            completeStepManually('step5');
            hideConfirmSectionManually();
            break;
    }
}

// 手动激活步骤
function activateStepManually(stepId) {
    const step = document.getElementById(stepId);
    if (step) {
        step.classList.add('active');
    }
}

// 手动完成步骤
function completeStepManually(stepId) {
    const step = document.getElementById(stepId);
    if (step) {
        step.classList.remove('active');
        step.classList.add('completed');
    }
}

// 手动更新配送信息
function updateDeliveryInfoManually(order) {
    const deliveryInfo = document.getElementById('deliveryInfo');
    const assignTime = document.getElementById('assignTime');

    if (deliveryInfo) {
        deliveryInfo.textContent = `配送员：${order.assigned_staff_name || '未分配'}，预计15-25分钟内到达`;
    }
    if (assignTime) {
        assignTime.textContent = new Date().toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }
}

// 手动更新配送进度
function updateDeliveryProgressManually() {
    const deliveryProgress = document.getElementById('deliveryProgress');
    const deliveryTime = document.getElementById('deliveryTime');

    if (deliveryProgress) {
        deliveryProgress.textContent = '配送员正在路上，预计10分钟内到达';
    }
    if (deliveryTime) {
        deliveryTime.textContent = new Date().toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }
}

// 手动显示确认收货按钮
function showConfirmSectionManually() {
    const confirmSection = document.getElementById('confirmSection');
    const arrivalTime = document.getElementById('arrivalTime');

    if (confirmSection) {
        confirmSection.style.display = 'block';
    }
    if (arrivalTime) {
        arrivalTime.textContent = new Date().toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }
}

// 手动隐藏确认收货按钮
function hideConfirmSectionManually() {
    const confirmSection = document.getElementById('confirmSection');
    const completeTime = document.getElementById('completeTime');

    if (confirmSection) {
        confirmSection.style.display = 'none';
    }
    if (completeTime) {
        completeTime.textContent = new Date().toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }
}

// 全局函数：刷新我的订单
function refreshMyOrders() {
    if (window.foodOrderManager && window.foodOrderManager.loadMyOrders) {
        window.foodOrderManager.loadMyOrders();
    }
}
</script>

<!-- 我的订单列表模态窗口 -->
<div class="modal fade" id="myOrdersModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h4 class="modal-title"><i class="bi bi-list-ul me-2"></i>我的订单</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <!-- 订单统计 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center bg-light">
                            <div class="card-body">
                                <h5 class="card-title text-primary" id="totalOrdersCount">0</h5>
                                <p class="card-text">总订单数</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center bg-warning text-white">
                            <div class="card-body">
                                <h5 class="card-title" id="pendingOrdersCount">0</h5>
                                <p class="card-text">待分配</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center bg-info text-white">
                            <div class="card-body">
                                <h5 class="card-title" id="processingOrdersCount">0</h5>
                                <p class="card-text">配送中</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center bg-success text-white">
                            <div class="card-body">
                                <h5 class="card-title" id="completedOrdersCount">0</h5>
                                <p class="card-text">已完成</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 订单列表 -->
                <div class="order-list">
                    <div id="orderListContainer">
                        <!-- 动态填充订单列表 -->
                    </div>

                    <!-- 空状态 -->
                    <div id="emptyOrderState" class="text-center py-5" style="display: none;">
                        <i class="bi bi-inbox fs-1 text-muted"></i>
                        <h5 class="mt-3 text-muted">暂无订单记录</h5>
                        <p class="text-muted">您还没有提交过任何订单</p>
                        <button class="btn btn-primary" data-bs-dismiss="modal">
                            <i class="bi bi-plus-circle me-2"></i>去点餐
                        </button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="refreshMyOrders()">
                    <i class="bi bi-arrow-clockwise me-2"></i>刷新
                </button>
            </div>
        </div>
    </div>
</div>