#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
添加老年人数据脚本
用于修复社区人员工作端地图显示问题
"""

import pymysql
from datetime import datetime

def add_elderly_data():
    """添加三位老年人的数据"""
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='pqr0713',
            database='elderly_care',
            charset='utf8mb4'
        )

        cursor = connection.cursor()

        # 首先删除已存在的数据
        print("正在清理已存在的数据...")
        cursor.execute("DELETE FROM smartwatch WHERE watch_id IN ('SW01', 'SW02', 'SW03')")
        cursor.execute("DELETE FROM elderlyuser WHERE user_id IN ('E01', 'E02', 'E03')")
        connection.commit()
        print("已清理旧数据")

        # 添加新的老年人数据
        elderly_data = [
            {
                'user_id': 'E01',
                'password': '123456',
                'name': '张建国',
                'age': 78,
                'phone': '13800138001',
                'address': '浙江省杭州市余杭区龙湖天街1号',
                'emergency_contact_name': '张小明',
                'emergency_contact_phone': '13900139001',
                'health_record_id': 'HR01',
                'smartwatch_id': 'SW01',
                'region': 'A',
                'gps_location': '30.2741,120.1551'  # 余杭区龙湖天街附近坐标
            },
            {
                'user_id': 'E02',
                'password': '123456',
                'name': '李淑兰',
                'age': 75,
                'phone': '13800138002',
                'address': '浙江省杭州市西湖区西溪路518号',
                'emergency_contact_name': '李小华',
                'emergency_contact_phone': '13900139002',
                'health_record_id': 'HR02',
                'smartwatch_id': 'SW02',
                'region': 'A',
                'gps_location': '30.2592,120.1136'  # 西湖区西溪路附近坐标
            },
            {
                'user_id': 'E03',
                'password': '123456',
                'name': '王福海',
                'age': 80,
                'phone': '13800138003',
                'address': '浙江省杭州市拱墅区湖墅南路88号',
                'emergency_contact_name': '王小丽',
                'emergency_contact_phone': '13900139003',
                'health_record_id': 'HR03',
                'smartwatch_id': 'SW03',
                'region': 'B',
                'gps_location': '30.3089,120.1553'  # 拱墅区湖墅南路附近坐标
            }
        ]

        # 插入老年人数据
        for data in elderly_data:
            gps_location = data.pop('gps_location')  # 取出GPS坐标，用于智能手环

            # 插入老年人用户
            insert_elderly_sql = """
                INSERT INTO elderlyuser
                (user_id, password, name, age, phone, address, emergency_contact_name,
                 emergency_contact_phone, health_record_id, smartwatch_id, region)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """

            cursor.execute(insert_elderly_sql, (
                data['user_id'], data['password'], data['name'], data['age'],
                data['phone'], data['address'], data['emergency_contact_name'],
                data['emergency_contact_phone'], data['health_record_id'],
                data['smartwatch_id'], data['region']
            ))

            # 插入智能手环数据
            insert_smartwatch_sql = """
                INSERT INTO smartwatch
                (watch_id, bound_user_id, last_sync_time, gps_location, heart_rate,
                 sleep_score, step_count, battery)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """

            cursor.execute(insert_smartwatch_sql, (
                data['smartwatch_id'], data['user_id'], datetime.now(),
                gps_location, 75, 80, 3000, 90
            ))

            print(f"已添加老年人: {data['name']} ({data['user_id']}) - 区域: {data['region']}")
            print(f"  地址: {data['address']}")
            print(f"  GPS坐标: {gps_location}")
            print(f"  智能手环: {data['smartwatch_id']}")
            print()

        # 提交所有更改
        connection.commit()
        print("✅ 所有老年人数据添加成功！")

        # 验证数据
        print("\n验证添加的数据:")
        for user_id in ['E01', 'E02', 'E03']:
            cursor.execute("""
                SELECT e.name, s.gps_location
                FROM elderlyuser e
                LEFT JOIN smartwatch s ON e.smartwatch_id = s.watch_id
                WHERE e.user_id = %s
            """, (user_id,))

            result = cursor.fetchone()
            if result and result[1]:
                print(f"✅ {result[0]} - GPS: {result[1]}")
            else:
                print(f"❌ {user_id} 数据不完整")

        cursor.close()
        connection.close()
        return True

    except Exception as e:
        print(f"❌ 添加数据时出错: {str(e)}")
        if 'connection' in locals():
            connection.rollback()
            connection.close()
        return False

if __name__ == '__main__':
    print("开始添加老年人数据...")
    success = add_elderly_data()
    if success:
        print("\n🎉 数据添加完成！现在可以测试地图显示功能了。")
    else:
        print("\n💥 数据添加失败，请检查错误信息。")
