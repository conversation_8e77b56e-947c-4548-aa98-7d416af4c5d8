{% extends "base.html" %}

{% block title %}注册 - 居家养老健康管理系统{% endblock %}

{% block head %}
<style>
    .register-container {
        max-width: 700px;
        margin: 50px auto;
    }
    
    .user-type-selector {
        margin-bottom: 20px;
    }
    
    .user-type-btn {
        width: 100%;
        text-align: left;
        padding: 15px;
        margin-bottom: 10px;
        border-radius: 8px;
        border: 2px solid #ddd;
        display: flex;
        align-items: center;
        transition: all 0.3s;
    }
    
    .user-type-btn.active {
        border-color: #3A84C3;
        background-color: rgba(0, 123, 255, 0.1);
    }
    
    .user-type-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    .user-icon {
        width: 40px;
        height: 40px;
        background-color: #e9ecef;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 1.2rem;
    }
    
    .elderly-icon { background-color: #d4edda; color: #28a745; }
    .family-icon { background-color: #cce5ff; color: #3A84C3; }
    .worker-icon { background-color: #f8d7da; color: #dc3545; }
    
    .form-step {
        display: none;
    }
    
    .form-step.active {
        display: block;
    }
</style>
{% endblock %}

{% block content %}
<div id="register-app" class="register-container">
    <div class="card shadow">
        <div class="card-header bg-primary text-white">
            <h3 class="mb-0">用户注册</h3>
        </div>
        <div class="card-body">
            <!-- 步骤指示器 -->
            <div class="progress mb-4" v-if="currentStep > 0 && source === 'index'">
                <div class="progress-bar" :style="{ width: progressWidth + '%' }"></div>
            </div>
            
            <!-- 步骤1: 选择用户类型 -->
            <div class="form-step" :class="{ 'active': currentStep === 0 }">
                <h4 class="mb-3">请选择您的用户身份</h4>
                
                <button @click="selectUserType('elderly')" class="user-type-btn btn" :class="{ 'active': selectedUserType === 'elderly' }">
                    <div class="user-icon elderly-icon">👴</div>
                    <div>
                        <h5 class="mb-1">老年人用户</h5>
                        <p class="mb-0 text-muted">管理健康数据、紧急呼叫、生活服务</p>
                    </div>
                </button>
                
                <button @click="selectUserType('family')" class="user-type-btn btn" :class="{ 'active': selectedUserType === 'family' }">
                    <div class="user-icon family-icon">👪</div>
                    <div>
                        <h5 class="mb-1">家属用户</h5>
                        <p class="mb-0 text-muted">监护老年人健康、接收通知</p>
                    </div>
                </button>
                
                <button @click="selectUserType('worker')" class="user-type-btn btn" :class="{ 'active': selectedUserType === 'worker' }">
                    <div class="user-icon worker-icon">👩‍⚕️</div>
                    <div>
                        <h5 class="mb-1">社区工作人员</h5>
                        <p class="mb-0 text-muted">管理服务、处理紧急情况</p>
                    </div>
                </button>
                
                <div class="d-flex justify-content-end mt-4">
                    <button class="btn btn-primary" @click="nextStep" :disabled="!selectedUserType">下一步</button>
                </div>
            </div>
            
            <!-- 步骤2: 基本信息 -->
            <div class="form-step" :class="{ 'active': currentStep === 1 }">
                <div class="d-flex align-items-center mb-4">
                    <div class="user-icon" :class="iconClass">
                        <span v-if="selectedUserType === 'elderly'">👴</span>
                        <span v-else-if="selectedUserType === 'family'">👪</span>
                        <span v-else-if="selectedUserType === 'worker'">👩‍⚕️</span>
                    </div>
                    <h4 class="mb-0 ms-2">[[ userTypeText ]]注册 - 基本信息</h4>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="username" class="form-label">姓名</label>
                        <input type="text" class="form-control" id="username" v-model="formData.name" required>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="phone" class="form-label">手机号码</label>
                        <input type="tel" class="form-control" id="phone" v-model="formData.phone" required>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="password" class="form-label">密码</label>
                        <input type="password" class="form-control" id="password" v-model="formData.password" required>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="confirmPassword" class="form-label">确认密码</label>
                        <input type="password" class="form-control" id="confirmPassword" v-model="formData.confirmPassword" required>
                    </div>
                </div>
                
                <!-- 老年人特定字段 -->
                <div v-if="selectedUserType === 'elderly'">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="age" class="form-label">年龄</label>
                            <input type="number" class="form-control" id="age" v-model="formData.age" min="60" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="address" class="form-label">居住地址</label>
                            <input type="text" class="form-control" id="address" v-model="formData.address" required>
                        </div>
                    </div>
                </div>
                
                <!-- 家属特定字段 -->
                <div v-if="selectedUserType === 'family'">
                    <div class="mb-3">
                        <label for="relationship" class="form-label">与老人关系</label>
                        <select class="form-select" id="relationship" v-model="formData.relationship" required>
                            <option value="儿子">儿子</option>
                            <option value="女儿">女儿</option>
                            <option value="配偶">配偶</option>
                            <option value="兄弟姐妹">兄弟姐妹</option>
                            <option value="其他亲属">其他亲属</option>
                        </select>
                    </div>
                </div>
                
                <!-- 工作人员特定字段 -->
                <div v-if="selectedUserType === 'worker'">
                    <div class="mb-3">
                        <label for="region" class="form-label">负责区域</label>
                        <select class="form-select" id="region" v-model="formData.region" required>
                            <option value="A">A区</option>
                            <option value="B">B区</option>
                            <option value="C">C区</option>
                            <option value="D">D区</option>
                        </select>
                    </div>
                </div>
                
                <div class="d-flex justify-content-between mt-4">
                    <button class="btn btn-outline-secondary" @click="prevStep">上一步</button>
                    <button class="btn btn-primary" @click="nextStep" :disabled="!isStepTwoValid">下一步</button>
                </div>
            </div>
            
            <!-- 步骤3: 附加信息 -->
            <div class="form-step" :class="{ 'active': currentStep === 2 }">
                <div class="d-flex align-items-center mb-4">
                    <div class="user-icon" :class="iconClass">
                        <span v-if="selectedUserType === 'elderly'">👴</span>
                        <span v-else-if="selectedUserType === 'family'">👪</span>
                        <span v-else-if="selectedUserType === 'worker'">👩‍⚕️</span>
                    </div>
                    <h4 class="mb-0 ms-2">[[ userTypeText ]]注册 - 附加信息</h4>
                </div>
                
                <!-- 老年人特定字段 -->
                <div v-if="selectedUserType === 'elderly'">
                    <div class="mb-3">
                        <label for="emergencyContact" class="form-label">紧急联系人姓名</label>
                        <input type="text" class="form-control" id="emergencyContact" v-model="formData.emergencyContactName">
                    </div>
                    
                    <div class="mb-3">
                        <label for="emergencyPhone" class="form-label">紧急联系人电话</label>
                        <input type="tel" class="form-control" id="emergencyPhone" v-model="formData.emergencyContactPhone">
                    </div>
                    
                    <div class="mb-3">
                        <label for="medicalHistory" class="form-label">医疗历史</label>
                        <textarea class="form-control" id="medicalHistory" v-model="formData.medicalHistory" rows="3"></textarea>
                    </div>
                </div>
                
                <!-- 家属特定字段 -->
                <div v-if="selectedUserType === 'family'">
                    <div class="mb-3">
                        <label for="elderlyId" class="form-label">关联老人ID</label>
                        <input type="text" class="form-control" id="elderlyId" v-model="formData.boundElderlyId" placeholder="如有多个老人ID，请用逗号分隔，例如：E001,E002">
                        <div class="form-text text-muted">可以输入多个老人ID，用逗号分隔</div>
                    </div>
                </div>
                
                <!-- 工作人员特定字段 -->
                <div v-if="selectedUserType === 'worker'">
                    <div class="mb-3">
                        <label for="workPosition" class="form-label">工作职位</label>
                        <select class="form-select" id="workPosition" v-model="formData.position">
                            <option value="社区医生">社区医生</option>
                            <option value="社区护士">社区护士</option>
                            <option value="社区服务人员">社区服务人员</option>
                            <option value="管理人员">管理人员</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="agreeTerms" v-model="formData.agreeTerms" required>
                    <label class="form-check-label" for="agreeTerms">
                        我已阅读并同意<a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">《服务条款》</a>和<a href="#" data-bs-toggle="modal" data-bs-target="#privacyModal">《隐私政策》</a>
                    </label>
                </div>
                
                <div v-if="errorMessage" class="alert alert-danger">
                    [[ errorMessage ]]
                </div>
                
                <div class="d-flex justify-content-between mt-4">
                    <button class="btn btn-outline-secondary" @click="prevStep">上一步</button>
                    <button class="btn btn-success" @click="register" :disabled="!formData.agreeTerms || isLoading">
                        <span v-if="!isLoading">完成注册</span>
                        <span v-else>注册中...</span>
                    </button>
                </div>
            </div>
            
            <!-- 步骤4: 注册成功 -->
            <div class="form-step" :class="{ 'active': currentStep === 3 }">
                <div class="text-center py-4">
                    <div class="mb-4">
                        <span class="display-1 text-success">✓</span>
                    </div>
                    <h3 class="mb-3">注册成功！</h3>
                    <p class="mb-4">您已成功注册为[[ userTypeText ]]用户，您的用户ID是：<strong>[[ generatedId ]]</strong></p>
                    <p class="text-muted mb-4">请记住您的用户ID和密码，用于登录系统。</p>
                    
                    <div class="d-grid gap-2 col-6 mx-auto">
                        <a href="{{ url_for('main.login') }}" class="btn btn-primary">前往登录</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 服务条款模态框 -->
<div class="modal fade" id="termsModal" tabindex="-1" aria-labelledby="termsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="termsModalLabel">服务条款</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h5>居家养老健康管理系统服务条款</h5>
                <p>本服务条款是您与居家养老健康管理系统之间就使用本系统服务所订立的协议。</p>
                <p>...</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 隐私政策模态框 -->
<div class="modal fade" id="privacyModal" tabindex="-1" aria-labelledby="privacyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="privacyModalLabel">隐私政策</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h5>居家养老健康管理系统隐私政策</h5>
                <p>本隐私政策描述了我们如何收集、使用和保护您的个人信息。</p>
                <p>...</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    new Vue({
    created() {
        const urlParams = new URLSearchParams(window.location.search);
        this.selectedUserType = urlParams.get('user_type');
        this.source = urlParams.get('source') || 'index';

        if (this.selectedUserType && ['elderly', 'family', 'worker'].includes(this.selectedUserType)) {
            this.currentStep = this.source === 'login' ? 1 : 0;
        }
    },
        el: '#register-app',
        delimiters: ['[[', ']]'],
        data: {
            currentStep: 0,
            selectedUserType: '',
            generatedId: '',
            isLoading: false,
            errorMessage: '',
            formData: {
                name: '',
                phone: '',
                password: '',
                confirmPassword: '',
                age: '',
                address: '',
                relationship: '儿子',
                region: 'A',
                emergencyContactName: '',
                emergencyContactPhone: '',
                medicalHistory: '',
                boundElderlyId: '',
                position: '社区服务人员',
                agreeTerms: false
            }
        },
        computed: {
            progressWidth() {
                return (this.currentStep / 3) * 100;
            },
            userTypeText() {
                switch(this.selectedUserType) {
                    case 'elderly': return '老年人';
                    case 'family': return '家属';
                    case 'worker': return '社区工作人员';
                    default: return '';
                }
            },
            iconClass() {
                switch(this.selectedUserType) {
                    case 'elderly': return 'elderly-icon';
                    case 'family': return 'family-icon';
                    case 'worker': return 'worker-icon';
                    default: return '';
                }
            },
            isStepTwoValid() {
                // 检查基本字段是否填写
                if (!this.formData.name || !this.formData.phone || !this.formData.password || !this.formData.confirmPassword) {
                    return false;
                }
                
                // 检查密码是否一致
                if (this.formData.password !== this.formData.confirmPassword) {
                    return false;
                }
                
                // 针对不同用户类型的特殊字段验证
                if (this.selectedUserType === 'elderly' && (!this.formData.age || !this.formData.address)) {
                    return false;
                }
                
                return true;
            }
        },
        methods: {
            selectUserType(type) {
                this.selectedUserType = type;
                this.errorMessage = '';
            },
            nextStep() {
                if (this.currentStep < 3) {
                    this.currentStep++;
                }
            },
            prevStep() {
                if (this.currentStep > 0) {
                    this.currentStep--;
                }
            },
            register() {
                this.isLoading = true;
                this.errorMessage = '';
                
                // 准备注册数据
                const registerData = {
                    user_type: this.selectedUserType,
                    name: this.formData.name,
                    phone: this.formData.phone,
                    password: this.formData.password
                };
                
                // 根据用户类型添加特定字段
                if (this.selectedUserType === 'elderly') {
                    registerData.age = this.formData.age;
                    registerData.address = this.formData.address;
                    registerData.emergencyContactName = this.formData.emergencyContactName;
                    registerData.emergencyContactPhone = this.formData.emergencyContactPhone;
                    registerData.medicalHistory = this.formData.medicalHistory;
                } else if (this.selectedUserType === 'family') {
                    registerData.relationship = this.formData.relationship;
                    registerData.boundElderlyId = this.formData.boundElderlyId;
                } else if (this.selectedUserType === 'worker') {
                    registerData.region = this.formData.region;
                    registerData.position = this.formData.position;
                }
                
                // 发送注册请求到后端API
                fetch('/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(registerData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 注册成功
                        this.generatedId = data.user_id;
                        this.nextStep();
                    } else {
                        // 注册失败
                        this.errorMessage = data.error || '注册失败，请重试';
                    }
                })
                .catch(error => {
                    this.errorMessage = '服务器错误，请稍后重试';
                    console.error('注册错误:', error);
                })
                .finally(() => {
                    this.isLoading = false;
                });
            }
        }
    });
</script>
{% endblock %}