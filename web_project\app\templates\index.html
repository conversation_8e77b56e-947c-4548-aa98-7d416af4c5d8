{% extends "base.html" %}

{% block title %}首页 - 居家养老健康管理系统{% endblock %}

{% block head %}
<style>
    /* 全局样式 */
    body {
        font-family: "Microsoft YaHei", "Segoe UI", sans-serif;
        color: #333;
        line-height: 1.6;
    }
    
    h1, h2, h3, h4, h5 {
        font-family: "Source Han Sans CN", "Microsoft YaHei", sans-serif;
    }
    
    /* 顶栏样式 */
    .top-bar {
        height: 60px;
        background-color: #5B8FF9;
        color: white;
    }
    
    .service-tags {
        animation: scrollTags 15s linear infinite;
        white-space: nowrap;
    }
    
    @keyframes scrollTags {
        0% { transform: translateX(0); }
        100% { transform: translateX(-100%); }
    }
    
    
    
    /* 主横幅区域 */
    .main-banner {
        position: relative;
        height: 650px;
        background-size: cover;
        background-position: center;
        margin-top: 0;
        overflow: hidden;
        background-image: url("{{ url_for('static', filename='images/index.png') }}");
    }
    
    .banner-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, rgba(22, 75, 135, 0.85) 0%, rgba(22, 75, 135, 0.4) 100%);
        display: flex;
        align-items: center;
    }
    
    .banner-content {
        position: relative;
        z-index: 2;
        padding: 80px 0 50px 50px;
        max-width: 600px;
        color: white;
    }
    
    .banner-title {
        font-size: 3rem;
        font-weight: bold;
        margin-bottom: 20px;
    }
    
    .banner-subtitle {
        font-size: 1.2rem;
        margin-bottom: 30px;
        font-weight: 300;
    }
    
    /* 登录入口区块 */
    .login-section {
        margin-top: -80px;
        position: relative;
        z-index: 10;
        padding: 0 10%;
    }
    
    .login-card {
        border-radius: 5px;
        color: white;
        padding: 30px;
        height: 100%;
        transition: all 0.3s;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        position: relative;
    }
    
    .login-card-1 {
        background-color: #2687be;
    }
    
    .login-card-2 {
        background-color: #1c6290;
    }
    
    .login-card-3 {
        background-color: #174277;
    }
    
    .login-card:before {
        content: "";
        position: absolute;
        bottom: -8px;
        left: 15px;
        width: 85%;
        height: 20px;
        background-color: rgba(255, 255, 255, 0.2);
        z-index: -1;
        border-radius: 5px;
    }
    
    .login-card:after {
        content: "";
        position: absolute;
        bottom: -16px;
        left: 30px;
        width: 70%;
        height: 20px;
        background-color: rgba(255, 255, 255, 0.1);
        z-index: -2;
        border-radius: 5px;
    }
    
    .login-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .login-card h4 {
        font-size: 1.5rem;
        margin-bottom: 15px;
        font-weight: 600;
    }
    
    .login-card p {
        margin-bottom: 20px;
        opacity: 0.9;
    }
    
    .login-card .btn {
        background-color: white;
        color: #164B87;
        border: none;
        padding: 10px 20px;
        font-weight: 500;
        border-radius: 4px;
    }
    
    /* 特色功能区域 */
    .features-section {
        padding: 100px 0 80px;
    }
    
    .section-title {
        text-align: center;
        margin-bottom: 60px;
    }
    
    .section-title h2 {
        font-size: 2.5rem;
        font-weight: bold;
        color: #164B87;
        margin-bottom: 10px;
    }
    
    .section-title p {
        color: #6c757d;
        font-size: 1.1rem;
    }
    
    .feature-item {
        text-align: center;
        margin-bottom: 40px;
        padding: 0 20px;
    }
    
    .feature-icon {
        width: 160px;
        height: 160px;
        margin: 0 auto 25px;
        border-radius: 50%;
        overflow: hidden;
        border: 5px solid #E8F1FA;
    }
    
    .feature-icon img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .feature-title {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 15px;
        color: #164B87;
    }
    
    .feature-desc {
        color: #6c757d;
        line-height: 1.7;
    }
    
    /* 底部信息栏样式 */
    .footer {
        background-color: #2c3e50;
        color: #ecf0f1;
        padding: 30px 0;
    }
    
    .footer h5 {
        color: #6DD400;
        border-bottom: 2px solid #6DD400;
        padding-bottom: 10px;
        margin-bottom: 15px;
    }
    
    .footer ul {
        list-style: none;
        padding-left: 0;
    }
    
    .footer ul li {
        margin-bottom: 10px;
    }
    
    .cert-icon {
        width: 40px;
        height: 40px;
        background-color: #ecf0f1;
        border-radius: 5px;
        margin-right: 10px;
    }
</style>
{% endblock %}

{% block content %}



<!-- 主横幅区域 -->
<div class="main-banner">
    <div class="banner-overlay">
        <div class="container">
            <div class="banner-content">
                <h1 class="banner-title">银发关爱，智慧守护</h1>
                <p class="banner-subtitle">专业居家养老健康管理平台，让长者生活更安心、更便捷</p>
            </div>
        </div>
    </div>
</div>

<!-- 登录入口区域 -->
<div class="login-section">
    <div class="container">
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="login-card login-card-1">
                    <h4>老年人入口</h4>
                    <p>健康管理、生活服务、智能守护，让晚年生活更安心。</p>
                    <a href="{{ url_for('main.login', user_type='elderly') }}" class="btn">立即登录 <i class="bi bi-arrow-right ms-2"></i></a>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="login-card login-card-2">
                    <h4>家属入口</h4>
                    <p>实时监控、健康预警、远程关怀，让关爱不再受距离限制。</p>
                    <a href="{{ url_for('main.login', user_type='family') }}" class="btn">立即登录 <i class="bi bi-arrow-right ms-2"></i></a>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="login-card login-card-3">
                    <h4>社区工作者入口</h4>
                    <p>数据分析、服务管理、应急响应，提升社区养老服务效率。</p>
                    <a href="{{ url_for('main.login', user_type='worker') }}" class="btn">立即登录 <i class="bi bi-arrow-right ms-2"></i></a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 特色功能区域 -->
<section class="features-section">
    <div class="container">
        <div class="section-title">
            <h2>特色功能</h2>
            <p>全方位守护长者健康，智慧赋能居家养老</p>
        </div>
        
        <div class="row">
            <div class="col-md-3">
                <div class="feature-item">
                    <div class="feature-icon">
                        <img src="../static/images/health.png" alt="健康管理">
                    </div>
                    <h3 class="feature-title">健康管理</h3>
                    <p class="feature-desc">实时监测健康数据，智能分析健康状况，提供个性化健康建议。</p>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="feature-item">
                    <div class="feature-icon">
                        <img src="../static/images/dish.png" alt="智能订餐">
                    </div>
                    <h3 class="feature-title">智能订餐</h3>
                    <p class="feature-desc">根据健康状况和口味偏好，提供个性化营养配餐，保障老年人饮食健康。</p>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="feature-item">
                    <div class="feature-icon">
                        <img src="../static/images/safe.png" alt="安全守护">
                    </div>
                    <h3 class="feature-title">安全守护</h3>
                    <p class="feature-desc">异常活动监测、一键紧急呼叫，及时响应突发情况，确保老年人安全。</p>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="feature-item">
                    <div class="feature-icon">
                        <img src="../static/images/yuancheng.png" alt="远程关怀">
                    </div>
                    <h3 class="feature-title">远程关怀</h3>
                    <p class="feature-desc">家属可远程查看健康数据和生活状态，让亲情不再受距离限制。</p>
                </div>
            </div>
        </div>
    </div>
</section>


{% endblock %}

{% block scripts %}
<script>
    // 在这里添加任何需要的JavaScript
</script>
{% endblock %} 