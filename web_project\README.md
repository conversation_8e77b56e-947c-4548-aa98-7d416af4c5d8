# 居家养老健康管理系统

本项目是一个基于Web的居家养老健康管理系统，旨在为老年人提供健康管理服务，帮助家属进行远程监护，并协助社区工作人员提供高效的养老服务。

## 主要功能

本系统包含以下核心功能模块：

1. **用户管理**
   - 支持老年人、家属和社区工作人员三类用户注册登录
   - 用户角色管理和权限控制
   - 个人信息维护与设置

2. **健康档案管理**
   - 老人健康基础信息记录（血压、血糖、病史等）
   - 健康档案查询和历史记录追踪
   - 健康趋势分析和风险评估报告

3. **健康监测**
   - 智能设备数据实时获取（心率、步数、睡眠等）
   - 健康数据可视化展示
   - 异常健康指标预警

4. **健康管理**
   - 运动数据记录与分析
   - 睡眠质量监测与评估
   - 饮食习惯记录与营养建议

5. **订餐服务**
   - 老人在线订餐和评价
   - 家属远程代订服务
   - 社区工作人员送餐管理

6. **紧急呼叫与定位**
   - 一键紧急求助功能
   - GPS定位和位置追踪
   - 紧急联系人通知和救助协调

7. **家属监护**
   - 老人健康状态远程监控
   - 活动轨迹查看
   - 紧急情况实时通知

8. **健康科普**
   - 健康知识推送
   - 适老化阅读界面
   - 文章收藏与分享

## 技术栈

- **前端**
  - HTML5/CSS3：页面结构和样式
  - JavaScript(ES6+)：交互功能
  - Bootstrap 5：响应式布局框架
  - Vue.js：前端MVVM框架
  - ECharts：数据可视化图表库
  - 高德/百度地图API：地理位置服务

- **后端**
  - Python 3.9+：编程语言
  - Flask：Web框架
  - SQLAlchemy：ORM数据库框架
  - Flask-Login：用户认证
  - Flask-WTF：表单验证
  - Flask-Migrate：数据库迁移

- **数据库**
  - MySQL：生产环境数据库
  - SQLite：开发和测试环境

- **部署**
  - Gunicorn：WSGI HTTP服务器
  - Nginx：反向代理和静态资源服务
  - Docker：容器化部署（可选）

## 项目结构

```
web_project/
├── app/                        # 应用主目录
│   ├── __init__.py             # 应用初始化
│   ├── api/                    # API接口模块
│   │   ├── __init__.py
│   │   ├── auth.py             # 认证相关API
│   │   ├── elderly.py          # 老人端API
│   │   ├── family.py           # 家属端API
│   │   ├── emergency.py        # 紧急呼叫API
│   │   └── admin.py            # 管理员API
│   ├── models/                 # 数据模型
│   │   ├── __init__.py
│   │   └── models.py           # 数据库模型定义
│   ├── services/               # 业务逻辑服务
│   │   ├── __init__.py
│   │   ├── health_service.py   # 健康数据处理服务
│   │   ├── meal_service.py     # 订餐服务
│   │   └── location_service.py # 位置服务
│   ├── static/                 # 静态资源
│   │   ├── css/                # 样式文件
│   │   │   ├── bootstrap.min.css
│   │   │   ├── main.css        # 主样式表
│   │   │   └── elderly.css     # 适老化样式
│   │   ├── js/                 # JavaScript文件
│   │   │   ├── vue.min.js
│   │   │   ├── echarts.min.js
│   │   │   ├── map.js          # 地图功能
│   │   │   └── health.js       # 健康监测功能
│   │   └── images/             # 图片资源
│   ├── templates/              # HTML模板
│   │   ├── base.html           # 基础模板
│   │   ├── auth/               # 认证相关模板
│   │   │   ├── login.html
│   │   │   └── register.html
│   │   ├── elderly/            # 老人端模板
│   │   │   ├── center.html     # 老人中心
│   │   │   ├── health.html     # 健康数据
│   │   │   ├── emergency.html  # 紧急呼叫
│   │   │   └── meal.html       # 订餐服务
│   │   ├── family/             # 家属端模板
│   │   │   ├── center.html     # 家属中心
│   │   │   ├── monitoring.html # 监护页面
│   │   │   └── location.html   # 位置跟踪
│   │   └── admin/              # 管理员模板
│   │       ├── dashboard.html  # 管理控制台
│   │       └── orders.html     # 订单管理
│   ├── utils/                  # 工具函数
│   │   ├── __init__.py
│   │   ├── helpers.py          # 通用辅助函数
│   │   └── validators.py       # 数据验证函数
│   ├── routes.py               # 主路由定义
│   └── error_handlers.py       # 错误处理
├── config.py                   # 配置文件
├── migrations/                 # 数据库迁移文件
├── instance/                   # 实例配置和数据库
├── logs/                       # 日志文件
├── tests/                      # 测试代码
│   ├── __init__.py
│   ├── test_api.py             # API测试
│   └── test_models.py          # 模型测试
├── app.py                      # 应用启动文件
├── requirements.txt            # 依赖包列表
├── .env.example                # 环境变量示例
├── .gitignore                  # Git忽略文件配置
└── README.md                   # 项目说明文档
```

## 模块功能说明

### 核心模块

#### 1. app/__init__.py
- 初始化Flask应用
- 注册蓝图和扩展
- 配置数据库连接

#### 2. app/models/models.py
- 定义数据库模型（用户、健康记录、订单等）
- 设置表关系和约束

#### 3. app/api/
- 提供各类功能的后端API接口
- 处理前端请求并返回响应
- 权限控制和数据验证

#### 4. app/services/
- 实现业务逻辑
- 数据处理和转换
- 与外部服务集成

#### 5. app/templates/
- 前端页面模板
- 适老化界面设计
- 响应式布局

## 安装与运行

### 前提条件

- Python 3.9+
- MySQL (生产环境)
- pip (Python包管理器)

### 安装步骤

1. **克隆项目**

```bash
git clone <项目仓库URL>
cd web_project
```

2. **创建虚拟环境**

```bash
# Windows
python -m venv venv
venv\Scripts\activate

# Linux/Mac
python -m venv venv
source venv/bin/activate
```

3. **安装依赖**

```bash
pip install -r requirements.txt
```

4. **配置环境变量**

```bash
# 复制环境变量示例文件并修改
cp .env.example .env
# 编辑.env文件，设置必要的环境变量
```

5. **初始化数据库**

```bash
flask db init     # 初始化迁移
flask db migrate  # 创建迁移脚本
flask db upgrade  # 应用迁移
```

6. **运行应用**

```bash
# 开发环境
flask run --debug

# 或者
python app.py
```

应用将在 http://127.0.0.1:5000/ 运行。

## 接口说明

项目提供了完整的RESTful API接口，详细接口设计请参考 [接口设计.txt](接口设计.txt) 文档。

## 注意事项

- 本项目涉及个人隐私和健康数据，请确保符合相关法律法规
- 老人端界面采用适老化设计，字体较大，操作简单
- 紧急呼叫功能需要浏览器支持地理位置服务
- 首次使用需要进行设备绑定和家属关联

## 贡献者

- [项目团队成员列表]

## 许可证

[许可证信息] 