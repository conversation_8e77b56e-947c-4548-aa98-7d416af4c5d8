from app import db
from datetime import datetime
from datetime import datetime

class ElderlyUser(db.Model):
    __tablename__ = 'elderlyuser'

    user_id = db.Column(db.String(10), primary_key=True)
    password = db.Column(db.String(50), nullable=False)
    name = db.Column(db.String(50), nullable=False)
    age = db.Column(db.Integer)
    phone = db.Column(db.String(20))
    address = db.Column(db.String(100))
    emergency_contact_name = db.Column(db.String(50))
    emergency_contact_phone = db.Column(db.String(20))
    health_record_id = db.Column(db.String(10))
    smartwatch_id = db.Column(db.String(10))
    bound_family_ids = db.Column(db.String(100))  # 存储逗号分隔的家属ID列表

    # 关联
    smartwatch = db.relationship('Smartwatch', backref='elderly_user', uselist=False)
    emergency_calls = db.relationship('EmergencyCall', backref='elderly_user')

    def __repr__(self):
        return f'<ElderlyUser {self.name}>'

class FamilyUser(db.Model):
    __tablename__ = 'familyuser'

    family_id = db.Column(db.String(10), primary_key=True)
    password = db.Column(db.String(50), nullable=False)
    name = db.Column(db.String(50), nullable=False)
    relationship = db.Column(db.String(20))
    phone = db.Column(db.String(20))
    bound_elderly_id = db.Column(db.String(10), db.ForeignKey('elderlyuser.user_id'))

    def __repr__(self):
        return f'<FamilyUser {self.name}>'

class CommunityWorker(db.Model):
    __tablename__ = 'communityworker'

    worker_id = db.Column(db.String(10), primary_key=True)
    password = db.Column(db.String(50), nullable=False)
    name = db.Column(db.String(50), nullable=False)
    region = db.Column(db.String(10))
    phone = db.Column(db.String(20))

    def __repr__(self):
        return f'<CommunityWorker {self.name}>'

class Smartwatch(db.Model):
    __tablename__ = 'smartwatch'

    watch_id = db.Column(db.String(10), primary_key=True)
    bound_user_id = db.Column(db.String(10), db.ForeignKey('elderlyuser.user_id'))
    last_sync_time = db.Column(db.DateTime, default=datetime.utcnow)
    gps_location = db.Column(db.String(50))  # 格式: 纬度,经度
    heart_rate = db.Column(db.Integer)
    sleep_score = db.Column(db.Integer)
    step_count = db.Column(db.Integer)
    battery = db.Column(db.Integer)

    def __repr__(self):
        return f'<Smartwatch {self.watch_id}>'

class HealthRecord(db.Model):
    __tablename__ = 'healthrecord'

    record_id = db.Column(db.String(10), primary_key=True)
    blood_pressure = db.Column(db.String(20))
    blood_sugar = db.Column(db.String(20))
    medication_record = db.Column(db.String(1000))
    medical_history = db.Column(db.String(1000))
    update_time = db.Column(db.Date)
    health_threshold = db.Column(db.String(1000))

    def __repr__(self):
        return f'<HealthRecord {self.record_id}>'

class EmergencyCall(db.Model):
    __tablename__ = 'emergencycall'

    call_id = db.Column(db.String(20), primary_key=True)
    elderly_id = db.Column(db.String(10), db.ForeignKey('elderlyuser.user_id'))
    call_time = db.Column(db.DateTime, default=datetime.utcnow)
    gps_location = db.Column(db.String(50))  # 格式: 纬度,经度
    status = db.Column(db.String(20), default='待处理')  # 待处理/已响应/已解决
    related_person_id = db.Column(db.String(10))  # 可能是家属ID或社区工作人员ID

    def __repr__(self):
        return f'<EmergencyCall {self.call_id}>'

class HealthArticle(db.Model):
    __tablename__ = 'healtharticle'

    article_id = db.Column(db.String(10), primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    content = db.Column(db.Text, nullable=False)
    category = db.Column(db.String(20), nullable=False)
    publish_time = db.Column(db.Date, nullable=False)
    favorited_by_users = db.Column(db.String(200))
    image_url = db.Column(db.String(200))
    read_count = db.Column(db.Integer, default=0)
    difficulty_level = db.Column(db.Integer, default=1)

    def __repr__(self):
        return f'<HealthArticle {self.article_id}: {self.title}>'

    def to_dict(self):
        return {
            'article_id': self.article_id,
            'title': self.title,
            'content': self.content,
            'category': self.category,
            'publish_time': self.publish_time.strftime('%Y-%m-%d') if self.publish_time else None,
            'favorited_by_users': self.favorited_by_users.split(',') if self.favorited_by_users else [],
            'image_url': self.image_url,
            'read_count': self.read_count,
            'difficulty_level': self.difficulty_level
        }

class EmergencyEvent(db.Model):
    __tablename__ = 'emergencyevent'

    event_id = db.Column(db.String(30), primary_key=True)
    elderly_id = db.Column(db.String(10), db.ForeignKey('elderlyuser.user_id'), nullable=False)
    emergency_type = db.Column(db.String(20), nullable=False)  # medical, fall, help
    location = db.Column(db.String(50))  # GPS坐标 "lat,lng"
    address = db.Column(db.String(200))  # 地址描述
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    status = db.Column(db.String(20), default='待处理')  # 待处理/部分响应/已处理

    # 家属响应相关字段
    family_responded = db.Column(db.Boolean, default=False)
    family_responder_id = db.Column(db.String(10))
    family_response_time = db.Column(db.DateTime)
    family_response_note = db.Column(db.String(500))

    # 工作人员响应相关字段
    worker_responded = db.Column(db.Boolean, default=False)
    worker_responder_id = db.Column(db.String(10))
    worker_response_time = db.Column(db.DateTime)
    worker_response_note = db.Column(db.String(500))

    def __repr__(self):
        return f'<EmergencyEvent {self.event_id}>'