from flask import Blueprint, request, jsonify
from app.api.food_orders import orders_storage

# 创建蓝图
worker_bp = Blueprint('worker', __name__)

@worker_bp.route('/meal_orders', methods=['GET'])
def get_meal_orders():
    """获取订餐订单列表（供工作人员使用）"""
    try:
        # 获取查询参数
        status = request.args.get('status', None)
        region = request.args.get('region', None)
        
        # 过滤订单
        filtered_orders = orders_storage
        if status:
            filtered_orders = [o for o in orders_storage if o['status'] == status]
        
        # 转换为工作人员界面需要的格式
        meal_services = []
        for order in filtered_orders:
            # 确定状态显示
            if order['status'] == 'pending_assignment':
                display_status = 'pending'
                status_text = '待分配'
            elif order['status'] == 'assigned':
                display_status = 'assigned'
                status_text = '已分配'
            elif order['status'] == 'delivered':
                display_status = 'delivered'
                status_text = '已送达'
            else:
                display_status = 'pending'
                status_text = '待处理'
            
            service = {
                'id': order['order_id'],
                'elderly_id': order['user_id'],
                'name': order['user_name'],
                'status': display_status,
                'status_text': status_text,
                'staff': order['assigned_staff_name'],
                'address': order['delivery_address'],
                'time': order['delivery_time'],
                'phone': order['user_phone'],
                'total_price': order['final_price'],
                'payment_method': order['payment_method'],
                'special_notes': order['special_notes'],
                'items': order['items'],
                'created_at': order['created_at'],
                'emergency_contact': order['emergency_contact'],
                'emergency_phone': order['emergency_phone'],
                'order_details': {
                    'order_id': order['order_id'],
                    'items_detail': order['items'],
                    'subtotal': order['total_price'],
                    'delivery_fee': order['delivery_fee'],
                    'final_total': order['final_price']
                }
            }
            meal_services.append(service)
        
        # 按创建时间倒序排列，确保新订单在前面
        meal_services.sort(key=lambda x: x['created_at'], reverse=True)
        
        return jsonify({
            'success': True,
            'meal_services': meal_services,
            'total_count': len(meal_services),
            'pending_count': len([s for s in meal_services if s['status'] == 'pending']),
            'assigned_count': len([s for s in meal_services if s['status'] == 'assigned']),
            'delivered_count': len([s for s in meal_services if s['status'] == 'delivered'])
        }), 200
        
    except Exception as e:
        print(f"获取订餐订单列表失败: {str(e)}")
        return jsonify({'error': f'获取订餐订单列表失败: {str(e)}'}), 500

@worker_bp.route('/assign_meal_order', methods=['POST'])
def assign_meal_order():
    """为订餐订单分配配送人员"""
    try:
        data = request.get_json()
        
        # 验证必要字段
        if 'order_id' not in data or 'assigned_staff' not in data:
            return jsonify({'error': '缺少必要字段: order_id 或 assigned_staff'}), 400
        
        order_id = data['order_id']
        assigned_staff = data['assigned_staff']
        
        # 查找订单
        order = None
        for o in orders_storage:
            if o['order_id'] == order_id:
                order = o
                break
        
        if not order:
            return jsonify({'error': '订单不存在'}), 404
        
        # 获取员工姓名
        staff_names = {
            '1': '王小明',
            '2': '李玲', 
            '3': '赵强'
        }
        staff_name = staff_names.get(assigned_staff, f'员工{assigned_staff}')
        
        # 更新订单状态
        order['status'] = 'assigned'
        order['assigned_staff'] = assigned_staff
        order['assigned_staff_name'] = staff_name
        from datetime import datetime
        order['updated_at'] = datetime.now().isoformat()
        
        print(f"订餐订单 {order_id} 已分配给 {staff_name}")
        
        return jsonify({
            'success': True,
            'message': f'订单已分配给 {staff_name}',
            'order_id': order_id,
            'staff_name': staff_name
        }), 200
        
    except Exception as e:
        print(f"订餐订单分配失败: {str(e)}")
        return jsonify({'error': f'订餐订单分配失败: {str(e)}'}), 500

@worker_bp.route('/update_meal_order_status', methods=['POST'])
def update_meal_order_status():
    """更新订餐订单状态"""
    try:
        data = request.get_json()
        
        if 'order_id' not in data or 'status' not in data:
            return jsonify({'error': '缺少必要字段: order_id 或 status'}), 400
        
        order_id = data['order_id']
        new_status = data['status']
        
        # 查找并更新订单
        order = None
        for o in orders_storage:
            if o['order_id'] == order_id:
                order = o
                break
        
        if not order:
            return jsonify({'error': '订单不存在'}), 404
        
        order['status'] = new_status
        from datetime import datetime
        order['updated_at'] = datetime.now().isoformat()
        
        print(f"订餐订单 {order_id} 状态已更新为: {new_status}")
        
        return jsonify({
            'success': True,
            'message': '订单状态更新成功',
            'order_id': order_id,
            'new_status': new_status
        }), 200
        
    except Exception as e:
        print(f"更新订餐订单状态失败: {str(e)}")
        return jsonify({'error': f'更新订餐订单状态失败: {str(e)}'}), 500
