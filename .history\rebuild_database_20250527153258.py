#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重建数据库脚本 - 包含区域自动分配功能
"""

import pymysql
import random
import sys
import os

# 添加web_project目录到路径以便导入config
sys.path.append(os.path.join(os.path.dirname(__file__), 'web_project'))
from config import Config

def rebuild_database_with_regions():
    """重建数据库，包含区域分配功能"""
    try:
        # 连接MySQL服务器
        connection = pymysql.connect(
            host=Config.MYSQL_HOST,
            port=int(Config.MYSQL_PORT),
            user=Config.MYSQL_USER,
            password=Config.MYSQL_PASSWORD
        )

        with connection.cursor() as cursor:
            # 删除现有数据库（如果存在）
            cursor.execute(f"DROP DATABASE IF EXISTS {Config.MYSQL_DB}")
            print(f"已删除现有数据库 '{Config.MYSQL_DB}'")

            # 创建新数据库
            cursor.execute(f"CREATE DATABASE {Config.MYSQL_DB} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print(f"已创建数据库 '{Config.MYSQL_DB}'")

            # 使用新数据库
            cursor.execute(f"USE {Config.MYSQL_DB}")

            # 创建老年人用户表（包含region字段）
            cursor.execute("""
                CREATE TABLE elderlyuser (
                    user_id VARCHAR(10) PRIMARY KEY,
                    password VARCHAR(50) NOT NULL,
                    name VARCHAR(50) NOT NULL,
                    age INT,
                    phone VARCHAR(20),
                    address VARCHAR(100),
                    emergency_contact_name VARCHAR(50),
                    emergency_contact_phone VARCHAR(20),
                    health_record_id VARCHAR(10),
                    smartwatch_id VARCHAR(10),
                    bound_family_ids VARCHAR(100),
                    region VARCHAR(10) COMMENT '老人所属区域（A或B）'
                )
            """)
            print("已创建ElderlyUser表")

            # 创建家属用户表
            cursor.execute("""
                CREATE TABLE familyuser (
                    family_id VARCHAR(10) PRIMARY KEY,
                    password VARCHAR(50) NOT NULL,
                    name VARCHAR(50) NOT NULL,
                    relationship VARCHAR(20),
                    phone VARCHAR(20),
                    bound_elderly_id VARCHAR(10),
                    FOREIGN KEY (bound_elderly_id) REFERENCES elderlyuser(user_id)
                )
            """)
            print("已创建FamilyUser表")

            # 创建社区工作人员表（包含password字段）
            cursor.execute("""
                CREATE TABLE communityworker (
                    worker_id VARCHAR(10) PRIMARY KEY,
                    password VARCHAR(50) NOT NULL,
                    name VARCHAR(50) NOT NULL,
                    region VARCHAR(10) COMMENT '负责区域（A或B）',
                    phone VARCHAR(20)
                )
            """)
            print("已创建CommunityWorker表")

            # 创建其他表
            cursor.execute("""
                CREATE TABLE smartwatch (
                    watch_id VARCHAR(10) PRIMARY KEY,
                    bound_user_id VARCHAR(10),
                    last_sync_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                    gps_location VARCHAR(50) COMMENT '格式: 纬度,经度',
                    heart_rate INT,
                    sleep_score INT,
                    step_count INT,
                    battery INT,
                    FOREIGN KEY (bound_user_id) REFERENCES elderlyuser(user_id)
                )
            """)
            print("已创建Smartwatch表")

            cursor.execute("""
                CREATE TABLE healthrecord (
                    record_id VARCHAR(10) PRIMARY KEY,
                    blood_pressure VARCHAR(20),
                    blood_sugar VARCHAR(20),
                    medication_record VARCHAR(1000),
                    medical_history VARCHAR(1000),
                    update_time DATE,
                    health_threshold VARCHAR(1000)
                )
            """)
            print("已创建HealthRecord表")

            cursor.execute("""
                CREATE TABLE EmergencyCall (
                    call_id VARCHAR(20) PRIMARY KEY,
                    elderly_id VARCHAR(10),
                    call_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                    gps_location VARCHAR(50) COMMENT '格式: 纬度,经度',
                    status VARCHAR(20) DEFAULT '待处理' COMMENT '待处理/已响应/已解决',
                    related_person_id VARCHAR(10) COMMENT '可能是家属ID或社区工作人员ID',
                    FOREIGN KEY (elderly_id) REFERENCES ElderlyUser(user_id)
                )
            """)
            print("已创建EmergencyCall表")

            # 插入社区工作人员数据
            cursor.execute("""
                INSERT INTO CommunityWorker (worker_id, password, name, region, phone) VALUES
                ('W_A01', '123456', '刘强', 'A', '13900000001'),
                ('W_B02', '123456', '王丽', 'B', '13900000002')
            """)
            print("已插入社区工作人员数据")

            # 插入测试老年人数据（自动分配区域）
            elderly_data = [
                ('E01', '123456', '张建国', 78, '13800000001', '浙江省杭州市余杭区龙湖天街1号', '张志强', '13800000011', 'HR01', 'SW01', 'F01,F02', 'A'),
                ('E02', '123456', '李淑兰', 75, '13800000002', '浙江省杭州市西湖区西溪路518号', '李朝阳', '13800000021', 'HR02', 'SW02', 'F03,F04', 'A'),
                ('E03', '123456', '王福海', 80, '13800000003', '浙江省杭州市拱墅区湖墅南路88号', '王宇轩', '13800000031', 'HR03', 'SW03', 'F05,F06', 'A')
            ]

            for data in elderly_data:
                cursor.execute("""
                    INSERT INTO ElderlyUser (user_id, password, name, age, phone, address, emergency_contact_name, emergency_contact_phone, health_record_id, smartwatch_id, bound_family_ids, region)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, data)
            print("已插入测试老年人数据")

            # 插入其他测试数据...
            family_data = [
                ('F01', '123456', '张志强', '儿子', '13800000011', 'E01'),
                ('F02', '123456', '张慧敏', '女儿', '13800000012', 'E01'),
                ('F03', '123456', '李朝阳', '儿子', '13800000021', 'E02'),
                ('F04', '123456', '李雅婷', '女儿', '13800000022', 'E02'),
                ('F05', '123456', '王宇轩', '儿子', '13800000031', 'E03'),
                ('F06', '123456', '王诗涵', '女儿', '13800000032', 'E03')
            ]

            for data in family_data:
                cursor.execute("""
                    INSERT INTO FamilyUser (family_id, password, name, relationship, phone, bound_elderly_id)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """, data)
            print("已插入家属用户数据")

            # 提交事务
            connection.commit()
            print("\n=== 数据库重建完成！===")
            print("功能特点：")
            print("1. 老人注册时自动分配区域A或B")
            print("2. 区域A由刘强负责，区域B由王丽负责")
            print("3. 所有表结构已更新，包含必要字段")

        connection.close()
        return True

    except Exception as e:
        print(f"重建数据库失败: {e}")
        return False

if __name__ == "__main__":
    rebuild_database_with_regions()
