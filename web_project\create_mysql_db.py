import pymysql
from config import Config

def create_database():
    """创建MySQL数据库"""
    try:
        # 连接MySQL服务器（不指定数据库）
        connection = pymysql.connect(
            host=Config.MYSQL_HOST,
            port=int(Config.MYSQL_PORT),
            user=Config.MYSQL_USER,
            password=Config.MYSQL_PASSWORD
        )
        
        with connection.cursor() as cursor:
            # 检查数据库是否存在
            cursor.execute(f"SHOW DATABASES LIKE '{Config.MYSQL_DB}'")
            result = cursor.fetchone()
            
            # 如果数据库不存在，则创建
            if not result:
                cursor.execute(f"CREATE DATABASE {Config.MYSQL_DB} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                print(f"数据库 '{Config.MYSQL_DB}' 创建成功！")
            else:
                print(f"数据库 '{Config.MYSQL_DB}' 已存在。")
                
        connection.close()
        print("MySQL连接测试成功！")
        return True
    except Exception as e:
        print(f"创建数据库时出错: {e}")
        return False

def create_test_data():
    """创建测试数据"""
    from app import create_app, db
    from app.models.models import ElderlyUser, FamilyUser, Smartwatch, HealthRecord, EmergencyCall, CommunityWorker
    from datetime import datetime, date
    
    app = create_app()
    
    with app.app_context():
        try:
            # 检查是否已有数据
            existing_users = ElderlyUser.query.first()
            if existing_users:
                print("数据库中已有测试数据，跳过创建。")
                return
                
            # 创建老年人用户
            elderly_users = [
                ElderlyUser(
                    user_id='E01', password='123456', name='张建国', age=78, 
                    phone='13800000001', address='浙江省杭州市余杭区龙湖天街1号', 
                    emergency_contact_name='张志强', emergency_contact_phone='13800000011',
                    health_record_id='HR01', smartwatch_id='SW01', bound_family_ids='F01,F02'
                ),
                ElderlyUser(
                    user_id='E02', password='123456', name='李淑兰', age=75, 
                    phone='13800000002', address='浙江省杭州市西湖区西溪路518号',
                    emergency_contact_name='李朝阳', emergency_contact_phone='13800000021',
                    health_record_id='HR02', smartwatch_id='SW02', bound_family_ids='F03,F04'
                ),
                ElderlyUser(
                    user_id='E03', password='123456', name='王福海', age=80, 
                    phone='13800000003', address='浙江省杭州市拱墅区湖墅南路88号',
                    emergency_contact_name='王宇轩', emergency_contact_phone='13800000031',
                    health_record_id='HR03', smartwatch_id='SW03', bound_family_ids='F05,F06'
                )
            ]
            
            # 创建家属用户
            family_users = [
                FamilyUser(family_id='F01', password='123456', name='张志强', relationship='儿子', 
                          phone='13800000011', bound_elderly_id='E01'),
                FamilyUser(family_id='F02', password='123456', name='张慧敏', relationship='女儿', 
                          phone='13800000012', bound_elderly_id='E01'),
                FamilyUser(family_id='F03', password='123456', name='李朝阳', relationship='儿子', 
                          phone='13800000021', bound_elderly_id='E02'),
                FamilyUser(family_id='F04', password='123456', name='李雅婷', relationship='女儿', 
                          phone='13800000022', bound_elderly_id='E02')
            ]
            
            # 创建社区工作人员
            community_workers = [
                CommunityWorker(worker_id='W_A01', password='123456', name='刘强', region='A', phone='13900000001'),
                CommunityWorker(worker_id='W_B02', password='123456', name='王丽', region='B', phone='13900000002')
            ]
            
            # 创建健康档案
            health_records = [
                HealthRecord(
                    record_id='HR01', blood_pressure='120/80', blood_sugar='5.6',
                    medication_record='阿司匹林:100mg:每日一次', medical_history='高血压',
                    update_time=date(2025, 5, 1), health_threshold='血压<140/90，心率<100'
                ),
                HealthRecord(
                    record_id='HR02', blood_pressure='130/85', blood_sugar='6.2',
                    medication_record='格列美脲:2mg:每日一次', medical_history='糖尿病',
                    update_time=date(2025, 5, 1), health_threshold='血压<140/90，心率<100'
                ),
                HealthRecord(
                    record_id='HR03', blood_pressure='140/90', blood_sugar='5.9',
                    medication_record='贝那普利:10mg:每日一次', medical_history='心脏病史',
                    update_time=date(2025, 5, 1), health_threshold='血压<140/90，心率<100'
                )
            ]
            
            # 创建智能手环数据
            smartwatches = [
                Smartwatch(
                    watch_id='SW01', bound_user_id='E01', 
                    last_sync_time=datetime(2025, 5, 5, 9, 0, 0),
                    gps_location='39.9042,116.4074', heart_rate=75, 
                    sleep_score=80, step_count=3000, battery=90
                ),
                Smartwatch(
                    watch_id='SW02', bound_user_id='E02', 
                    last_sync_time=datetime(2025, 5, 5, 9, 0, 0),
                    gps_location='39.9043,116.4075', heart_rate=70, 
                    sleep_score=85, step_count=2800, battery=88
                ),
                Smartwatch(
                    watch_id='SW03', bound_user_id='E03', 
                    last_sync_time=datetime(2025, 5, 5, 9, 0, 0),
                    gps_location='39.9044,116.4076', heart_rate=82, 
                    sleep_score=78, step_count=2600, battery=85
                )
            ]
            
            # 创建紧急呼叫记录
            emergency_calls = [
                EmergencyCall(
                    call_id='EC01', elderly_id='E01', 
                    call_time=datetime(2025, 5, 1, 12, 30, 0),
                    gps_location='39.9042,116.4074', status='已解决', 
                    related_person_id='F01'
                ),
                EmergencyCall(
                    call_id='EC02', elderly_id='E02', 
                    call_time=datetime(2025, 5, 2, 9, 15, 0),
                    gps_location='39.9043,116.4075', status='待处理', 
                    related_person_id='W_B02'
                )
            ]
            
            # 添加数据到数据库
            db.session.add_all(elderly_users)
            db.session.add_all(family_users)
            db.session.add_all(community_workers)
            db.session.add_all(health_records)
            db.session.add_all(smartwatches)
            db.session.add_all(emergency_calls)
            
            db.session.commit()
            print("测试数据创建成功！")
            
        except Exception as e:
            db.session.rollback()
            print(f"创建测试数据失败: {e}")

if __name__ == "__main__":
    if create_database():
        create_test_data() 