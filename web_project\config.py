import os

class Config:
    # 调试模式
    DEBUG = True

    # 禁用模板缓存
    TEMPLATES_AUTO_RELOAD = True

    # 密钥配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-key-for-elderly-care-system'

    # MySQL数据库配置
    MYSQL_HOST = os.environ.get('MYSQL_HOST') or 'localhost'
    MYSQL_PORT = os.environ.get('MYSQL_PORT') or '3306'
    MYSQL_USER = os.environ.get('MYSQL_USER') or 'root'
    MYSQL_PASSWORD = os.environ.get('MYSQL_PASSWORD') or 'pqr0713'
    MYSQL_DB = os.environ.get('MYSQL_DB') or 'elderly_care'

    # 数据库URI
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        f'mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DB}'
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # 地图API密钥
    MAP_API_KEY = os.environ.get('MAP_API_KEY') or '3ba2467a72df9bf471cbcbde0ebd9ea7'