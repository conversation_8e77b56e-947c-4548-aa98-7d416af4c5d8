#include <stdio.h>
#include <stdlib.h>
#include <pthread.h>
#include <semaphore.h>
#include <unistd.h>

#define R_NUM 5  // 读者数量
#define W_NUM 3  // 写者数量

int account = 2000;  // 共享账户余额

sem_t rmutex;     // 保护rcnt的互斥信号量
sem_t wmutex;     // 保护wcnt的互斥信号量
sem_t readTry;    // 控制读者能否尝试读取
sem_t resource;   // 控制对共享资源的访问

int rcnt = 0, wcnt = 0;

// 读者线程
void* reader(void* arg) {
    int id = (int)(long)arg;
    while(1) {
        // 读者进入区（写者优先控制）
        sem_wait(&readTry);
        sem_wait(&rmutex);
        rcnt++;
        if (rcnt == 1)
            sem_wait(&resource);  // 第一个读者锁住资源
        sem_post(&rmutex);
        sem_post(&readTry);

        // 模拟读操作
        printf("读者 %d 正在读取账户余额：%d\n", id, account);
        sleep(1);

        // 读者退出区
        sem_wait(&rmutex);
        rcnt--;
        if (rcnt == 0)
            sem_post(&resource);  // 最后一个读者释放资源
        sem_post(&rmutex);
    }
    pthread_exit(NULL);
}

// 写者线程
void* writer(void* arg) {
    int id = (int)(long)arg;
    while(1) {
        // 写者进入区
        sem_wait(&wmutex);
        wcnt++;
        if (wcnt == 1)
            sem_wait(&readTry);  // 第一个写者阻止读者进入
        sem_post(&wmutex);

        sem_wait(&resource);  // 独占访问资源

        // 模拟写操作
        int amount = 300;
        if(account>=amount){
                account -= amount;
        printf("写者 %d 取出 %d 元，账户余额变为 %d\n", id, amount, account);}
        else{
                printf("写者 %d 想取钱，但余额不足\n",id);
                }
        sleep(1);

        // 写者退出区
        sem_post(&resource);

        sem_wait(&wmutex);
        wcnt--;
        if (wcnt == 0)
            sem_post(&readTry);  // 最后一个写者允许读者进入
        sem_post(&wmutex);
    }
    pthread_exit(NULL);
}

int main() {
    pthread_t rtid[R_NUM], wtid[W_NUM];
        srand(time(NULL));

    // 初始化信号量
    sem_init(&rmutex, 0, 1);
    sem_init(&wmutex, 0, 1);
    sem_init(&readTry, 0, 1);
    sem_init(&resource, 0, 1);

    // 创建读者线程
    for (int i = 0; i < R_NUM; i++) {
        pthread_create(&rtid[i], NULL, reader, (void*)(long)i);
    }

      // 创建写者线程
    for (int i = 0; i < W_NUM; i++) {
        pthread_create(&wtid[i], NULL, writer, (void*)(long)i);
    }

    // 等待线程结束
    for (int i = 0; i < R_NUM; i++) pthread_join(rtid[i], NULL);
    for (int i = 0; i < W_NUM; i++) pthread_join(wtid[i], NULL);

    return 0;
}