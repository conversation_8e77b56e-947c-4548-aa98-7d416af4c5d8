<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>紧急呼叫测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        .emergency-btn {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            font-size: 24px;
            font-weight: bold;
            border: none;
            color: white;
            background: linear-gradient(45deg, #ff4757, #ff3838);
            box-shadow: 0 8px 20px rgba(255, 71, 87, 0.4);
            transition: all 0.3s ease;
        }

        .emergency-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 30px rgba(255, 71, 87, 0.6);
        }

        .emergency-btn:active {
            transform: scale(0.95);
        }

        .test-section {
            margin: 2rem 0;
            padding: 2rem;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .log-area {
            background: #000;
            color: #00ff00;
            padding: 1rem;
            border-radius: 5px;
            font-family: monospace;
            height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-5">🚨 紧急呼叫系统测试</h1>

        <!-- 老年端紧急呼叫测试 -->
        <div class="test-section">
            <h3>👴 老年端紧急呼叫测试</h3>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">老年人ID:</label>
                        <input type="text" id="elderlyId" class="form-control" value="E01" placeholder="输入老年人ID">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">紧急类型:</label>
                        <select id="emergencyType" class="form-select">
                            <option value="medical">医疗紧急</option>
                            <option value="fall">跌倒求助</option>
                            <option value="help">其他求助</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">位置信息:</label>
                        <input type="text" id="location" class="form-control" value="30.2741,120.1551" placeholder="GPS坐标">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">地址描述:</label>
                        <input type="text" id="address" class="form-control" value="杭州市西湖区西溪路518号" placeholder="详细地址">
                    </div>
                </div>
                <div class="col-md-6 text-center">
                    <button class="emergency-btn" onclick="triggerEmergencyCall()">
                        🚨<br>紧急呼叫
                    </button>
                </div>
            </div>
        </div>

        <!-- 家属端测试 -->
        <div class="test-section">
            <h3>👨‍👩‍👧‍👦 家属端测试</h3>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">家属ID:</label>
                        <input type="text" id="familyId" class="form-control" value="F01" placeholder="输入家属ID">
                    </div>
                    <button class="btn btn-primary" onclick="loadFamilyEvents()">获取紧急事件</button>
                    <button class="btn btn-success ms-2" onclick="respondAsFamily()">模拟家属响应</button>
                </div>
                <div class="col-md-6">
                    <div id="familyEvents"></div>
                </div>
            </div>
        </div>

        <!-- 工作人员端测试 -->
        <div class="test-section">
            <h3>👷‍♂️ 工作人员端测试</h3>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">工作人员ID:</label>
                        <input type="text" id="workerId" class="form-control" value="W01" placeholder="输入工作人员ID">
                    </div>
                    <button class="btn btn-primary" onclick="loadWorkerEvents()">获取紧急事件</button>
                    <button class="btn btn-success ms-2" onclick="respondAsWorker()">模拟工作人员响应</button>
                </div>
                <div class="col-md-6">
                    <div id="workerEvents"></div>
                </div>
            </div>
        </div>

        <!-- 日志区域 -->
        <div class="test-section">
            <h3>📋 操作日志</h3>
            <div class="log-area" id="logArea"></div>
            <button class="btn btn-secondary mt-2" onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <script>
        let currentEventId = null;

        function log(message) {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            logArea.innerHTML += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logArea').innerHTML = '';
        }

        async function triggerEmergencyCall() {
            const elderlyId = document.getElementById('elderlyId').value;
            const emergencyType = document.getElementById('emergencyType').value;
            const location = document.getElementById('location').value;
            const address = document.getElementById('address').value;

            log(`🚨 发起紧急呼叫: ${elderlyId} - ${emergencyType}`);

            try {
                const response = await axios.post('/api/emergency/event/create', {
                    elderly_id: elderlyId,
                    emergency_type: emergencyType,
                    location: location,
                    address: address
                });

                if (response.data && response.data.success) {
                    currentEventId = response.data.event_id;
                    log(`✅ 紧急呼叫成功创建: ${currentEventId}`);
                    alert(`紧急呼叫已发送！\n事件ID: ${currentEventId}`);

                    // 自动刷新家属端和工作人员端的事件列表
                    setTimeout(() => {
                        loadFamilyEvents();
                        loadWorkerEvents();
                    }, 1000);
                } else {
                    log(`❌ 紧急呼叫失败: ${response.data.error}`);
                    alert('紧急呼叫失败: ' + response.data.error);
                }
            } catch (error) {
                log(`❌ 紧急呼叫错误: ${error.message}`);
                alert('紧急呼叫错误: ' + error.message);
            }
        }

        async function loadFamilyEvents() {
            const familyId = document.getElementById('familyId').value;
            log(`🔍 家属 ${familyId} 获取紧急事件`);

            try {
                const response = await axios.get('/api/emergency/event/active', {
                    params: {
                        user_type: 'family',
                        user_id: familyId
                    }
                });

                if (response.data && response.data.events) {
                    const events = response.data.events;
                    log(`📊 家属获取到 ${events.length} 个事件`);

                    const eventsDiv = document.getElementById('familyEvents');
                    eventsDiv.innerHTML = '<h6>紧急事件列表:</h6>';

                    events.forEach(event => {
                        eventsDiv.innerHTML += `
                            <div class="alert alert-${event.status === '待处理' ? 'danger' : 'success'} p-2 mb-2">
                                <small>
                                    <strong>${event.event_id}</strong><br>
                                    类型: ${event.emergency_type}<br>
                                    状态: ${event.status}<br>
                                    时间: ${event.created_at}
                                </small>
                            </div>
                        `;
                    });
                } else {
                    log(`❌ 家属获取事件失败: ${response.data.error}`);
                }
            } catch (error) {
                log(`❌ 家属获取事件错误: ${error.message}`);
            }
        }

        async function respondAsFamily() {
            if (!currentEventId) {
                alert('请先创建一个紧急事件');
                return;
            }

            const familyId = document.getElementById('familyId').value;
            log(`👨‍👩‍👧‍👦 家属 ${familyId} 响应事件 ${currentEventId}`);

            try {
                const response = await axios.post('/api/emergency/event/respond', {
                    event_id: currentEventId,
                    user_type: 'family',
                    user_id: familyId,
                    response_note: '家属已收到，正在处理'
                });

                if (response.data && response.data.success) {
                    log(`✅ 家属响应成功: ${response.data.message}`);
                    alert('家属响应成功！');
                } else {
                    log(`❌ 家属响应失败: ${response.data.error}`);
                    alert('家属响应失败: ' + response.data.error);
                }
            } catch (error) {
                log(`❌ 家属响应错误: ${error.message}`);
                alert('家属响应错误: ' + error.message);
            }
        }

        async function loadWorkerEvents() {
            const workerId = document.getElementById('workerId').value;
            log(`🔍 工作人员 ${workerId} 获取紧急事件`);

            try {
                const response = await axios.get('/api/emergency/event/active', {
                    params: {
                        user_type: 'worker',
                        user_id: workerId
                    }
                });

                if (response.data && response.data.events) {
                    const events = response.data.events;
                    log(`📊 工作人员获取到 ${events.length} 个事件`);

                    const eventsDiv = document.getElementById('workerEvents');
                    eventsDiv.innerHTML = '<h6>紧急事件列表:</h6>';

                    events.forEach(event => {
                        eventsDiv.innerHTML += `
                            <div class="alert alert-${event.status === '待处理' ? 'danger' : 'success'} p-2 mb-2">
                                <small>
                                    <strong>${event.event_id}</strong><br>
                                    老人: ${event.elderly_name}<br>
                                    类型: ${event.emergency_type}<br>
                                    状态: ${event.status}<br>
                                    时间: ${event.created_at}
                                </small>
                            </div>
                        `;
                    });
                } else {
                    log(`❌ 工作人员获取事件失败: ${response.data.error}`);
                }
            } catch (error) {
                log(`❌ 工作人员获取事件错误: ${error.message}`);
            }
        }

        async function respondAsWorker() {
            if (!currentEventId) {
                alert('请先创建一个紧急事件');
                return;
            }

            const workerId = document.getElementById('workerId').value;
            log(`👷‍♂️ 工作人员 ${workerId} 响应事件 ${currentEventId}`);

            try {
                const response = await axios.post('/api/emergency/event/respond', {
                    event_id: currentEventId,
                    user_type: 'worker',
                    user_id: workerId,
                    response_note: '社区工作人员已收到，正在前往现场'
                });

                if (response.data && response.data.success) {
                    log(`✅ 工作人员响应成功: ${response.data.message}`);
                    alert('工作人员响应成功！');
                } else {
                    log(`❌ 工作人员响应失败: ${response.data.error}`);
                    alert('工作人员响应失败: ' + response.data.error);
                }
            } catch (error) {
                log(`❌ 工作人员响应错误: ${error.message}`);
                alert('工作人员响应错误: ' + error.message);
            }
        }

        // 页面加载时的初始化
        window.onload = function() {
            log('🚀 紧急呼叫测试页面已加载');
            log('📝 使用说明:');
            log('   1. 点击红色紧急呼叫按钮创建紧急事件');
            log('   2. 使用家属端和工作人员端按钮测试响应功能');
            log('   3. 观察日志了解系统运行状态');
        };
    </script>
</body>
</html>
