body {
    background: #f6f8fa;
    font-family: 'Noto Sans SC', sans-serif;
    color: #333;
}

.monitor-topbar {
    background: #f6f8fa;
    border-bottom: 1px solid #e9ecef;
    padding: 0.8rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
    z-index: 100;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    color: #333;
}

.monitor-topbar .top-actions > * { margin-right: 1.5rem; }
.monitor-topbar .top-actions > *:last-child { margin-right: 0; }

/* 顶栏按钮样式 */
.monitor-topbar .btn {
    border-radius: 8px;
    padding: 0.6rem 1.2rem;
    font-weight: 500;
    font-size: 1.05rem;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.monitor-topbar .btn-danger {
    background-color: #ff4d4f;
    border-color: #ff4d4f;
    box-shadow: 0 2px 6px rgba(255, 77, 79, 0.2);
}

.monitor-topbar .btn-danger:hover {
    background-color: #ff7875;
    border-color: #ff7875;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(255, 77, 79, 0.3);
}

.monitor-topbar .btn-outline-primary {
    color: #1765d5;
    border-color: #1765d5;
    background-color: transparent;
}

.monitor-topbar .btn-outline-primary:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.7);
    transform: translateY(-2px);
}

/* 用户下拉菜单样式 */
.user-dropdown {
    position: relative;
    display: inline-block;
    cursor: pointer;
}

.user-dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.1);
    transition: all 0.3s;
}

.user-dropdown-toggle:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.user-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    width: 220px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    z-index: 1000;
    overflow: hidden;
    margin-top: 0.5rem;
    visibility: hidden;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s;
}

.user-dropdown-menu.show {
    visibility: visible;
    opacity: 1;
    transform: translateY(0);
}

.user-dropdown-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 0.8rem 1.2rem;
    color: #333;
    text-decoration: none;
    transition: all 0.2s;
}

.user-dropdown-item:hover {
    background-color: #f0f7ff;
    color: #1765d5;
}

.user-dropdown-item i {
    font-size: 1.1rem;
    color: #666;
}

.user-dropdown-item:hover i {
    color: #1765d5;
}

.user-dropdown-divider {
    height: 1px;
    background-color: #e9ecef;
    margin: 0.5rem 0;
}

.user-dropdown-header {
    padding: 1rem 1.2rem;
    background-color: #f0f7ff;
    border-bottom: 1px solid #e9ecef;
}

.user-dropdown-header .user-name {
    font-weight: 600;
    color: #1765d5;
    font-size: 1.1rem;
    margin-bottom: 0.3rem;
}

.user-dropdown-header .user-role {
    color: #666;
    font-size: 0.9rem;
}

.monitor-layout {
    display: flex;
    flex-direction: column;  /* 修改为纵向布局 */
    height: calc(100vh - 56px); /* 调整高度，减去navbar的高度 */
    position: relative;
}

.monitor-content-wrapper {
    display: flex;
    flex: 1;
}

/* 侧边栏改进 */
.monitor-sidemenu {
    width: 280px;
    background: #fff;
    border-right: 1px solid #e9ecef;
    padding: 1.8rem 0.5rem 1.5rem 1.5rem;
    overflow-y: auto;
    box-shadow: 2px 0 10px rgba(0,0,0,0.03);
    flex-shrink: 0; /* 防止侧边栏被挤压 */
}
.monitor-sidemenu .menu-title {
    font-family: 'Noto Serif SC', serif;
    font-weight: 600;
    margin: 1.5rem 0 0.8rem 0.5rem;
    color: #1765d5;
    font-size: 1.25rem;
    display: flex;
    align-items: center;
    gap: 10px;
}
.monitor-sidemenu ul {
    list-style: none;
    padding-left: 1.2rem;
    margin-bottom: 1rem;
}
.monitor-sidemenu li {
    margin-bottom: 0.7rem;
    cursor: pointer;
    color: #4a5a6a;
    border-radius: 8px;
    padding: 0.5rem 0.8rem;
    transition: all 0.25s ease;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 10px;
}
.monitor-sidemenu li i {
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
    opacity: 0.8;
}
.monitor-sidemenu li.active, .monitor-sidemenu li:hover {
    background: #e6f0ff;
    color: #1765d5;
    box-shadow: 0 2px 8px rgba(23, 101, 213, 0.15);
    transform: translateX(5px);
}
.monitor-sidemenu li.active i, .monitor-sidemenu li:hover i {
    opacity: 1;
}

.monitor-workspace {
    flex: 1;
    padding: 1.8rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    background-color: #f8fafd;
}
.monitor-split {
    display: flex;
    gap: 1.8rem;
    flex: 1;
}
.monitor-panel {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.06);
    padding: 1.5rem 1.8rem;
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s, box-shadow 0.3s;
    border: 1px solid rgba(0,0,0,0.03);
}
.monitor-panel:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.08);
}
.monitor-panel-header {
    font-family: 'Noto Serif SC', serif;
    font-weight: 700;
    font-size: 1.5rem;
    margin-bottom: 0.8rem;
    color: #1765d5;
    display: flex;
    align-items: center;
    gap: 10px;
}
.monitor-panel-desc {
    color: #666;
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}
.health-matrix {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0 0.6rem;
    margin-bottom: 1.8rem;
}
.health-matrix td {
    border: 1px solid #e9ecef;
    padding: 1rem 1.5rem;
    text-align: center;
    font-size: 1.15rem;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.04);
    transition: transform 0.2s ease;
}
.health-matrix td:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0,0,0,0.08);
}
.health-matrix td[data-status="normal"] {
    background: #f6fff6;
    color: #2e7d32;
    border-left: 4px solid #2e7d32;
}
.health-matrix td[data-status="warning"] {
    background: #fffbe6;
    color: #faad14;
    font-weight: bold;
    border-left: 4px solid #faad14;
}
.health-matrix td[data-status="alert"] {
    background: #fff1f0;
    color: #ff4d4f;
    font-weight: bold;
    animation: blink 1s infinite;
    border-left: 4px solid #ff4d4f;
}
@keyframes blink { 0%{opacity:1;} 50%{opacity:0.5;} 100%{opacity:1;} }
.radar-container {
    width: 100%;
    max-width: 350px;
    margin: 0 auto;
}
.trend-chart-container {
    height: 400px; /* 增大高度 */
    margin-bottom: 2.5rem; /* 增加间距 */
    background-color: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}
.trend-chart-center {
    height: 450px; /* 进一步增大高度 */
    margin-bottom: 3rem; /* 增加间距 */
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 2rem; /* 下移 */
}
.amap-container {
    width: 100%;
    height: 320px;
    border-radius: 15px;
    overflow: hidden;
    margin-bottom: 1.8rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* 位置信息卡片样式 */
.location-info-card {
    background: #f0f8ff;
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #e6f0ff;
    box-shadow: 0 4px 15px rgba(23,101,213,0.1);
}

.location-info-card .info-item {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.location-info-card .info-item label {
    font-weight: 600;
    color: #1765d5;
    margin-bottom: 0;
    min-width: 100px;
    font-size: 1rem;
}

.location-info-card .info-item span {
    color: #333;
    font-size: 1rem;
    flex: 1;
}
.table-responsive {
    max-height: 300px;
    overflow-y: auto;
    border-radius: 10px;
    box-shadow: 0 3px 12px rgba(0,0,0,0.05);
}
.meal-bar-warning {
    color: #fff;
    background: #ff7875;
    border-radius: 50px;
    padding: 0.2rem 0.8rem;
    font-size: 1rem;
    margin-left: 8px;
    box-shadow: 0 2px 6px rgba(255,120,117,0.2);
}
.calendar-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1.5rem;
    box-shadow: 0 3px 12px rgba(0,0,0,0.05);
    border-radius: 10px;
    overflow: hidden;
}
.calendar-table th, .calendar-table td {
    border: 1px solid #e9ecef;
    text-align: center;
    padding: 0.8rem 0.5rem;
    font-size: 1.05rem;
}
.calendar-table th {
    background-color: #f8fafd;
    font-weight: 600;
    color: #1765d5;
}
.calendar-table .conflict {
    background: #fff1f0;
    color: #ff4d4f;
    font-weight: bold;
}
.timeline {
    list-style: none;
    padding-left: 0;
    margin-top: 1rem;
}
.timeline li {
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.05);
    transition: transform 0.2s ease;
    position: relative;
    border-left: 4px solid #1765d5;
    display: flex;
    align-items: center;
}
.timeline li:hover {
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}
.timeline li::before {
    content: '';
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    background-color: #1765d5;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 0 0 2px #1765d5;
}
.timeline .ai-flag {
    color: #faad14;
    font-weight: bold;
    margin-left: 0.8rem;
    background-color: #fffbe6;
    padding: 0.2rem 0.6rem;
    border-radius: 50px;
    font-size: 0.9rem;
}
.timeline .ai-btn {
    margin-left: 1rem;
    font-size: 1rem;
    background-color: #1765d5;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 0.3rem 0.8rem;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    transition: all 0.2s ease;
}
.timeline .ai-btn:hover {
    background-color: #104db3;
    box-shadow: 0 3px 8px rgba(23,101,213,0.3);
}

/* 实时体征看板样式 - 修改为蓝色风格并增大字体 */
.vital-signs-summary {
    background: #f0f8ff;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-left: 6px solid #1765d5;
    box-shadow: 0 5px 15px rgba(23,101,213,0.1);
}

.vital-signs-summary .summary-title {
    color: #1765d5;
    font-weight: bold;
    margin-bottom: 0.8rem;
    font-size: 1.4rem;
    font-family: 'Noto Serif SC', serif;
    display: flex;
    align-items: center;
}

.vital-signs-summary .summary-content {
    color: #333;
    font-size: 1.15rem;
    line-height: 1.6;
}

.vital-signs-table {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.2rem;
    width: 100%;
    margin-bottom: 2rem;
}

.vital-signs-table tr {
    display: contents;
}

.vital-signs-table td {
    background: #fff;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(91,143,249,0.08);
    border: 1px solid #e6f0ff;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    position: relative;
    display: flex;
    flex-direction: column;
}

.vital-signs-table td:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(91,143,249,0.15);
}

.vital-signs-table th {
    display: none;
}

.vital-signs-table .value-container {
    display: flex;
    align-items: baseline;
    gap: 0.5rem;
    margin-bottom: 0.8rem;
}

.vital-signs-table .indicator {
    font-size: 1.1rem;
    color: #1765d5;
    font-weight: 600;
    margin-bottom: 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.vital-signs-table .indicator i {
    font-size: 1.3rem;
}

.vital-signs-table .value {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
}

.vital-signs-table .unit {
    font-size: 1rem;
    color: #666;
}

.vital-signs-table .trend {
    font-size: 1.2rem;
    font-weight: bold;
    margin-left: 0.4rem;
}

.vital-signs-table .trend.up {
    color: #ff4d4f;
}

.vital-signs-table .trend.down {
    color: #52c41a;
}

.vital-signs-table .last-update {
    font-size: 0.9rem;
    color: #666;
    margin-top: auto;
    display: flex;
    align-items: center;
    gap: 0.4rem;
}

.vital-signs-table td[data-status="warning"] {
    border-left: 4px solid #faad14;
}

.vital-signs-table td[data-status="alert"] {
    border-left: 4px solid #ff4d4f;
}

.vital-signs-table td.value {
    font-weight: bold;
    text-align: right;
}

.vital-signs-table td[data-status="normal"] .value {
    color: #2e7d32;
}

.vital-signs-table td[data-status="warning"] .value {
    color: #333;
    font-weight: 700;
}

.vital-signs-table td[data-status="alert"] .value {
    color: #ff4d4f;
    animation: blink 1s infinite;
}

.vital-signs-table td[data-status="warning"],
.vital-signs-table td[data-status="alert"] {
    background: #e6f0ff;
    border: 1.5px solid #5B8FF9;
}

.vital-signs-table td[data-status="alert"] {
    background: #fff1f0;
    border-color: #ff7875;
}

.vital-signs-table .trend {
    font-size: 1.1rem;
    margin-left: 0.6rem;
    font-weight: bold;
}

.vital-signs-table .trend.up {
    color: #ff4d4f;
}

.vital-signs-table .trend.down {
    color: #52c41a;
}

.vital-signs-table .unit {
    font-size: 1.1rem;
    color: #555;
    margin-left: 0.4rem;
}

.vital-signs-table .value {
    font-size: 1.5rem;
    color: #333;
    font-weight: 700;
}

.vital-signs-table .last-update {
    font-size: 0.95rem;
    color: #666;
    margin-top: 0.6rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

/* 用药提醒样式 */
.medication-reminder {
    background: #fff8f0;
    border-radius: 10px;
    padding: 1.2rem 1.5rem;
    margin-bottom: 1.5rem;
    border-left: 4px solid #ffa940;
    box-shadow: 0 4px 12px rgba(255,169,64,0.15);
}

.medication-reminder h4 {
    color: #fa8c16;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.medication-cards {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.medication-card {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    border: 1px solid #ffe7ba;
}

.medication-card h5 {
    color: #fa8c16;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.medication-card p {
    margin-bottom: 0.3rem;
    font-size: 0.95rem;
}

/* 生活监控模块样式 */
.lifestyle-monitor {
    background: #f0f7ff;
    border-radius: 16px;
    padding: 1.8rem 2rem;
    margin-bottom: 2.5rem;
    border-left: 6px solid #1765d5;
    box-shadow: 0 6px 20px rgba(23,101,213,0.12);
}

.lifestyle-monitor h4 {
    color: #1765d5;
    margin-bottom: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.7rem;
    font-size: 1.6rem;
    font-family: 'Noto Serif SC', serif;
    font-weight: 700;
}

.lifestyle-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
}

.lifestyle-card {
    background: white;
    border-radius: 14px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    border: 1px solid rgba(23,101,213,0.1);
    transition: transform 0.3s, box-shadow 0.3s;
}

.lifestyle-card:hover {
    transform: translateY(-7px);
    box-shadow: 0 12px 25px rgba(23,101,213,0.15);
}

.lifestyle-card h5 {
    color: #1765d5;
    margin-bottom: 1rem;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.6rem;
    font-family: 'Noto Serif SC', serif;
    font-weight: 600;
}

.lifestyle-card h5 i {
    font-size: 1.5rem;
}

.lifestyle-card p {
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.lifestyle-card .data-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.7rem;
    font-size: 1.1rem;
}

.lifestyle-card .data-row strong {
    font-weight: 600;
    color: #555;
}

.lifestyle-card .data-row span {
    color: #333;
}

.lifestyle-card .data-row .fw-bold {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1765d5;
}

.lifestyle-card .progress {
    height: 10px;
    margin-top: 0.6rem;
    margin-bottom: 1.2rem;
    border-radius: 5px;
    background-color: #e9ecef;
    overflow: hidden;
}

.lifestyle-card .progress-bar {
    transition: width 0.6s ease;
}

.lifestyle-card .card-footer {
    margin-top: 1.2rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
    font-size: 1rem;
    color: #666;
}

.lifestyle-card .trend-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    font-weight: 500;
}

.trend-indicator.positive {
    color: #52c41a;
}

.trend-indicator.negative {
    color: #ff4d4f;
}

.trend-indicator.neutral {
    color: #faad14;
}

.trend-indicator i {
    font-size: 1.1rem;
}

/* 历史趋势分析样式 */
.trend-analysis-container {
    display: flex;
    gap: 2.5rem;
    margin-bottom: 2rem;
}

.trend-chart-box {
    flex: 1.3;
    background: white;
    border-radius: 16px;
    padding: 1.8rem;
    box-shadow: 0 5px 20px rgba(91,143,249,0.1);
    border: 1px solid rgba(91,143,249,0.1);
    transition: transform 0.3s ease;
}

.trend-analysis-box {
    flex: 1.1;
    min-width: 450px;
    background: #f7faff;
    border-radius: 16px;
    padding: 1.8rem;
    box-shadow: 0 5px 20px rgba(91,143,249,0.08);
    display: flex;
    flex-direction: column;
    border-left: 6px solid #1765d5;
    transition: transform 0.3s ease;
}

.trend-analysis-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 30px rgba(91,143,249,0.12);
}

.analysis-title {
    color: #1765d5;
    font-weight: bold;
    margin-bottom: 1.2rem;
    font-size: 1.5rem;
    font-family: 'Noto Serif SC', serif;
}

.analysis-content {
    flex: 1;
    color: #333;
    font-size: 1.1rem;
    line-height: 1.6;
}

.analysis-content ul {
    padding-left: 1.8rem;
    margin-top: 0.8rem;
    margin-bottom: 1.2rem;
}

.analysis-content li {
    margin-bottom: 0.8rem;
    position: relative;
}

.analysis-content li::marker {
    color: #1765d5;
    font-size: 1.1em;
}

.risk-tags-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2.5rem;
    margin-top: 2rem;
    padding: 1rem 0;
}

.risk-tag {
    min-width: 200px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    font-weight: 600;
    border-radius: 12px;
    padding: 0 1.8rem;
    border: 2px solid;
    text-align: center;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.risk-tag:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.1);
}

.risk-tag.warning {
    background: #fffbe6;
    color: #faad14;
    border-color: #ffe58f;
}

.risk-tag.alert {
    background: #fff1f0;
    color: #ff4d4f;
    border-color: #ffa39e;
}

/* 实时位置与安全围栏样式 - 修改为单一地图 */
.location-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}
.map-container {
    width: 100%;
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 12px rgba(91,143,249,0.08);
}
.map-title {
    color: #1765d5;
    margin-bottom: 1rem;
    font-weight: 600;
}
.fence-controls {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
    margin-bottom: 1rem;
}
.entry-exit-container {
    width: 100%;
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 12px rgba(91,143,249,0.08);
}
.entry-exit-table {
    width: 100%;
    border-collapse: collapse;
}
.entry-exit-table th {
    background: #f0f8ff;
    padding: 0.8rem;
    text-align: left;
    border-bottom: 2px solid #e6f0ff;
    color: #1765d5;
    font-weight: 600;
}
.entry-exit-table td {
    padding: 0.8rem;
    border-bottom: 1px solid #e6f0ff;
}

/* 应急响应中心样式改进 */
.emergency-response {
    padding: 1rem;
}

.emergency-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1.5rem;
    font-size: 1.05rem;
}

.emergency-table th {
    background: #f0f8ff;
    padding: 1rem;
    text-align: left;
    border-bottom: 2px solid #e6f0ff;
    color: #1765d5;
    font-weight: 600;
}

.emergency-table td {
    padding: 1.2rem 1rem;
    border-bottom: 1px solid #e6f0ff;
}

.emergency-badge {
    display: inline-block;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    font-size: 0.95rem;
    font-weight: 500;
}

.emergency-badge.danger {
    background: #fff1f0;
    color: #ff4d4f;
}

.emergency-badge.warning {
    background: #fffbe6;
    color: #faad14;
}

.emergency-progress {
    height: 24px;
    background: #f0f8ff;
    border-radius: 12px;
    overflow: hidden;
    margin-top: 1.5rem;
}

.emergency-progress-bar {
    height: 100%;
    background: #52c41a;
    color: white;
    text-align: center;
    line-height: 24px;
    font-weight: 500;
    transition: width 0.5s;
}

.emergency-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.emergency-actions button {
    padding: 0.8rem 1.2rem;
    border-radius: 8px;
    border: none;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.emergency-actions .primary {
    background: #1765d5;
    color: white;
}

.emergency-actions .danger {
    background: #ff4d4f;
    color: white;
}

/* 医疗档案管理样式改进 */
.medical-records {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 1.8rem;
    margin: 2rem 0;
    width: 100%;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.medical-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: all 0.3s;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(0, 0, 0, 0.05);
    height: 100%;
}

.medical-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
    border-color: #1765d5;
}

.medical-card .card-header {
    padding: 1.2rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-bottom: 1px solid #f0f0f0;
    background: linear-gradient(to right, #e6f0ff, #f0f7ff);
}

.medical-card .card-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #1765d5, #5B8FF9);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: 0 4px 10px rgba(23, 101, 213, 0.2);
}

.medical-card .card-title-wrapper {
    flex: 1;
}

.medical-card .card-title {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
    color: #1765d5;
    font-family: 'Noto Serif SC', serif;
}

.medical-card .card-date {
    font-size: 0.9rem;
    color: #666;
    display: block;
    margin-top: 0.3rem;
}

.medical-card .card-content {
    padding: 1.5rem;
    flex: 1;
}

.medical-card .card-content p {
    font-size: 1.1rem;
    color: #333;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.medical-card .card-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.medical-card .card-tag {
    font-size: 0.85rem;
    padding: 0.3rem 0.8rem;
    border-radius: 1rem;
    background: #f0f0f0;
    color: #555;
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
}

.medical-card .card-tag.warning {
    background: #fffbe6;
    color: #d48806;
}

.medical-card .card-actions {
    padding: 1rem 1.5rem;
    display: flex;
    gap: 0.8rem;
    border-top: 1px solid #f0f0f0;
    background: #fafafa;
}

.medical-card .action-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #d9d9d9;
    background: white;
    border-radius: 0.5rem;
    color: #666;
    flex: 1;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.4rem;
    transition: all 0.2s;
    cursor: pointer;
    font-size: 0.95rem;
}

.medical-card .action-btn:hover {
    border-color: #1765d5;
    color: #1765d5;
    background: #f0f7ff;
}

.medical-card .action-btn.primary {
    background: #1765d5;
    color: white;
    border-color: #1765d5;
}

.medical-card .action-btn.primary:hover {
    background: #0d5bc2;
    box-shadow: 0 3px 8px rgba(23, 101, 213, 0.2);
}

@media (max-width: 768px) {
    .medical-records {
        grid-template-columns: 1fr;
        grid-template-rows: auto;
    }

    .medical-records-header {
        flex-direction: column;
        align-items: stretch;
    }

    .upload-btn {
        width: 100%;
    }
}

/* 医疗档案管理相关样式 */
.medical-records-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.medical-records-search {
    flex: 1;
    min-width: 280px;
}

.upload-btn {
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    border-radius: 0.5rem;
    background: linear-gradient(135deg, #1765d5, #5B8FF9);
    border: none;
    box-shadow: 0 4px 15px rgba(23, 101, 213, 0.2);
    transition: all 0.3s;
    font-size: 1.1rem;
}

.upload-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(23, 101, 213, 0.3);
    background: linear-gradient(135deg, #0d5bc2, #4a7fe6);
}

.medical-records-filter {
    display: flex;
    gap: 0.8rem;
    flex-wrap: wrap;
}

.filter-btn {
    background: #f0f7ff;
    border: 1px solid #d9e8ff;
    color: #1765d5;
    padding: 0.6rem 1.2rem;
    border-radius: 2rem;
    font-weight: 500;
    transition: all 0.2s;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-btn:hover, .filter-btn.active {
    background: #1765d5;
    color: white;
    border-color: #1765d5;
    box-shadow: 0 3px 10px rgba(23, 101, 213, 0.2);
}

.medical-records {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 1.8rem;
    margin: 2rem 0;
    width: 100%;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.medical-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: all 0.3s;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(0, 0, 0, 0.05);
    height: 100%;
}

.medical-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
    border-color: #1765d5;
}

.medical-card .card-header {
    padding: 1.2rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-bottom: 1px solid #f0f0f0;
    background: linear-gradient(to right, #e6f0ff, #f0f7ff);
}

.medical-card .card-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #1765d5, #5B8FF9);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: 0 4px 10px rgba(23, 101, 213, 0.2);
}

.medical-card .card-title-wrapper {
    flex: 1;
}

.medical-card .card-title {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
    color: #1765d5;
    font-family: 'Noto Serif SC', serif;
}

.medical-card .card-date {
    font-size: 0.9rem;
    color: #666;
    display: block;
    margin-top: 0.3rem;
}

.medical-card .card-content {
    padding: 1.5rem;
    flex: 1;
}

.medical-card .card-content p {
    font-size: 1.1rem;
    color: #333;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.medical-card .card-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.medical-card .card-tag {
    font-size: 0.85rem;
    padding: 0.3rem 0.8rem;
    border-radius: 1rem;
    background: #f0f0f0;
    color: #555;
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
}

.medical-card .card-tag.warning {
    background: #fffbe6;
    color: #d48806;
}

.medical-card .card-actions {
    padding: 1rem 1.5rem;
    display: flex;
    gap: 0.8rem;
    border-top: 1px solid #f0f0f0;
    background: #fafafa;
}

.medical-card .action-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #d9d9d9;
    background: white;
    border-radius: 0.5rem;
    color: #666;
    flex: 1;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.4rem;
    transition: all 0.2s;
    cursor: pointer;
    font-size: 0.95rem;
}

.medical-card .action-btn:hover {
    border-color: #1765d5;
    color: #1765d5;
    background: #f0f7ff;
}

.medical-card .action-btn.primary {
    background: #1765d5;
    color: white;
    border-color: #1765d5;
}

.medical-card .action-btn.primary:hover {
    background: #0d5bc2;
    box-shadow: 0 3px 8px rgba(23, 101, 213, 0.2);
}

@media (max-width: 768px) {
    .medical-records {
        grid-template-columns: 1fr;
        grid-template-rows: auto;
    }

    .medical-records-header {
        flex-direction: column;
        align-items: stretch;
    }

    .upload-btn {
        width: 100%;
    }
}

/* 活动行为分析样式 */
.activity-analysis-container {
    display: flex;
    gap: 2.5rem;
    margin-bottom: 2rem;
    width: 100%;
    justify-content: center; /* 添加居中对齐 */
}

.activity-chart-wrapper {
    flex: 1;
    background: #f7faff;
    border-radius: 16px;
    padding: 1.8rem;
    box-shadow: 0 5px 20px rgba(91,143,249,0.08);
    border: 1px solid #e6f0ff;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;
    align-items: center; /* 添加居中对齐 */
}

.activity-chart {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 1rem 0;
    flex: 1;
    width: 100%; /* 确保图表占满容器宽度 */
}

/* 生活服务代理样式 */
.service-container {
    display: flex;
    gap: 2.5rem;
    width: 100%;
    margin-top: 1.5rem;
}

.service-form-wrapper {
    flex: 1;
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(91,143,249,0.08);
    border: 1px solid #e6f0ff;
}

.service-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
    font-size: 1.1rem;
}

.input-wrapper {
    position: relative;
}

.input-wrapper i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #1765d5;
    pointer-events: none;
    z-index: 10; /* 提高图标的z-index以确保显示 */
    text-align: right; /* 确保图标文本靠右 */
}

.form-control, .form-select {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    font-size: 1.1rem;
    transition: all 0.3s;
    position: relative;
    background-color: white;
    -webkit-appearance: auto; /* 恢复为原生样式 */
    -moz-appearance: auto; /* 恢复为原生样式 */
    appearance: auto; /* 恢复为原生样式 */
}

.form-control:focus, .form-select:focus {
    border-color: #1765d5;
    box-shadow: 0 0 0 3px rgba(23,101,213,0.1);
    outline: none;
}

/* 调整日期选择器右侧图标 */
input[type="date"]::-webkit-calendar-picker-indicator {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
}

.btn-submit {
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #1765d5, #5B8FF9);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1rem;
    align-self: center;
}

.btn-submit:hover {
    background: linear-gradient(135deg, #104db3, #4a7fe6);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(23,101,213,0.2);
}

.service-history {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.nutrition-advice {
    background: #f0f8ff;
    border-radius: 12px;
    padding: 1.5rem;
    border-left: 4px solid #1765d5;
    box-shadow: 0 4px 15px rgba(23,101,213,0.1);
}

.nutrition-advice h5 {
    color: #1765d5;
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.history-title {
    margin: 1rem 0;
    color: #333;
    font-weight: 600;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.history-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.history-item {
    display: flex;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 3px 15px rgba(0,0,0,0.05);
    transition: transform 0.2s ease;
    border: 1px solid #f0f0f0;
}

.history-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.08);
}

.history-date {
    background: #f0f7ff;
    padding: 1rem;
    font-weight: 500;
    color: #1765d5;
    display: flex;
    align-items: center;
    min-width: 120px;
    justify-content: center;
    border-right: 1px dashed #d9e8ff;
}

.history-content {
    padding: 1rem 1.5rem;
    flex: 1;
    position: relative;
}

.history-content p {
    margin: 0 0 0.3rem 0;
}

.status-tag {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    padding: 0.3rem 0.8rem;
    border-radius: 50px;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
}

.status-tag.done {
    background: #f6ffed;
    color: #52c41a;
}

.status-tag.pending {
    background: #fff7e6;
    color: #fa8c16;
}

.service-subtitle {
    color: #1765d5;
    font-weight: 600;
    margin-bottom: 1.5rem;
    font-size: 1.4rem;
    border-bottom: 2px solid rgba(23,101,213,0.1);
    padding-bottom: 0.8rem;
    display: flex;
    align-items: center;
}

.form-select {
    -webkit-appearance: menulist !important; /* 强制使用原生下拉菜单样式 */
    -moz-appearance: menulist !important;
    appearance: menulist !important;
    background-image: none !important; /* 移除任何自定义背景箭头 */
}

/* 移除.input-wrapper内的图标显示 */
.input-wrapper i {
    display: none;
}

/* 改善图标和文字的布局 */
.vital-signs-table i {
    margin-right: 10px;
    vertical-align: middle;
    font-size: 1.3rem;
    display: inline-block; /* 确保图标是内联块元素 */
}

/* 确保文本容器足够宽 */
.vital-signs-table td {
    min-width: 200px;
    padding: 0.8rem 1.2rem;
    vertical-align: middle;
}

/* 确保th标签中的内容不会换行，且保持在一行 */
.vital-signs-table th {
    display: flex;
    align-items: center;
    white-space: nowrap;
    width: auto;
    min-width: 220px;
}

.vital-signs-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 1.2rem;
    margin-bottom: 2rem;
}

.vital-signs-row {
    display: flex;
    gap: 1.2rem;
    width: 100%;
}

/* 第一行三等分 */
.vital-signs-row:first-child .vital-sign-card {
    flex: 1;
    width: calc(33.333% - 0.8rem);
}

/* 第二行两等分 */
.vital-signs-row:last-child {
    justify-content: center;
    gap: 2rem;
}

.vital-signs-row:last-child .vital-sign-card {
    width: calc(40% - 1rem);
}

.vital-sign-card {
    background: #fff;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(91,143,249,0.08);
    border: 1px solid #e6f0ff;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    display: flex;
    flex-direction: column;
}

.vital-sign-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(91,143,249,0.15);
}

.vital-sign-card .indicator {
    font-size: 1.1rem;
    color: #1765d5;
    font-weight: 600;
    margin-bottom: 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.vital-sign-card .indicator i {
    font-size: 1.3rem;
}

.vital-sign-card .value-container {
    display: flex;
    align-items: baseline;
    gap: 0.5rem;
    margin-bottom: 0.8rem;
}

.vital-sign-card .value {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
}

.vital-sign-card .unit {
    font-size: 1rem;
    color: #666;
}

.vital-sign-card .trend {
    font-size: 1.2rem;
    font-weight: bold;
    margin-left: 0.4rem;
}

.vital-sign-card .trend.up {
    color: #ff4d4f;
}

.vital-sign-card .trend.down {
    color: #52c41a;
}

.vital-sign-card .last-update {
    font-size: 0.9rem;
    color: #666;
    margin-top: auto;
    display: flex;
    align-items: center;
    gap: 0.4rem;
}

.vital-sign-card[data-status="warning"] {
    border-left: 4px solid #faad14;
}

.vital-sign-card[data-status="alert"] {
    border-left: 4px solid #ff4d4f;
}

/* 紧急事件卡片样式 */
.emergency-event-card {
    transition: all 0.3s ease;
    border-radius: 12px;
    overflow: hidden;
}

.emergency-event-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.emergency-event-card.border-danger {
    border-left: 5px solid #dc3545;
    background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);
}

.emergency-event-card.border-success {
    border-left: 5px solid #28a745;
    background: linear-gradient(135deg, #f0fff4 0%, #ffffff 100%);
}

.emergency-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 120px;
}

.emergency-actions .btn {
    font-size: 0.9rem;
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.emergency-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.emergency-events-list {
    max-height: 600px;
    overflow-y: auto;
    padding-right: 0.5rem;
}

.emergency-events-list::-webkit-scrollbar {
    width: 6px;
}

.emergency-events-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.emergency-events-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.emergency-events-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 紧急事件响应式设计 */
@media (max-width: 768px) {
    .emergency-actions {
        flex-direction: row;
        min-width: auto;
    }

    .emergency-event-card .d-flex {
        flex-direction: column;
        align-items: stretch;
    }

    .emergency-actions {
        margin-top: 1rem;
        justify-content: flex-start;
    }
}

</rewritten_file>