<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复数据库</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <div style="padding: 20px;">
        <h1>修复数据库用户绑定关系</h1>
        <button onclick="fixBindings()" style="padding: 10px 20px; font-size: 16px;">修复用户绑定</button>
        <div id="result" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc;"></div>
    </div>

    <script>
        async function fixBindings() {
            try {
                console.log('🔧 开始修复用户绑定关系...');
                const response = await axios.post('/api/fix/fix_bindings');
                
                console.log('✅ 修复响应:', response.data);
                
                document.getElementById('result').innerHTML = `
                    <h3>修复成功！</h3>
                    <pre>${JSON.stringify(response.data, null, 2)}</pre>
                `;
                
                alert('用户绑定关系修复成功！');
                
            } catch (error) {
                console.error('❌ 修复失败:', error);
                document.getElementById('result').innerHTML = `
                    <h3>修复失败</h3>
                    <p style="color: red;">${error.message}</p>
                `;
                alert('修复失败: ' + error.message);
            }
        }
    </script>
</body>
</html>
