#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新老年人GPS坐标为杭州实际位置
"""

import sys
import os
from datetime import datetime

# 切换到web_project目录
os.chdir('web_project')
sys.path.insert(0, os.getcwd())

from app import create_app, db
from app.models.models import ElderlyUser, Smartwatch

def update_hangzhou_coordinates():
    """更新三个老年人的GPS坐标为杭州实际位置"""

    app = create_app()

    with app.app_context():
        try:
            # 杭州三个具体位置的GPS坐标
            hangzhou_coordinates = {
                'E01': {
                    'name': '张建国',
                    'address': '浙江省杭州市余杭区龙湖天街1号',
                    'gps_location': '30.2741,120.1551'  # 余杭区龙湖天街附近
                },
                'E02': {
                    'name': '李淑兰',
                    'address': '浙江省杭州市西湖区西溪路518号',
                    'gps_location': '30.2592,120.1136'  # 西湖区西溪路附近
                },
                'E03': {
                    'name': '王福海',
                    'address': '浙江省杭州市拱墅区湖墅南路88号',
                    'gps_location': '30.3056,120.1442'  # 拱墅区湖墅南路附近
                }
            }

            print("开始更新老年人GPS坐标...")
            print("=" * 50)

            for elderly_id, data in hangzhou_coordinates.items():
                # 查找老年人记录
                elderly = ElderlyUser.query.filter_by(user_id=elderly_id).first()
                if not elderly:
                    print(f"❌ 未找到老年人记录: {elderly_id}")
                    continue

                # 查找对应的智能手环
                smartwatch = Smartwatch.query.filter_by(bound_user_id=elderly_id).first()
                if not smartwatch:
                    print(f"❌ 未找到智能手环记录: {elderly_id}")
                    continue

                # 更新GPS坐标
                old_location = smartwatch.gps_location
                smartwatch.gps_location = data['gps_location']
                smartwatch.last_sync_time = datetime.now()

                print(f"✅ 更新 {data['name']} ({elderly_id}):")
                print(f"   地址: {data['address']}")
                print(f"   旧坐标: {old_location}")
                print(f"   新坐标: {data['gps_location']}")
                print(f"   更新时间: {smartwatch.last_sync_time}")
                print()

            # 提交更改
            db.session.commit()
            print("🎉 所有GPS坐标更新成功！")
            print("=" * 50)

            # 验证更新结果
            print("验证更新结果:")
            for elderly_id in hangzhou_coordinates.keys():
                elderly = ElderlyUser.query.filter_by(user_id=elderly_id).first()
                smartwatch = Smartwatch.query.filter_by(bound_user_id=elderly_id).first()
                if elderly and smartwatch:
                    print(f"📍 {elderly.name} ({elderly_id}): {smartwatch.gps_location}")

        except Exception as e:
            db.session.rollback()
            print(f"❌ 更新失败: {e}")
            return False

    return True

if __name__ == "__main__":
    print("🚀 开始更新杭州老年人GPS坐标...")
    if update_hangzhou_coordinates():
        print("✅ 更新完成！现在社区工作人员可以在地图上看到正确的老年人位置了。")
    else:
        print("❌ 更新失败，请检查错误信息。")
