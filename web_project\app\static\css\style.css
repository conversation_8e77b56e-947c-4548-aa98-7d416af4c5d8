/* 全局样式 */
body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* 导航栏样式 */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s, box-shadow 0.3s;
    margin-bottom: 20px;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-radius: 8px 8px 0 0 !important;
    font-weight: 600;
}

/* 按钮样式 */
.btn {
    border-radius: 5px;
    font-weight: 500;
    padding: 8px 16px;
}

.btn-primary {
    background-color: #1890ff;
    border-color: #1890ff;
}

.btn-primary:hover {
    background-color: #40a9ff;
    border-color: #40a9ff;
}

.btn-success {
    background-color: #52c41a;
    border-color: #52c41a;
}

.btn-success:hover {
    background-color: #73d13d;
    border-color: #73d13d;
}

.btn-danger {
    background-color: #ff4d4f;
    border-color: #ff4d4f;
}

.btn-danger:hover {
    background-color: #ff7875;
    border-color: #ff7875;
}

/* 紧急呼叫按钮脉冲动画 */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 20px rgba(220, 53, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
    }
}

.emergency-button {
    animation: pulse 2s infinite;
}

/* 通知徽章闪烁动画 */
@keyframes blink {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.notification-badge {
    animation: blink 1s infinite;
}

/* 地图容器 */
.map-container {
    border: 1px solid #e8e8e8;
}

/* 表格样式 */
.table th {
    font-weight: 600;
    color: #333;
}

.table-hover tbody tr:hover {
    background-color: rgba(24, 144, 255, 0.1);
}

/* 健康卡片样式 */
.health-card {
    border-left: 4px solid #52c41a;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 30px;
    height: 30px;
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: #1890ff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 响应式调整 */
@media (max-width: 768px) {
    .card {
        margin-bottom: 15px;
    }
    
    .map-container {
        height: 300px !important;
    }
} 