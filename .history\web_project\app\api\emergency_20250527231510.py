from flask import Blueprint, request, jsonify, current_app
import uuid
from datetime import datetime
from app import db
from app.models.models import <PERSON><PERSON>all, EmergencyEvent, ElderlyUser, FamilyUser, CommunityWorker, Smartwatch

# 创建蓝图，注意命名空间不要与URL前缀相同
emergency_bp = Blueprint('emergency_api', __name__)

@emergency_bp.route('/call', methods=['POST'])
def emergency_call():
    """
    触发紧急呼叫，记录老年人的紧急求助
    接收来自前端的请求数据: elderly_id, gps_location
    """
    data = request.json
    elderly_id = data.get('elderly_id')
    gps_location = data.get('gps_location')

    if not elderly_id:
        return jsonify({'error': '缺少老年人ID'}), 400

    # 检查老年人ID是否存在
    elderly = ElderlyUser.query.filter_by(user_id=elderly_id).first()
    if not elderly:
        return jsonify({'error': '未找到该老年人用户'}), 404

    # 生成紧急呼叫ID (缩短ID长度，防止超出数据库限制)
    call_id = 'EC' + datetime.now().strftime('%m%d%H%M%S')

    # 如果没有提供GPS位置，尝试从智能手环获取
    if not gps_location and elderly.smartwatch_id:
        smartwatch = Smartwatch.query.filter_by(watch_id=elderly.smartwatch_id).first()
        if smartwatch:
            gps_location = smartwatch.gps_location

    # 创建紧急呼叫记录
    emergency_call = EmergencyCall(
        call_id=call_id,
        elderly_id=elderly_id,
        gps_location=gps_location,
        status='待处理'
    )

    try:
        db.session.add(emergency_call)
        db.session.commit()

        # TODO: 在这里添加通知家属和社区工作人员的代码
        # notify_family_and_workers(elderly_id, call_id)

        return jsonify({
            'success': True,
            'message': '紧急呼叫已记录',
            'call_id': call_id
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'保存紧急呼叫时出错: {str(e)}'}), 500

@emergency_bp.route('/calls/<elderly_id>', methods=['GET'])
def get_elderly_calls(elderly_id):
    """获取老年人的紧急呼叫历史记录"""
    try:
        calls = EmergencyCall.query.filter_by(elderly_id=elderly_id).order_by(EmergencyCall.call_time.desc()).all()

        result = []
        for call in calls:
            result.append({
                'call_id': call.call_id,
                'call_time': call.call_time.strftime('%Y-%m-%d %H:%M:%S'),
                'gps_location': call.gps_location,
                'status': call.status,
                'related_person_id': call.related_person_id
            })

        return jsonify({'calls': result}), 200
    except Exception as e:
        return jsonify({'error': f'获取紧急呼叫记录失败: {str(e)}'}), 500

@emergency_bp.route('/call/<call_id>/update', methods=['PUT'])
def update_call_status(call_id):
    """更新紧急呼叫的状态"""
    data = request.json
    new_status = data.get('status')
    related_person_id = data.get('related_person_id')

    if not new_status:
        return jsonify({'error': '缺少状态信息'}), 400

    try:
        call = EmergencyCall.query.filter_by(call_id=call_id).first()
        if not call:
            return jsonify({'error': '未找到该紧急呼叫记录'}), 404

        call.status = new_status
        if related_person_id:
            call.related_person_id = related_person_id

        db.session.commit()
        return jsonify({'success': True, 'message': '状态更新成功'}), 200
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'更新状态失败: {str(e)}'}), 500

@emergency_bp.route('/location/<elderly_id>', methods=['GET'])
def get_elderly_location(elderly_id):
    """获取老年人当前位置"""
    try:
        elderly = ElderlyUser.query.filter_by(user_id=elderly_id).first()
        if not elderly:
            return jsonify({'error': '未找到该老年人用户'}), 404

        if not elderly.smartwatch_id:
            return jsonify({'error': '该用户未绑定智能手环'}), 400

        smartwatch = Smartwatch.query.filter_by(watch_id=elderly.smartwatch_id).first()
        if not smartwatch:
            return jsonify({'error': '未找到该智能手环'}), 404

        return jsonify({
            'elderly_id': elderly_id,
            'name': elderly.name,
            'gps_location': smartwatch.gps_location,
            'last_sync_time': smartwatch.last_sync_time.strftime('%Y-%m-%d %H:%M:%S')
        }), 200
    except Exception as e:
        return jsonify({'error': f'获取位置信息失败: {str(e)}'}), 500

@emergency_bp.route('/elderly/locations', methods=['GET'])
def get_all_elderly_locations():
    """获取所有老年人的位置信息（用于社区工作人员地图显示）"""
    try:
        # 获取查询参数
        region = request.args.get('region', '')

        # 构建查询
        query = ElderlyUser.query
        if region:
            query = query.filter(ElderlyUser.region == region)

        elderly_users = query.all()

        locations = []
        for elderly in elderly_users:
            # 获取GPS位置信息
            gps_location = None
            if elderly.smartwatch_id:
                smartwatch = Smartwatch.query.filter_by(watch_id=elderly.smartwatch_id).first()
                if smartwatch and smartwatch.gps_location:
                    gps_location = smartwatch.gps_location

            # 如果没有GPS位置，尝试从地址解析坐标
            if not gps_location and elderly.address:
                # 这里可以调用地理编码API将地址转换为坐标
                # 暂时使用模拟数据
                gps_location = "39.9042,116.4074"  # 默认坐标

            locations.append({
                'elderly_id': elderly.user_id,
                'name': elderly.name,
                'address': elderly.address,
                'gps_location': gps_location,
                'region': elderly.region,
                'phone': elderly.phone,
                'emergency_contact_name': elderly.emergency_contact_name,
                'emergency_contact_phone': elderly.emergency_contact_phone
            })

        return jsonify({'locations': locations}), 200

    except Exception as e:
        return jsonify({'error': f'获取位置信息失败: {str(e)}'}), 500

# ===== 紧急事件相关API =====

@emergency_bp.route('/event/create', methods=['POST'])
def create_emergency_event():
    """
    创建紧急事件（新版本，支持家属和工作人员响应）
    接收数据: elderly_id, emergency_type, location, address
    """
    try:
        data = request.json
        elderly_id = data.get('elderly_id')
        emergency_type = data.get('emergency_type')  # medical, fall, help
        location = data.get('location')  # GPS坐标 "lat,lng"
        address = data.get('address', '')  # 地址描述

        print(f"🚨 创建紧急事件请求: elderly_id={elderly_id}, type={emergency_type}")

        if not elderly_id or not emergency_type:
            return jsonify({'error': '缺少必要参数'}), 400

        # 检查老年人是否存在
        elderly = ElderlyUser.query.filter_by(user_id=elderly_id).first()
        if not elderly:
            return jsonify({'error': '未找到该老年人用户'}), 404

        # 生成事件ID
        event_id = 'EE' + datetime.now().strftime('%Y%m%d%H%M%S') + str(uuid.uuid4())[:4]

        # 创建紧急事件
        emergency_event = EmergencyEvent(
            event_id=event_id,
            elderly_id=elderly_id,
            emergency_type=emergency_type,
            location=location,
            address=address,
            status='待处理',
            family_responded=False,
            worker_responded=False
        )

        db.session.add(emergency_event)
        db.session.commit()

        print(f"✅ 紧急事件创建成功: {event_id}")

        return jsonify({
            'success': True,
            'message': '紧急事件已创建',
            'event_id': event_id,
            'timestamp': emergency_event.created_at.isoformat()
        }), 201

    except Exception as e:
        db.session.rollback()
        print(f"❌ 创建紧急事件失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'创建紧急事件失败: {str(e)}'}), 500

@emergency_bp.route('/event/active', methods=['GET'])
def get_active_events():
    """
    获取活跃的紧急事件
    查询参数: user_type (family/worker), user_id
    """
    try:
        user_type = request.args.get('user_type')  # family 或 worker
        user_id = request.args.get('user_id')

        print(f"🔍 获取紧急事件请求: user_type={user_type}, user_id={user_id}")

        if not user_type or not user_id:
            print(f"❌ 缺少参数")
            return jsonify({'error': '缺少用户类型或用户ID'}), 400

        # 构建查询条件
        query = EmergencyEvent.query

        if user_type == 'family':
            # 家属可以看到绑定老人的所有事件
            print(f"🔍 查找家属用户: {user_id}")
            family_user = FamilyUser.query.filter_by(family_id=user_id).first()
            if not family_user:
                print(f"❌ 未找到家属用户: {user_id}")
                return jsonify({'error': '未找到家属用户'}), 404

            print(f"✅ 找到家属用户: {family_user.name}, 绑定老人ID: {family_user.bound_elderly_id}")
            query = query.filter(
                EmergencyEvent.elderly_id == family_user.bound_elderly_id
            )

        elif user_type == 'worker':
            # 工作人员可以看到所有事件
            print(f"🔍 工作人员查看所有事件")
            pass

        else:
            print(f"❌ 无效的用户类型: {user_type}")
            return jsonify({'error': '无效的用户类型'}), 400

        events = query.order_by(EmergencyEvent.created_at.desc()).all()
        print(f"📊 查询到 {len(events)} 个紧急事件")

        result = []
        for event in events:
            elderly = ElderlyUser.query.filter_by(user_id=event.elderly_id).first()
            event_data = {
                'event_id': event.event_id,
                'elderly_id': event.elderly_id,
                'elderly_name': elderly.name if elderly else '未知',
                'emergency_type': event.emergency_type,
                'location': event.location,
                'address': event.address,
                'created_at': event.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'status': event.status,
                'family_responded': event.family_responded,
                'worker_responded': event.worker_responded
            }
            result.append(event_data)
            print(f"📋 事件: {event.event_id} - {event.emergency_type} - {event.status}")

        print(f"✅ 返回 {len(result)} 个事件")
        return jsonify({'events': result}), 200

    except Exception as e:
        print(f"❌ 获取事件失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'获取事件失败: {str(e)}'}), 500

@emergency_bp.route('/event/respond', methods=['POST'])
def respond_to_event():
    """
    家属或工作人员响应紧急事件
    接收数据: event_id, user_type (family/worker), user_id, response_note
    """
    print(f"🚀 响应API被调用")
    try:
        data = request.json
        print(f"📥 接收到数据: {data}")

        event_id = data.get('event_id')
        user_type = data.get('user_type')  # family 或 worker
        user_id = data.get('user_id')
        response_note = data.get('response_note', '')

        print(f"📋 解析参数: event_id={event_id}, user_type={user_type}, user_id={user_id}")

        if not event_id or not user_type or not user_id:
            print(f"❌ 缺少必要参数")
            return jsonify({'error': '缺少必要参数'}), 400

        # 查找事件
        print(f"🔍 查找事件: {event_id}")
        event = EmergencyEvent.query.filter_by(event_id=event_id).first()
        if not event:
            print(f"❌ 未找到事件")
            return jsonify({'error': '未找到该紧急事件'}), 404

        print(f"✅ 找到事件: {event.event_id}, 当前状态: {event.status}")

        # 验证用户权限
        if user_type == 'family':
            family_user = FamilyUser.query.filter_by(family_id=user_id).first()
            if not family_user or family_user.bound_elderly_id != event.elderly_id:
                return jsonify({'error': '无权限响应此事件'}), 403

            if event.family_responded:
                return jsonify({'error': '家属已响应此事件'}), 400

            # 标记家属已响应
            event.family_responded = True
            event.family_responder_id = user_id
            event.family_response_time = datetime.now()
            event.family_response_note = response_note

        elif user_type == 'worker':
            worker = CommunityWorker.query.filter_by(worker_id=user_id).first()
            if not worker:
                return jsonify({'error': '未找到工作人员'}), 404

            if event.worker_responded:
                return jsonify({'error': '工作人员已响应此事件'}), 400

            # 标记工作人员已响应
            event.worker_responded = True
            event.worker_responder_id = user_id
            event.worker_response_time = datetime.now()
            event.worker_response_note = response_note

        else:
            return jsonify({'error': '无效的用户类型'}), 400

        # 更新事件状态
        if event.family_responded or event.worker_responded:
            old_status = event.status
            event.status = '已处理'
            print(f"   状态更新: {old_status} -> {event.status}")

        db.session.commit()
        print(f"   数据库提交后状态: {event.status}")

        return jsonify({
            'success': True,
            'message': f'{"家属" if user_type == "family" else "工作人员"}响应成功',
            'event_status': event.status
        }), 200

    except Exception as e:
        db.session.rollback()
        print(f"❌ 响应事件失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'响应事件失败: {str(e)}'}), 500