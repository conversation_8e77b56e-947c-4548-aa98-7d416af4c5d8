new Vue({
    el: '#monitor-app',
    delimiters: ['[[', ']]'],
    data: {
        currentMenu: '实时体征看板',
        currentUser: null,
        currentElderly: null,
        boundElderlyList: [],
        // 图表对象
        charts: {
            trendChart: null,
            activityRadar: null,
            contactGraph: null
        },
        // 地图对象
        map: null,
        // 当前位置信息
        currentLocationInfo: {
            name: '加载中...',
            address: '获取地址中...',
            lastUpdate: '',
            coordinates: ''
        },
        // 当前选中的老人索引
        selectedElderlyIndex: 0,
        // 紧急呼叫相关数据
        emergencyEvents: [],
        isLoadingEmergencyEvents: false,
        emergencyPollingInterval: null,
        lastEventCount: 0,
        hasShownEmergencyAlert: false
    },
    mounted() {
        // 获取当前用户信息和绑定的老人列表
        this.getCurrentUser();

        // 立即开始紧急事件轮询（不管当前在哪个页面）
        setTimeout(() => {
            console.log('🚀 开始初始化紧急事件监控...');
            this.loadEmergencyEvents();
            this.startEmergencyPolling();
        }, 3000); // 等待3秒让用户信息加载完成

        // 添加watch监听当前菜单变化，以便在菜单切换时初始化对应的图表
        this.$watch('currentMenu', (newMenu, oldMenu) => {
            // 在组件更新后初始化相应的图表
            this.$nextTick(() => {
                switch(newMenu) {
                    case '历史趋势分析':
                        this.initTrendChart();
                        break;
                    case '活动行为分析':
                        this.initActivityCharts();
                        break;
                    case '实时定位监控':
                        this.initMap();
                        break;
                    case '应急响应中心':
                        this.loadEmergencyEvents();
                        break;
                }
            });
        });

        // 页面卸载时清理轮询
        window.addEventListener('beforeunload', () => {
            this.stopEmergencyPolling();
        });
    },
    methods: {
        selectMenu(menu) {
            this.currentMenu = menu;
        },
        getCurrentUser() {
            axios.get('/api/auth/current_user')
                .then(response => {
                    console.log('🔍 当前用户认证响应:', response.data);
                    if (response.data.logged_in) {
                        this.currentUser = {
                            id: response.data.user_id,
                            name: response.data.name,
                            type: response.data.user_type
                        };
                        console.log('✅ 当前登录用户:', this.currentUser);

                        // 检查用户类型
                        if (this.currentUser.type !== 'family') {
                            console.error('❌ 错误：当前登录的不是家属账号！');
                            console.error('   当前用户类型:', this.currentUser.type);
                            console.error('   当前用户ID:', this.currentUser.id);
                            console.error('   请使用家属账号（F01, F02等）登录！');
                            alert('错误：请使用家属账号登录！\n当前登录的是：' + this.currentUser.type + ' 账号\n请登录F01、F02等家属账号');
                            return;
                        }

                        this.getBoundElderlyList();
                    } else {
                        console.warn('⚠️ 用户未登录');
                        alert('请先登录家属账号（如F01、F02等）');
                    }
                })
                .catch(error => {
                    console.error('获取用户信息失败:', error);
                });
        },
        getBoundElderlyList() {
            axios.get(`/api/family/elderly/${this.currentUser.id}`)
                .then(response => {
                    if (response.data.elderly_list) {
                        this.boundElderlyList = response.data.elderly_list;
                        // 设置当前选中的老人为主绑定老人或第一个老人
                        const primaryElderly = this.boundElderlyList.find(e => e.is_primary);
                        if (primaryElderly) {
                            this.currentElderly = primaryElderly;
                            this.selectedElderlyIndex = this.boundElderlyList.findIndex(e => e.is_primary);
                        } else if (this.boundElderlyList.length > 0) {
                            this.currentElderly = this.boundElderlyList[0];
                            this.selectedElderlyIndex = 0;
                        }

                        if (this.currentElderly) {
                            this.loadElderlyLocationInfo(this.currentElderly.user_id);
                        }
                    }
                })
                .catch(error => {
                    console.error('获取绑定老人列表失败:', error);
                });
        },
        loadElderlyData(elderlyId) {
            // 加载该老人的数据，更新页面内容
            console.log(`加载老人ID ${elderlyId} 的数据`);

            // 根据当前选项卡初始化相应的图表
            this.$nextTick(() => {
                switch(this.currentMenu) {
                    case '历史趋势分析':
                        this.initTrendChart();
                        break;
                    case '活动行为分析':
                        this.initActivityCharts();
                        break;
                    case '实时定位监控':
                        this.initMap();
                        break;
                }
            });
        },

        // 初始化历史趋势图表
        initTrendChart() {
            if (document.getElementById('trendChart')) {
                // 清除旧图表
                if (this.charts.trendChart) {
                    this.charts.trendChart.destroy();
                }

                const ctx = document.getElementById('trendChart').getContext('2d');
                this.charts.trendChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                        datasets: [
                            {
                                label: '收缩压',
                                data: [145, 142, 138, 146, 150, 142, 140],
                                borderColor: '#1765d5',
                                backgroundColor: 'rgba(23, 101, 213, 0.1)',
                                borderWidth: 2,
                                fill: true,
                                tension: 0.3
                            },
                            {
                                label: '舒张压',
                                data: [92, 88, 85, 90, 93, 87, 85],
                                borderColor: '#5B8FF9',
                                backgroundColor: 'rgba(91, 143, 249, 0.1)',
                                borderWidth: 2,
                                fill: true,
                                tension: 0.3
                            },
                            {
                                label: '空腹血糖',
                                data: [7.2, 7.4, 7.1, 7.6, 7.8, 7.5, 7.3],
                                borderColor: '#ff7875',
                                backgroundColor: 'rgba(255, 120, 117, 0.1)',
                                borderWidth: 2,
                                fill: true,
                                tension: 0.3,
                                yAxisID: 'y1'
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                title: {
                                    display: true,
                                    text: '血压 (mmHg)'
                                }
                            },
                            y1: {
                                position: 'right',
                                title: {
                                    display: true,
                                    text: '血糖 (mmol/L)'
                                },
                                grid: {
                                    drawOnChartArea: false
                                }
                            }
                        },
                        plugins: {
                            title: {
                                display: true,
                                text: '近7天健康指标趋势',
                                font: {
                                    size: 16
                                }
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false
                            },
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }
        },

        // 初始化活动行为分析图表
        initActivityCharts() {
            // 初始化活动雷达图
            if (document.getElementById('activityRadar')) {
                if (this.charts.activityRadar) {
                    this.charts.activityRadar.destroy();
                }

                const radarCtx = document.getElementById('activityRadar').getContext('2d');
                this.charts.activityRadar = new Chart(radarCtx, {
                    type: 'radar',
                    data: {
                        labels: ['家人聚会', '社区活动', '健康锻炼', '医疗就诊', '生活自理', '朋友交往'],
                        datasets: [{
                            label: '参与度评分',
                            data: [85, 40, 55, 75, 90, 45],
                            backgroundColor: 'rgba(23, 101, 213, 0.2)',
                            borderColor: '#1765d5',
                            pointBackgroundColor: '#1765d5',
                            pointBorderColor: '#fff',
                            pointHoverBackgroundColor: '#fff',
                            pointHoverBorderColor: '#1765d5'
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: '老人活动参与度雷达图',
                                font: {
                                    size: 16
                                }
                            }
                        },
                        scales: {
                            r: {
                                angleLines: {
                                    display: true
                                },
                                min: 0,
                                max: 100
                            }
                        }
                    }
                });
            }

            // 初始化社交关系图
            if (document.getElementById('contactGraph')) {
                if (this.charts.contactGraph) {
                    this.charts.contactGraph.destroy();
                }

                const graphCtx = document.getElementById('contactGraph').getContext('2d');
                this.charts.contactGraph = new Chart(graphCtx, {
                    type: 'polarArea',
                    data: {
                        labels: ['家庭成员', '邻居', '社区医生', '朋友', '社区工作者'],
                        datasets: [{
                            label: '互动频率',
                            data: [80, 45, 25, 35, 60],
                            backgroundColor: [
                                'rgba(23, 101, 213, 0.7)',
                                'rgba(91, 143, 249, 0.7)',
                                'rgba(96, 183, 96, 0.7)',
                                'rgba(250, 173, 20, 0.7)',
                                'rgba(255, 120, 117, 0.7)'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: '老人社交互动频率分析',
                                font: {
                                    size: 16
                                }
                            },
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }
        },

        // 初始化地图
        async initMap() {
            // 确保地图容器存在
            if (document.getElementById('amapRealtime')) {
                // 创建地图实例
                if (!this.map) {
                    this.map = new AMap.Map('amapRealtime', {
                        zoom: 14,
                        center: [120.1626, 30.2729]  // 默认杭州位置
                    });

                    // 添加工具条控件
                    this.map.plugin(["AMap.ToolBar"], function() {
                        const toolBar = new AMap.ToolBar();
                        this.map.addControl(toolBar);
                    });
                }

                // 加载老人的实时位置
                await this.loadElderlyLocation();
            }
        },

        async loadElderlyLocation() {
            try {
                // 使用当前选中的老人
                if (!this.currentElderly) {
                    console.warn('未选择老人');
                    this.showDefaultLocation();
                    return;
                }

                const response = await axios.get(`/api/family/elderly/${this.currentUser.id}/location/${this.currentElderly.user_id}`);

                if (response.data && response.data.coordinates) {
                    this.updateMapWithElderlyLocation(response.data);
                } else {
                    console.warn('未获取到老人位置信息');
                    this.showDefaultLocation();
                }
            } catch (error) {
                console.error('加载老人位置失败:', error);
                this.showDefaultLocation();
            }
        },

        showDefaultLocation() {
            // 显示默认位置
            const defaultLng = 120.1626;
            const defaultLat = 30.2729;

            this.map.setCenter([defaultLng, defaultLat]);

            const marker = new AMap.Marker({
                position: [defaultLng, defaultLat],
                title: '默认位置',
                icon: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png'
            });

            this.map.add(marker);

            // 更新位置信息显示
            this.currentLocationInfo = {
                name: '位置信息获取中...',
                address: '杭州市西湖区（默认位置）',
                lastUpdate: new Date().toLocaleString(),
                coordinates: `${defaultLat}, ${defaultLng}`
            };
        },

        getAddressFromCoordinates(lng, lat, elderlyName) {
            // 使用高德地图API进行逆地理编码
            if (typeof AMap !== 'undefined') {
                AMap.plugin('AMap.Geocoder', () => {
                    const geocoder = new AMap.Geocoder();
                    geocoder.getAddress([lng, lat], (status, result) => {
                        if (status === 'complete' && result.info === 'OK') {
                            const address = result.regeocode.formattedAddress;

                            // 更新位置信息
                            this.currentLocationInfo = {
                                name: elderlyName,
                                address: address,
                                lastUpdate: new Date().toLocaleString(),
                                coordinates: `${lat}, ${lng}`
                            };

                            // 添加信息窗口
                            const infoWindow = new AMap.InfoWindow({
                                content: `
                                    <div style="padding: 10px;">
                                        <h6>${elderlyName}的位置</h6>
                                        <p><strong>地址:</strong> ${address}</p>
                                        <p><strong>坐标:</strong> ${lat}, ${lng}</p>
                                        <p><strong>更新时间:</strong> ${new Date().toLocaleString()}</p>
                                    </div>
                                `,
                                offset: new AMap.Pixel(0, -30)
                            });

                            // 点击标记显示信息窗口
                            const markers = this.map.getAllOverlays('marker');
                            if (markers.length > 0) {
                                markers[0].on('click', () => {
                                    infoWindow.open(this.map, markers[0].getPosition());
                                });
                            }
                        } else {
                            console.error('地址解析失败');
                            this.currentLocationInfo = {
                                name: elderlyName,
                                address: '地址解析失败',
                                lastUpdate: new Date().toLocaleString(),
                                coordinates: `${lat}, ${lng}`
                            };
                        }
                    });
                });
            }
        },

        updateLocationInfo(locationData) {
            // 更新位置信息显示
            this.currentLocationInfo = {
                name: locationData.name,
                address: '正在获取地址...',
                lastUpdate: locationData.last_sync_time || new Date().toLocaleString(),
                coordinates: locationData.gps_location
            };
        },

        // 切换老人
        switchElderly() {
            if (this.boundElderlyList.length > 0) {
                this.currentElderly = this.boundElderlyList[this.selectedElderlyIndex];
                this.loadElderlyLocationInfo(this.currentElderly.user_id);

                // 如果当前在位置监控页面，更新地图
                if (this.currentMenu === '实时定位监控') {
                    this.initMap();
                }
            }
        },

        // 加载老人位置信息
        loadElderlyLocationInfo(elderlyId) {
            if (!this.currentUser || !elderlyId) return;

            axios.get(`/api/family/elderly/${this.currentUser.id}/location/${elderlyId}`)
                .then(response => {
                    const data = response.data;
                    this.currentLocationInfo = {
                        name: data.name,
                        address: data.address,
                        lastUpdate: data.last_sync_time || '位置信息暂未更新',
                        coordinates: data.coordinates || '暂无坐标信息'
                    };

                    // 如果当前在位置监控页面，更新地图
                    if (this.currentMenu === '实时定位监控' && this.map) {
                        this.updateMapWithElderlyLocation(data);
                    }
                })
                .catch(error => {
                    console.error('获取老人位置信息失败:', error);
                    this.currentLocationInfo = {
                        name: this.currentElderly ? this.currentElderly.name : '未知',
                        address: '位置信息获取失败',
                        lastUpdate: '获取失败',
                        coordinates: '暂无坐标信息'
                    };
                });
        },

        // 更新地图显示老人位置
        updateMapWithElderlyLocation(locationData) {
            if (!this.map) return;

            let lng, lat;

            // 解析坐标
            if (locationData.coordinates && locationData.coordinates.includes(',')) {
                [lng, lat] = locationData.coordinates.split(',').map(Number);
            } else {
                // 使用默认坐标（杭州市中心）
                lng = 120.1626;
                lat = 30.2729;
            }

            // 清除现有标记
            this.map.clearMap();

            // 更新地图中心
            this.map.setCenter([lng, lat]);

            // 添加老人位置标记
            const marker = new AMap.Marker({
                position: [lng, lat],
                title: `${locationData.name}的当前位置`,
                icon: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png'
            });

            this.map.add(marker);

            // 添加安全围栏
            const circle = new AMap.Circle({
                center: [lng, lat],
                radius: 500,  // 500米安全围栏
                fillColor: 'rgba(23, 101, 213, 0.1)',
                strokeColor: '#1765d5',
                strokeWeight: 2
            });

            this.map.add(circle);

            // 添加信息窗口
            const infoWindow = new AMap.InfoWindow({
                content: `
                    <div style="padding: 10px;">
                        <h6>${locationData.name}的位置</h6>
                        <p><strong>地址:</strong> ${locationData.address}</p>
                        <p><strong>坐标:</strong> ${lat}, ${lng}</p>
                        <p><strong>更新时间:</strong> ${locationData.last_sync_time || '暂未更新'}</p>
                    </div>
                `,
                offset: new AMap.Pixel(0, -30)
            });

            // 点击标记显示信息窗口
            marker.on('click', () => {
                infoWindow.open(this.map, marker.getPosition());
            });
        },

        refreshLocation() {
            // 刷新位置信息
            if (this.currentElderly) {
                this.loadElderlyLocationInfo(this.currentElderly.user_id);
            }
        },

        // 紧急事件相关方法
        async testEmergencyAPI() {
            console.log('🧪 测试紧急事件API');
            try {
                const response = await axios.get('/api/emergency/event/test');
                console.log('测试API响应:', response.data);
                alert('API测试成功！\n' + JSON.stringify(response.data, null, 2));
            } catch (error) {
                console.error('API测试失败:', error);
                // 如果API失败，使用模拟数据测试弹窗功能
                console.log('🔄 API失败，使用模拟数据测试弹窗功能');
                this.testEmergencyAlert();
            }
        },

        testEmergencyAlert() {
            console.log('🧪 测试紧急事件弹窗');

            // 创建模拟紧急事件
            const mockEvent = {
                event_id: 'TEST_' + Date.now(),
                elderly_id: 'E01',
                elderly_name: '张建国',
                emergency_type: 'medical',
                location: '30.2741,120.1551',
                address: '杭州市西湖区西溪路518号',
                created_at: new Date().toLocaleString('zh-CN'),
                status: '待处理',
                family_responded: false,
                worker_responded: false
            };

            // 添加到事件列表
            this.emergencyEvents.unshift(mockEvent);

            // 显示弹窗
            this.showEmergencyAlert(mockEvent);

            alert('模拟紧急事件已创建！请查看弹窗通知。');
        },

        async loadEmergencyEvents() {
            if (!this.currentUser) return;

            this.isLoadingEmergencyEvents = true;
            try {
                const response = await axios.get('/api/emergency/event/active', {
                    params: {
                        user_type: 'family',
                        user_id: this.currentUser.id
                    }
                });

                if (response.data && response.data.events) {
                    const newEvents = response.data.events;

                    // 检查是否有新的紧急事件
                    const pendingEvents = newEvents.filter(event => event.status === '待处理');
                    const newPendingCount = pendingEvents.length;
                    const oldPendingCount = this.lastEventCount;

                    console.log(`🔍 紧急事件检查: 新事件${newPendingCount}个, 旧事件${oldPendingCount}个`);

                    // 修复弹窗逻辑：如果有新的待处理事件，且数量增加了，就显示弹窗
                    if (newPendingCount > 0 && newPendingCount > oldPendingCount) {
                        console.log('🚨 检测到新的紧急事件，显示弹窗');
                        // 找到最新的事件（按创建时间排序）
                        const latestEvent = pendingEvents.sort((a, b) =>
                            new Date(b.created_at) - new Date(a.created_at)
                        )[0];
                        this.showEmergencyAlert(latestEvent);
                    }

                    // 更新事件列表和计数
                    this.emergencyEvents = newEvents;
                    this.lastEventCount = newPendingCount;
                    console.log('✅ 加载紧急事件成功:', this.emergencyEvents);

                    // 如果当前在应急响应中心页面，更新页面标题提示
                    if (this.currentMenu === '应急响应中心' && newPendingCount > 0) {
                        document.title = `(${newPendingCount}) 紧急事件 - 家属中心`;
                    } else if (this.currentMenu === '应急响应中心') {
                        document.title = '家属中心';
                    }
                }
            } catch (error) {
                console.error('加载紧急事件失败:', error);
            } finally {
                this.isLoadingEmergencyEvents = false;
            }
        },

        async respondToEmergencyEvent(eventId, responseNote = '') {
            if (!this.currentUser) return;

            try {
                const response = await axios.post('/api/emergency/event/respond', {
                    event_id: eventId,
                    user_type: 'family',
                    user_id: this.currentUser.id,
                    response_note: responseNote
                });

                if (response.data && response.data.success) {
                    alert('响应成功！');
                    // 重新加载紧急事件列表
                    await this.loadEmergencyEvents();
                } else {
                    alert('响应失败：' + (response.data.error || '未知错误'));
                }
            } catch (error) {
                console.error('响应紧急事件失败:', error);
                alert('响应失败：' + (error.response?.data?.error || '网络错误'));
            }
        },

        startEmergencyPolling() {
            // 停止现有轮询
            this.stopEmergencyPolling();

            // 每5秒检查一次新的紧急事件（更频繁的检查）
            this.emergencyPollingInterval = setInterval(() => {
                console.log('⏰ 定时检查紧急事件...');
                this.loadEmergencyEvents();
            }, 5000);

            console.log('🔄 紧急事件轮询已启动，每5秒检查一次');
        },

        stopEmergencyPolling() {
            if (this.emergencyPollingInterval) {
                clearInterval(this.emergencyPollingInterval);
                this.emergencyPollingInterval = null;
            }
        },

        getEmergencyTypeText(type) {
            const typeMap = {
                'medical': '医疗紧急',
                'fall': '跌倒求助',
                'help': '其他求助'
            };
            return typeMap[type] || type;
        },

        getEmergencyTypeBadgeClass(type) {
            const classMap = {
                'medical': 'bg-danger',
                'fall': 'bg-warning',
                'help': 'bg-info'
            };
            return classMap[type] || 'bg-secondary';
        },

        formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '';
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN');
        },

        showEventDetails(event) {
            // 显示事件详情
            const details = `
紧急事件详情：
- 事件ID：${event.event_id}
- 老人姓名：${event.elderly_name}
- 事件类型：${this.getEmergencyTypeText(event.emergency_type)}
- 发生时间：${this.formatDateTime(event.created_at)}
- 位置信息：${event.address || '暂无'}
- 当前状态：${event.status}
- 家属响应：${event.family_responded ? '已响应' : '未响应'}
- 工作人员响应：${event.worker_responded ? '已响应' : '未响应'}
            `;
            alert(details);
        },

        showEmergencyAlert(event) {
            // 播放提示音（如果浏览器支持）
            try {
                const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                audio.play().catch(() => {}); // 忽略播放失败
            } catch (e) {}

            // 显示紧急事件弹窗
            const alertMessage = `🚨 紧急呼叫通知！

老人姓名：${event.elderly_name}
事件类型：${this.getEmergencyTypeText(event.emergency_type)}
发生时间：${this.formatDateTime(event.created_at)}
位置信息：${event.address || '位置信息暂无'}

请立即查看应急响应中心并进行处理！`;

            if (confirm(alertMessage + '\n\n点击"确定"前往应急响应中心，点击"取消"稍后处理')) {
                // 切换到应急响应中心
                this.currentMenu = '应急响应中心';
            }

            // 发送浏览器通知（如果用户已授权）
            if ('Notification' in window && Notification.permission === 'granted') {
                new Notification('🚨 紧急呼叫通知', {
                    body: `${event.elderly_name} 发起了${this.getEmergencyTypeText(event.emergency_type)}`,
                    icon: '/static/images/emergency-icon.png',
                    requireInteraction: true
                });
            } else if ('Notification' in window && Notification.permission !== 'denied') {
                // 请求通知权限
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        new Notification('🚨 紧急呼叫通知', {
                            body: `${event.elderly_name} 发起了${this.getEmergencyTypeText(event.emergency_type)}`,
                            icon: '/static/images/emergency-icon.png',
                            requireInteraction: true
                        });
                    }
                });
            }
        }
    }
});