const foodOrderModule = {
    data: {
        cart: {
            items: [],
            totalPrice: 0
        },
        currentFood: null,
        foodDetailModalInstance: null,
        paymentModalInstance: null,
        orderStatusModalInstance: null,
        currentOrderId: null,
        orderStatusTimer: null,
        foodList: [
            {
                id: 1,
                name: "清蒸鲈鱼",
                price: 28,
                image: "/static/images/fish.png",
                tags: ["高蛋白", "低脂"],
                info: "适合高血压人群",
                badge: "医生推荐",
                badgeClass: "bg-success",
                nutrition: {
                    protein: 20,
                    fat: 5,
                    carbs: 10,
                    salt: 2
                },
                benefits: [
                    "富含DHA，有助于心脑血管健康",
                    "清蒸烹饪法，低脂健康",
                    "优质蛋白质来源，易于消化吸收"
                ]
            },
            {
                id: 2,
                name: "五谷杂粮粥",
                price: 15,
                image: "/static/images/wugu.png",
                tags: ["高纤维", "低糖"],
                info: "促进肠道健康",
                badge: "营养均衡",
                badgeClass: "bg-primary",
                nutrition: {
                    protein: 8,
                    fat: 2,
                    carbs: 30,
                    salt: 1
                },
                benefits: [
                    "富含膳食纤维，促进肠道蠕动",
                    "多种谷物营养均衡",
                    "低糖设计，适合血糖控制"
                ]
            },
            {
                id: 3,
                name: "西兰花炒牛肉",
                price: 25,
                image: "/static/images/beaf.png",
                tags: ["高蛋白", "维生素"],
                info: "增强免疫力",
                nutrition: {
                    protein: 18,
                    fat: 8,
                    carbs: 15,
                    salt: 2.5
                },
                benefits: [
                    "牛肉富含优质蛋白和铁质",
                    "西兰花含丰富维生素C和抗氧化物",
                    "搭配科学，营养均衡"
                ]
            },
            {
                id: 4,
                name: "三文鱼沙拉",
                price: 32,
                image: "/static/images/sala.png",
                tags: ["优质脂肪", "维生素"],
                info: "心脑血管健康",
                nutrition: {
                    protein: 15,
                    fat: 12,
                    carbs: 10,
                    salt: 1.5
                },
                benefits: [
                    "富含优质Omega-3脂肪酸",
                    "新鲜蔬菜提供充足维生素",
                    "有助于降低胆固醇，保护心血管健康"
                ]
            },
            {
                id: 5,
                name: "山药排骨汤",
                price: 36,
                image: "/static/images/shanyao.png",
                tags: ["滋补", "易消化"],
                info: "健脾益胃",
                badge: "老年养生",
                badgeClass: "bg-warning",
                nutrition: {
                    protein: 18,
                    fat: 10,
                    carbs: 25,
                    salt: 2.5
                },
                benefits: [
                    "含丰富的维生素和矿物质",
                    "山药易于消化吸收，养胃健脾",
                    "排骨提供丰富蛋白质和钙质"
                ]
            },
            {
                id: 6,
                name: "清蒸南瓜",
                price: 18,
                image: "/static/images/nangua.png",
                tags: ["高纤维", "低糖"],
                info: "适合糖尿病人",
                badge: "低糖食谱",
                badgeClass: "bg-info",
                nutrition: {
                    protein: 5,
                    fat: 1,
                    carbs: 20,
                    salt: 0.5
                },
                benefits: [
                    "低糖低脂，适合糖尿病患者",
                    "富含胡萝卜素和膳食纤维",
                    "帮助控制血糖，促进肠道健康"
                ]
            }
        ]
    },
    methods: {
        addToCart(foodId) {
            const food = this.foodList.find(f => f.id === foodId);
            if (!food) return;

            const existingItem = this.cart.items.find(item => item.id === foodId);

            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                this.cart.items.push({
                    id: food.id,
                    name: food.name,
                    price: food.price,
                    image: food.image,
                    quantity: 1,
                    tags: food.tags
                });
            }
            this.updateCartTotal();
            this.showFoodDetail(foodId);
        },
        updateCartTotal() {
            this.cart.totalPrice = this.cart.items.reduce((total, item) => {
                return total + (item.price * item.quantity);
            }, 0);
        },
        showFoodDetail(foodId) {
            this.currentFood = this.foodList.find(f => f.id === foodId);
            if (this.foodDetailModalInstance) {
                this.foodDetailModalInstance.show();
            } else {
                console.error("foodDetailModalInstance not initialized. Modal cannot be shown.");
            }
        },
        increaseQuantity(itemId) {
            const item = this.cart.items.find(i => i.id === itemId);
            if (item) {
                item.quantity += 1;
                this.updateCartTotal();
            }
        },
        decreaseQuantity(itemId) {
            const item = this.cart.items.find(i => i.id === itemId);
            if (item && item.quantity > 1) {
                item.quantity -= 1;
                this.updateCartTotal();
            } else if (item && item.quantity === 1) {
                this.removeFromCart(itemId);
            }
        },
        removeFromCart(itemId) {
            const index = this.cart.items.findIndex(i => i.id === itemId);
            if (index !== -1) {
                this.cart.items.splice(index, 1);
                this.updateCartTotal();
            }
        },
        checkout() {
            if (this.paymentModalInstance) {
                this.paymentModalInstance.show();
            } else {
                console.error("paymentModalInstance not initialized. Payment modal cannot be shown.");
            }
            if (this.foodDetailModalInstance) {
                this.foodDetailModalInstance.hide();
            }
        },
        async confirmPayment() {
            try {
                // 收集订单信息
                const orderData = {
                    user_id: 'elderly_001', // 模拟用户ID，实际应从登录信息获取
                    items: this.cart.items.map(item => ({
                        id: item.id,
                        name: item.name,
                        price: item.price,
                        quantity: item.quantity,
                        tags: item.tags
                    })),
                    total_price: this.cart.totalPrice,
                    delivery_fee: 5,
                    delivery_time: this.getSelectedDeliveryTime(),
                    address: this.getDeliveryAddress(),
                    payment_method: '子女代付',
                    special_notes: this.getSpecialNotes()
                };

                // 发送订单到后端
                const response = await fetch('/api/food_orders/submit', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(orderData)
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    // 保存当前订单ID
                    this.currentOrderId = result.order_id;

                    // 显示订单状态跟踪模态窗口
                    this.showOrderStatusModal(result.order);

                    // 清空购物车
                    this.cart.items = [];
                    this.updateCartTotal();

                    // 关闭支付模态框
                    if (this.paymentModalInstance) {
                        this.paymentModalInstance.hide();
                    }

                    console.log('订单提交成功:', result);
                } else {
                    throw new Error(result.error || '订单提交失败');
                }

            } catch (error) {
                console.error('订单提交失败:', error);
                alert(`订单提交失败: ${error.message}\n请稍后重试或联系客服。`);
            }
        },

        // 获取选中的配送时间
        getSelectedDeliveryTime() {
            const timeSelect = document.querySelector('#paymentModal select');
            if (timeSelect && timeSelect.value) {
                return timeSelect.options[timeSelect.selectedIndex].text;
            }
            return '11:30-12:00'; // 默认时间
        },

        // 获取配送地址
        getDeliveryAddress() {
            // 这里可以从用户设置或表单中获取，暂时使用默认地址
            return '浙江省杭州市余杭区龙湖天街1号';
        },

        // 获取特殊备注
        getSpecialNotes() {
            // 这里可以从表单中获取用户输入的备注
            return '请轻敲门，老人听力不太好';
        },

        // 显示订单状态跟踪模态窗口
        showOrderStatusModal(order) {
            // 填充订单基本信息
            document.getElementById('orderNumber').textContent = order.order_id;
            document.getElementById('submitTime').textContent = this.formatTime(new Date());

            // 填充订单详情
            this.populateOrderDetails(order);

            // 显示模态窗口
            if (this.orderStatusModalInstance) {
                this.orderStatusModalInstance.show();
            }

            // 开始轮询订单状态
            this.startOrderStatusPolling();
        },

        // 填充订单详情
        populateOrderDetails(order) {
            const orderItemsList = document.getElementById('orderItemsList');
            const orderTotal = document.getElementById('orderTotal');

            // 清空现有内容
            orderItemsList.innerHTML = '';

            // 添加订单项目
            order.items.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item.name} x${item.quantity}</td>
                    <td class="text-end">¥${(item.price * item.quantity).toFixed(2)}</td>
                `;
                orderItemsList.appendChild(row);
            });

            // 添加配送费
            const deliveryRow = document.createElement('tr');
            deliveryRow.innerHTML = `
                <td>配送费</td>
                <td class="text-end">¥${order.delivery_fee.toFixed(2)}</td>
            `;
            orderItemsList.appendChild(deliveryRow);

            // 设置总价
            orderTotal.textContent = `¥${order.final_price.toFixed(2)}`;
        },

        // 开始轮询订单状态
        startOrderStatusPolling() {
            // 清除现有定时器
            if (this.orderStatusTimer) {
                clearInterval(this.orderStatusTimer);
            }

            // 每5秒检查一次订单状态
            this.orderStatusTimer = setInterval(() => {
                this.checkOrderStatus();
            }, 5000);
        },

        // 检查订单状态
        async checkOrderStatus() {
            if (!this.currentOrderId) return;

            try {
                const response = await fetch(`/api/food_orders/status/${this.currentOrderId}`);
                const result = await response.json();

                if (response.ok && result.success) {
                    this.updateOrderProgress(result.order);
                }
            } catch (error) {
                console.error('检查订单状态失败:', error);
            }
        },

        // 更新订单进度
        updateOrderProgress(order) {
            const steps = ['step1', 'step2', 'step3', 'step4', 'step5'];

            // 重置所有步骤
            steps.forEach(stepId => {
                const step = document.getElementById(stepId);
                step.classList.remove('active', 'completed');
            });

            // 根据订单状态更新进度
            switch(order.status) {
                case 'pending_assignment':
                    this.activateStep('step1');
                    break;
                case 'assigned':
                    this.completeStep('step1');
                    this.activateStep('step2');
                    this.updateDeliveryInfo(order);
                    // 模拟配送过程（15秒后自动变为配送中）
                    setTimeout(() => {
                        this.simulateDelivery();
                    }, 15000);
                    break;
                case 'delivering':
                    this.completeStep('step1');
                    this.completeStep('step2');
                    this.activateStep('step3');
                    this.updateDeliveryProgress();
                    // 模拟送达（10秒后自动变为已送达）
                    setTimeout(() => {
                        this.simulateArrival();
                    }, 10000);
                    break;
                case 'delivered':
                    this.completeStep('step1');
                    this.completeStep('step2');
                    this.completeStep('step3');
                    this.activateStep('step4');
                    this.showConfirmSection();
                    break;
                case 'completed':
                    steps.forEach(stepId => {
                        this.completeStep(stepId);
                    });
                    this.hideConfirmSection();
                    // 停止轮询
                    if (this.orderStatusTimer) {
                        clearInterval(this.orderStatusTimer);
                        this.orderStatusTimer = null;
                    }
                    break;
            }
        },

        // 激活步骤
        activateStep(stepId) {
            const step = document.getElementById(stepId);
            step.classList.add('active');
        },

        // 完成步骤
        completeStep(stepId) {
            const step = document.getElementById(stepId);
            step.classList.remove('active');
            step.classList.add('completed');
        },

        // 更新配送信息
        updateDeliveryInfo(order) {
            const deliveryInfo = document.getElementById('deliveryInfo');
            const assignTime = document.getElementById('assignTime');

            deliveryInfo.textContent = `配送员：${order.assigned_staff_name}，预计15-25分钟内到达`;
            assignTime.textContent = this.formatTime(new Date());
        },

        // 更新配送进度
        updateDeliveryProgress() {
            const deliveryProgress = document.getElementById('deliveryProgress');
            const deliveryTime = document.getElementById('deliveryTime');

            deliveryProgress.textContent = '配送员正在路上，预计10分钟内到达';
            deliveryTime.textContent = this.formatTime(new Date());
        },

        // 显示确认收货按钮
        showConfirmSection() {
            const confirmSection = document.getElementById('confirmSection');
            const arrivalTime = document.getElementById('arrivalTime');

            confirmSection.style.display = 'block';
            arrivalTime.textContent = this.formatTime(new Date());
        },

        // 隐藏确认收货按钮
        hideConfirmSection() {
            const confirmSection = document.getElementById('confirmSection');
            const completeTime = document.getElementById('completeTime');

            confirmSection.style.display = 'none';
            completeTime.textContent = this.formatTime(new Date());
        },

        // 模拟配送过程
        async simulateDelivery() {
            if (!this.currentOrderId) return;

            try {
                await fetch(`/api/food_orders/simulate_delivery/${this.currentOrderId}`, {
                    method: 'POST'
                });
            } catch (error) {
                console.error('模拟配送失败:', error);
            }
        },

        // 模拟送达
        async simulateArrival() {
            if (!this.currentOrderId) return;

            try {
                await fetch(`/api/food_orders/simulate_arrival/${this.currentOrderId}`, {
                    method: 'POST'
                });
            } catch (error) {
                console.error('模拟送达失败:', error);
            }
        },

        // 格式化时间
        formatTime(date) {
            return date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }
    },
    initFoodOrderingFeatures(vueInstance) {
        // Modal ainstantiation for food ordering was previously done within the changeTab method.
        // We will ensure they are initialized if the services tab is active,
        // or pre-initialize them here if they are always needed.
        // For now, let's assume they are always needed for simplicity,
        // consistent with emergencyModalInstance.
        const foodDetailModalEl = document.getElementById('foodDetailModal');
        if (foodDetailModalEl) {
            vueInstance.foodDetailModalInstance = new bootstrap.Modal(foodDetailModalEl);
        } else {
            // console.error is not strictly necessary here as changeTab will also check.
        }

        const paymentModalEl = document.getElementById('paymentModal');
        if (paymentModalEl) {
            vueInstance.paymentModalInstance = new bootstrap.Modal(paymentModalEl);
        } else {
            // console.error is not strictly necessary here as changeTab will also check.
        }

        const orderStatusModalEl = document.getElementById('orderStatusModal');
        if (orderStatusModalEl) {
            vueInstance.orderStatusModalInstance = new bootstrap.Modal(orderStatusModalEl);
        }
    }
};