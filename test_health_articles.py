#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试健康科普API的脚本
"""

import requests
import json

def test_health_articles_api():
    """测试健康科普文章API"""
    base_url = "http://127.0.0.1:5000"
    
    print("=== 测试健康科普API ===")
    
    # 测试获取所有文章
    print("\n1. 测试获取所有文章...")
    try:
        response = requests.get(f"{base_url}/api/health/articles")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"返回数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 测试按分类筛选
    print("\n2. 测试按分类筛选...")
    try:
        response = requests.get(f"{base_url}/api/health/articles?category=营养膳食")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"营养膳食分类文章数量: {len(data.get('articles', []))}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 测试API连通性
    print("\n3. 测试API连通性...")
    try:
        response = requests.get(f"{base_url}/api/health/test")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"测试响应: {data}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    test_health_articles_api()
