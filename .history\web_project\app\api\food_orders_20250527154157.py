from flask import Blueprint, request, jsonify
from datetime import datetime
from app.models.models import ElderlyUser

# 创建蓝图
food_orders_bp = Blueprint('food_orders', __name__)

# 模拟数据存储（实际项目中应使用数据库）
# 订单存储列表
orders_storage = []

def get_user_info(user_id):
    """从数据库获取用户信息"""
    try:
        user = ElderlyUser.query.filter_by(user_id=user_id).first()
        if user:
            return {
                'name': user.name,
                'phone': user.phone or '未设置',
                'address': user.address or '地址未设置',
                'emergency_contact': user.emergency_contact_name or '未设置',
                'emergency_phone': user.emergency_contact_phone or '未设置'
            }
        else:
            # 如果数据库中没有找到用户，返回默认信息
            return {
                'name': f'用户{user_id}',
                'phone': '未设置',
                'address': '地址未设置',
                'emergency_contact': '未设置',
                'emergency_phone': '未设置'
            }
    except Exception as e:
        print(f"获取用户信息失败: {str(e)}")
        # 发生错误时返回默认信息
        return {
            'name': f'用户{user_id}',
            'phone': '未设置',
            'address': '地址未设置',
            'emergency_contact': '未设置',
            'emergency_phone': '未设置'
        }

@food_orders_bp.route('/submit', methods=['POST'])
def submit_order():
    """接收老年人提交的订餐订单"""
    try:
        data = request.get_json()

        # 验证必要字段
        required_fields = ['user_id', 'items', 'total_price', 'delivery_time', 'address']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'缺少必要字段: {field}'}), 400

        # 生成订单ID
        order_id = f"FO{datetime.now().strftime('%Y%m%d%H%M%S')}{len(orders_storage)+1:03d}"

        # 获取用户信息
        user_info = get_user_info(data['user_id'])

        # 创建订单对象
        order = {
            'order_id': order_id,
            'user_id': data['user_id'],
            'user_name': user_info['name'],
            'user_phone': user_info['phone'],
            'items': data['items'],
            'total_price': data['total_price'],
            'delivery_fee': data.get('delivery_fee', 5),
            'final_price': data['total_price'] + data.get('delivery_fee', 5),
            'delivery_time': data['delivery_time'],
            'delivery_address': data.get('address', user_info['address']),  # 优先使用用户输入的地址，否则使用数据库地址
            'payment_method': data.get('payment_method', '子女代付'),
            'special_notes': data.get('special_notes', ''),
            'status': 'pending_assignment',  # 待分配
            'assigned_staff': None,
            'assigned_staff_name': None,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'emergency_contact': user_info['emergency_contact'],
            'emergency_phone': user_info['emergency_phone']
        }

        # 存储订单
        orders_storage.append(order)

        print(f"新订单已创建: {order_id}, 用户: {user_info['name']}")

        return jsonify({
            'success': True,
            'message': '订单提交成功',
            'order_id': order_id,
            'order': order
        }), 201

    except Exception as e:
        print(f"订单提交失败: {str(e)}")
        return jsonify({'error': f'订单提交失败: {str(e)}'}), 500

@food_orders_bp.route('/assign_staff', methods=['POST'])
def assign_staff():
    """为订单分配配送人员"""
    try:
        data = request.get_json()

        # 验证必要字段
        if 'order_id' not in data or 'assigned_staff' not in data:
            return jsonify({'error': '缺少必要字段: order_id 或 assigned_staff'}), 400

        order_id = data['order_id']
        assigned_staff = data['assigned_staff']

        # 查找订单
        order = None
        for o in orders_storage:
            if o['order_id'] == order_id:
                order = o
                break

        if not order:
            return jsonify({'error': '订单不存在'}), 404

        # 获取员工姓名
        staff_names = {
            '1': '王小明',
            '2': '李玲',
            '3': '赵强'
        }
        staff_name = staff_names.get(assigned_staff, f'员工{assigned_staff}')

        # 更新订单状态
        order['status'] = 'assigned'
        order['assigned_staff'] = assigned_staff
        order['assigned_staff_name'] = staff_name
        order['updated_at'] = datetime.now().isoformat()

        print(f"订单 {order_id} 已分配给 {staff_name}")

        return jsonify({
            'success': True,
            'message': f'订单已分配给 {staff_name}',
            'order': order
        }), 200

    except Exception as e:
        print(f"订单分配失败: {str(e)}")
        return jsonify({'error': f'订单分配失败: {str(e)}'}), 500

@food_orders_bp.route('/list', methods=['GET'])
def get_orders():
    """获取订单列表（供工作人员使用）"""
    try:
        # 获取查询参数
        status = request.args.get('status', None)

        # 过滤订单
        filtered_orders = orders_storage
        if status:
            filtered_orders = [o for o in orders_storage if o['status'] == status]

        # 转换为工作人员界面需要的格式
        meal_services = []
        for order in filtered_orders:
            service = {
                'id': order['order_id'],
                'elderly_id': order['user_id'],
                'name': order['user_name'],
                'status': 'pending' if order['status'] == 'pending_assignment' else
                         'assigned' if order['status'] == 'assigned' else 'delivered',
                'staff': order['assigned_staff_name'],
                'address': order['delivery_address'],
                'time': order['delivery_time'],
                'phone': order['user_phone'],
                'total_price': order['final_price'],
                'payment_method': order['payment_method'],
                'special_notes': order['special_notes'],
                'items': order['items'],
                'created_at': order['created_at'],
                'emergency_contact': order['emergency_contact'],
                'emergency_phone': order['emergency_phone']
            }
            meal_services.append(service)

        # 按创建时间倒序排列
        meal_services.sort(key=lambda x: x['created_at'], reverse=True)

        return jsonify({
            'success': True,
            'meal_services': meal_services,
            'total_count': len(meal_services)
        }), 200

    except Exception as e:
        print(f"获取订单列表失败: {str(e)}")
        return jsonify({'error': f'获取订单列表失败: {str(e)}'}), 500

@food_orders_bp.route('/update_status', methods=['POST'])
def update_order_status():
    """更新订单状态"""
    try:
        data = request.get_json()

        if 'order_id' not in data or 'status' not in data:
            return jsonify({'error': '缺少必要字段: order_id 或 status'}), 400

        order_id = data['order_id']
        new_status = data['status']

        # 查找并更新订单
        order = None
        for o in orders_storage:
            if o['order_id'] == order_id:
                order = o
                break

        if not order:
            return jsonify({'error': '订单不存在'}), 404

        order['status'] = new_status
        order['updated_at'] = datetime.now().isoformat()

        print(f"订单 {order_id} 状态已更新为: {new_status}")

        return jsonify({
            'success': True,
            'message': '订单状态更新成功',
            'order': order
        }), 200

    except Exception as e:
        print(f"更新订单状态失败: {str(e)}")
        return jsonify({'error': f'更新订单状态失败: {str(e)}'}), 500

@food_orders_bp.route('/status/<order_id>', methods=['GET'])
def get_order_status(order_id):
    """获取订单状态"""
    try:
        # 查找订单
        order = None
        for o in orders_storage:
            if o['order_id'] == order_id:
                order = o
                break

        if not order:
            return jsonify({'error': '订单不存在'}), 404

        return jsonify({
            'success': True,
            'order': order
        }), 200

    except Exception as e:
        print(f"获取订单状态失败: {str(e)}")
        return jsonify({'error': f'获取订单状态失败: {str(e)}'}), 500

@food_orders_bp.route('/simulate_delivery/<order_id>', methods=['POST'])
def simulate_delivery(order_id):
    """模拟开始配送"""
    try:
        # 查找订单
        order = None
        for o in orders_storage:
            if o['order_id'] == order_id:
                order = o
                break

        if not order:
            return jsonify({'error': '订单不存在'}), 404

        # 更新订单状态为配送中
        order['status'] = 'delivering'
        order['updated_at'] = datetime.now().isoformat()

        print(f"订单 {order_id} 开始配送")

        return jsonify({
            'success': True,
            'message': '开始配送',
            'order': order
        }), 200

    except Exception as e:
        print(f"模拟配送失败: {str(e)}")
        return jsonify({'error': f'模拟配送失败: {str(e)}'}), 500

@food_orders_bp.route('/simulate_arrival/<order_id>', methods=['POST'])
def simulate_arrival(order_id):
    """模拟送达"""
    try:
        # 查找订单
        order = None
        for o in orders_storage:
            if o['order_id'] == order_id:
                order = o
                break

        if not order:
            return jsonify({'error': '订单不存在'}), 404

        # 更新订单状态为已送达
        order['status'] = 'delivered'
        order['updated_at'] = datetime.now().isoformat()

        print(f"订单 {order_id} 已送达，等待用户确认收货")

        return jsonify({
            'success': True,
            'message': '已送达',
            'order': order
        }), 200

    except Exception as e:
        print(f"模拟送达失败: {str(e)}")
        return jsonify({'error': f'模拟送达失败: {str(e)}'}), 500

@food_orders_bp.route('/confirm_receipt/<order_id>', methods=['POST'])
def confirm_receipt(order_id):
    """确认收货"""
    try:
        # 查找订单
        order = None
        for o in orders_storage:
            if o['order_id'] == order_id:
                order = o
                break

        if not order:
            return jsonify({'error': '订单不存在'}), 404

        # 更新订单状态为已完成
        order['status'] = 'completed'
        order['updated_at'] = datetime.now().isoformat()

        print(f"订单 {order_id} 已完成，用户确认收货")

        return jsonify({
            'success': True,
            'message': '订单已完成',
            'order': order
        }), 200

    except Exception as e:
        print(f"确认收货失败: {str(e)}")
        return jsonify({'error': f'确认收货失败: {str(e)}'}), 500

@food_orders_bp.route('/my_orders/<user_id>', methods=['GET'])
def get_my_orders(user_id):
    """获取用户的订单列表"""
    try:
        # 过滤出该用户的订单
        user_orders = [order for order in orders_storage if order['user_id'] == user_id]

        # 按创建时间倒序排列
        user_orders.sort(key=lambda x: x['created_at'], reverse=True)

        return jsonify({
            'success': True,
            'orders': user_orders,
            'total_count': len(user_orders)
        }), 200

    except Exception as e:
        print(f"获取用户订单列表失败: {str(e)}")
        return jsonify({'error': f'获取用户订单列表失败: {str(e)}'}), 500
