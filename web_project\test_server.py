#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app, db
    print("✅ 成功导入app模块")
    
    app = create_app()
    print("✅ 成功创建Flask应用")
    
    with app.app_context():
        # 创建数据库表
        db.create_all()
        print("✅ 数据库表创建成功")
        
        # 测试数据库连接
        from app.models.models import ElderlyUser, FamilyUser
        elderly = ElderlyUser.query.first()
        family = FamilyUser.query.first()
        
        print(f"📋 数据库中的老人用户: {elderly.name if elderly else '无'}")
        print(f"📋 数据库中的家属用户: {family.name if family else '无'}")
        
        if elderly and family:
            print(f"📋 老人{elderly.user_id}绑定的家属: {elderly.bound_family_ids}")
            print(f"📋 家属{family.family_id}绑定的老人: {family.bound_elderly_id}")
    
    print("🚀 启动Flask服务器...")
    app.run(debug=True, host='0.0.0.0', port=5000)
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
