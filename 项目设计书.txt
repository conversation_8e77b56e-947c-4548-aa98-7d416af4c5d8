# 居家养老健康管理系统项目设计书

## 一、系统设计（40分）

### 1. 功能模块覆盖

本系统采用模块化设计，包含以下核心功能模块：

#### 1.1 用户管理模块
- 用户注册：支持老年人、家属和社区工作人员三类用户注册
- 用户登录：多角色登录系统，权限分离
- 用户信息管理：个人资料维护与修改

#### 1.2 健康档案管理模块
- 基础健康信息记录：血压、血糖、病史等数据管理
- 健康档案查询：支持按时间和类型检索历史健康数据
- 健康档案分析：生成健康趋势报告和风险评估

#### 1.3 健康监测模块
- 实时数据采集：对接智能手环等设备实时获取健康数据
- 数据可视化：将健康数据转化为直观图表
- 异常监测：自动识别异常健康指标并发出预警

#### 1.4 健康管理模块
- 运动监测：记录和分析日常运动情况
- 睡眠监测：记录和分析睡眠质量与时长
- 饮食监测：记录和分析饮食习惯和营养摄入

#### 1.5 社区服务模块
- 订餐服务：老年人在线订餐，支持定制餐类型
- 送餐管理：社区工作人员安排送餐人员和时间
- 服务评价：老年人对服务进行评价和反馈

#### 1.6 紧急救助模块
- 一键求助：老年人遇到紧急情况时一键发起求助
- GPS定位：自动获取老年人当前位置并显示在地图上
- 紧急联系人通知：自动通知家属和社区工作人员

#### 1.7 家属监护模块
- 实时监测：家属远程查看老年人健康状态
- 活动轨迹：查看老年人活动记录和位置信息
- 紧急通知：接收老年人紧急求助信息

#### 1.8 健康科普模块
- 健康知识库：提供养生保健知识
- 健康资讯推送：根据老年人健康状况推送相关健康知识
- 内容收藏：老年人可收藏感兴趣的健康内容

### 2. 开发模式说明

本系统采用MVC（Model-View-Controller）架构模式进行开发，实现业务逻辑、数据和界面的分离，提高系统的可维护性和可扩展性。

- **Model（模型）**：负责数据处理和业务逻辑
  - 数据库模型定义（models.py）
  - 数据访问和处理逻辑
  - 业务规则和数据验证

- **View（视图）**：负责页面展示
  - HTML模板（templates目录）
  - CSS样式和JavaScript交互
  - 响应式布局和用户体验设计

- **Controller（控制器）**：负责协调模型和视图
  - 路由处理（routes.py）
  - 请求处理和响应生成
  - 业务流程控制和调度

### 3. 数据库设计

#### 3.1 概念模型（实体类图）

##### ER图
```
+---------------+       +----------------+       +----------------+
|  ElderlyUser  |-------| HealthRecord   |       | CommunityWorker|
+---------------+       +----------------+       +----------------+
| PK: user_id   |       | PK: record_id  |       | PK: worker_id  |
| password      |       | blood_pressure |       | password       |
| name          |       | blood_sugar    |       | name           |
| age           |       | medication     |       | region         |
| phone         |       | medical_history|       | phone          |
| address       |       | update_time    |       +--------+-------+
| emergency_info|       | health_threshold|              |
| health_record_id<---+ +----------------+              |
| smartwatch_id |                                       |
| bound_family  |                                       |
+-------+-------+                                       |
        |                 +----------------+            |
        |                 |   Smartwatch   |            |
        |                 +----------------+            |
        |                 | PK: watch_id   |            |
        +---------------->| bound_user_id  |            |
        |                 | last_sync_time |            |
        |                 | gps_location   |            |
        |                 | heart_rate     |            |
        |                 | sleep_score    |            |
        |                 | step_count     |            |
        |                 | battery        |            |
        |                 +----------------+            |
        |                                               |
        |                 +----------------+            |
        |                 | EmergencyCall  |            |
        |                 +----------------+            |
        +---------------->| PK: call_id    |            |
        |                 | elderly_id     |            |
        |                 | call_time      |            |
        |                 | gps_location   |            |
        |                 | status         |            |
        |                 | related_person |            |
        |                 +----------------+            |
        |                                               |
        |                 +----------------+            |
        |                 |   FamilyUser   |            |
        |                 +----------------+            |
        +---------------->| PK: family_id  |            |
        |                 | password       |            |
        |                 | name           |            |
        |                 | relationship   |            |
        |                 | phone          |            |
        |                 | bound_elderly  |            |
        |                 +----------------+            |
        |                                               |
        |                 +----------------+            |
        |                 |   MealOrder    |            |
        |                 +----------------+            |
        +---------------->| PK: order_id   |            |
                          | elderly_id     |            |
                          | meal_type      |            |
                          | order_time     |            |
                          | delivery_time  |            |
                          | status         |            |
                          | special_req    |            |
                          +-------+--------+            |
                                  |                     |
                                  |                     |
                                  v                     |
                          +----------------+            |
                          | MealDelivery   |            |
                          +----------------+            |
                          | PK: delivery_id|            |
                          | order_id       |            |
                          | worker_id      |<-----------+
                          | assigned_time  |
                          | completed_time |
                          | status         |
                          +----------------+
```

实体间的关系：
- 老年人用户(ElderlyUser) 1--1 健康档案(HealthRecord)
- 老年人用户(ElderlyUser) 1--1 智能手环(Smartwatch)
- 老年人用户(ElderlyUser) 1--n 紧急呼叫(EmergencyCall)
- 老年人用户(ElderlyUser) 1--n 家属用户(FamilyUser)
- 老年人用户(ElderlyUser) 1--n 订餐记录(MealOrder)
- 社区工作人员(CommunityWorker) 1--n 送餐任务(MealDelivery)

#### 3.2 关系模型

**老年人用户表(ElderlyUser)**

| 字段名 | 类型 | 属性 | 说明 |
|-------|------|------|------|
| user_id | VARCHAR(10) | 主键 | 用户ID |
| password | VARCHAR(50) | 非空 | 用户密码 |
| name | VARCHAR(50) | 非空 | 用户姓名 |
| age | INT | | 年龄 |
| phone | VARCHAR(20) | | 联系电话 |
| address | VARCHAR(100) | | 居住地址 |
| emergency_contact_name | VARCHAR(50) | | 紧急联系人姓名 |
| emergency_contact_phone | VARCHAR(20) | | 紧急联系人电话 |
| health_record_id | VARCHAR(10) | 外键 | 关联的健康档案ID |
| smartwatch_id | VARCHAR(10) | 外键 | 关联的智能手环ID |
| bound_family_ids | VARCHAR(100) | | 绑定的家属ID列表 |

**家属用户表(FamilyUser)**

| 字段名 | 类型 | 属性 | 说明 |
|-------|------|------|------|
| family_id | VARCHAR(10) | 主键 | 家属ID |
| password | VARCHAR(50) | 非空 | 密码 |
| name | VARCHAR(50) | 非空 | 姓名 |
| relationship | VARCHAR(20) | | 与老人的关系 |
| phone | VARCHAR(20) | | 联系电话 |
| bound_elderly_id | VARCHAR(10) | 外键 | 关联的老人ID |

**社区工作人员表(CommunityWorker)**

| 字段名 | 类型 | 属性 | 说明 |
|-------|------|------|------|
| worker_id | VARCHAR(10) | 主键 | 工作人员ID |
| password | VARCHAR(50) | 非空 | 密码 |
| name | VARCHAR(50) | 非空 | 姓名 |
| region | VARCHAR(10) | | 负责区域 |
| phone | VARCHAR(20) | | 联系电话 |

**智能手环表(Smartwatch)**

| 字段名 | 类型 | 属性 | 说明 |
|-------|------|------|------|
| watch_id | VARCHAR(10) | 主键 | 手环ID |
| bound_user_id | VARCHAR(10) | 外键 | 绑定的用户ID |
| last_sync_time | DATETIME | | 最后同步时间 |
| gps_location | VARCHAR(50) | | GPS位置信息 |
| heart_rate | INT | | 心率 |
| sleep_score | INT | | 睡眠评分 |
| step_count | INT | | 步数 |
| battery | INT | | 电池电量 |

**健康档案表(HealthRecord)**

| 字段名 | 类型 | 属性 | 说明 |
|-------|------|------|------|
| record_id | VARCHAR(10) | 主键 | 档案ID |
| blood_pressure | VARCHAR(20) | | 血压值 |
| blood_sugar | VARCHAR(20) | | 血糖值 |
| medication_record | VARCHAR(1000) | | 用药记录 |
| medical_history | VARCHAR(1000) | | 病史记录 |
| update_time | DATE | | 更新时间 |
| health_threshold | VARCHAR(1000) | | 健康阈值设置 |

**紧急呼叫表(EmergencyCall)**

| 字段名 | 类型 | 属性 | 说明 |
|-------|------|------|------|
| call_id | VARCHAR(20) | 主键 | 呼叫ID |
| elderly_id | VARCHAR(10) | 外键 | 老人ID |
| call_time | DATETIME | | 呼叫时间 |
| gps_location | VARCHAR(50) | | 呼叫位置 |
| status | VARCHAR(20) | | 处理状态 |
| related_person_id | VARCHAR(10) | | 处理人ID |

**订餐记录表(MealOrder)**

| 字段名 | 类型 | 属性 | 说明 |
|-------|------|------|------|
| order_id | VARCHAR(20) | 主键 | 订单ID |
| elderly_id | VARCHAR(10) | 外键 | 老人ID |
| meal_type | VARCHAR(50) | | 餐食类型 |
| order_time | DATETIME | | 下单时间 |
| delivery_time | DATETIME | | 预期送达时间 |
| status | VARCHAR(20) | | 订单状态 |
| special_requirements | VARCHAR(200) | | 特殊要求 |

**送餐任务表(MealDelivery)**

| 字段名 | 类型 | 属性 | 说明 |
|-------|------|------|------|
| delivery_id | VARCHAR(20) | 主键 | 送餐任务ID |
| order_id | VARCHAR(20) | 外键 | 关联的订单ID |
| worker_id | VARCHAR(10) | 外键 | 负责送餐的工作人员ID |
| assigned_time | DATETIME | | 任务分配时间 |
| completed_time | DATETIME | | 任务完成时间 |
| status | VARCHAR(20) | | 任务状态 |

#### 3.3 物理设计

使用MySQL数据库，表格创建示例SQL如下：

```sql
CREATE TABLE ElderlyUser (
    user_id VARCHAR(10) PRIMARY KEY,
    password VARCHAR(50) NOT NULL,
    name VARCHAR(50) NOT NULL,
    age INT,
    phone VARCHAR(20),
    address VARCHAR(100),
    emergency_contact_name VARCHAR(50),
    emergency_contact_phone VARCHAR(20),
    health_record_id VARCHAR(10),
    smartwatch_id VARCHAR(10),
    bound_family_ids VARCHAR(100)
);

CREATE TABLE FamilyUser (
    family_id VARCHAR(10) PRIMARY KEY,
    password VARCHAR(50) NOT NULL,
    name VARCHAR(50) NOT NULL,
    relationship VARCHAR(20),
    phone VARCHAR(20),
    bound_elderly_id VARCHAR(10),
    FOREIGN KEY (bound_elderly_id) REFERENCES ElderlyUser(user_id)
);

CREATE TABLE CommunityWorker (
    worker_id VARCHAR(10) PRIMARY KEY,
    password VARCHAR(50) NOT NULL,
    name VARCHAR(50) NOT NULL,
    region VARCHAR(10),
    phone VARCHAR(20)
);

CREATE TABLE Smartwatch (
    watch_id VARCHAR(10) PRIMARY KEY,
    bound_user_id VARCHAR(10),
    last_sync_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    gps_location VARCHAR(50),
    heart_rate INT,
    sleep_score INT,
    step_count INT,
    battery INT,
    FOREIGN KEY (bound_user_id) REFERENCES ElderlyUser(user_id)
);

CREATE TABLE HealthRecord (
    record_id VARCHAR(10) PRIMARY KEY,
    blood_pressure VARCHAR(20),
    blood_sugar VARCHAR(20),
    medication_record VARCHAR(1000),
    medical_history VARCHAR(1000),
    update_time DATE,
    health_threshold VARCHAR(1000)
);

CREATE TABLE EmergencyCall (
    call_id VARCHAR(20) PRIMARY KEY,
    elderly_id VARCHAR(10),
    call_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    gps_location VARCHAR(50),
    status VARCHAR(20) DEFAULT '待处理',
    related_person_id VARCHAR(10),
    FOREIGN KEY (elderly_id) REFERENCES ElderlyUser(user_id)
);

CREATE TABLE MealOrder (
    order_id VARCHAR(20) PRIMARY KEY,
    elderly_id VARCHAR(10),
    meal_type VARCHAR(50),
    order_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    delivery_time DATETIME,
    status VARCHAR(20) DEFAULT '待处理',
    special_requirements VARCHAR(200),
    FOREIGN KEY (elderly_id) REFERENCES ElderlyUser(user_id)
);

CREATE TABLE MealDelivery (
    delivery_id VARCHAR(20) PRIMARY KEY,
    order_id VARCHAR(20),
    worker_id VARCHAR(10),
    assigned_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_time DATETIME,
    status VARCHAR(20) DEFAULT '待分配',
    FOREIGN KEY (order_id) REFERENCES MealOrder(order_id),
    FOREIGN KEY (worker_id) REFERENCES CommunityWorker(worker_id)
);
```

### 4. 项目架构搭建

#### 4.1 项目目录结构
```
web_project/
├── app/
│   ├── __init__.py         # 应用程序初始化
│   ├── models/             # 数据模型定义
│   │   ├── __init__.py
│   │   └── models.py       # 数据库模型
│   ├── static/             # 静态资源文件
│   │   ├── css/            # 样式表
│   │   ├── js/             # JavaScript脚本
│   │   └── images/         # 图像资源
│   ├── templates/          # HTML模板
│   │   ├── base.html                # 基础模板
│   │   ├── index.html               # 首页
│   │   ├── login.html               # 登录页
│   │   ├── register.html            # 注册页
│   │   ├── elderly_center.html      # 老人中心
│   │   ├── family_center.html       # 家属中心
│   │   ├── worker_dashboard.html    # 工作人员控制台
│   │   ├── elderly_emergency.html   # 紧急呼叫页面
│   │   └── family_monitoring.html   # 家属监控页面
│   ├── routes.py           # 主路由定义
│   ├── family_routes.py    # 家属功能路由
│   └── api/                # API接口
│       └── __init__.py
├── config.py               # 配置文件
├── app.py                  # 应用入口
├── create_mysql_db.py      # 数据库创建脚本
└── requirements.txt        # 依赖包列表
```

#### 4.2 技术栈选择
- 前端：HTML5 + CSS3 + JavaScript + Vue.js + Bootstrap
- 后端：Python + Flask框架
- 数据库：MySQL
- 地图服务：百度地图API（用于位置显示和跟踪）
- 图表库：ECharts（用于健康数据可视化）

#### 4.3 配置的依赖包（requirements.txt）
```
Flask==2.0.1
Flask-SQLAlchemy==2.5.1
Flask-Login==0.5.0
Flask-WTF==0.15.1
mysql-connector-python==8.0.26
python-dotenv==0.19.0
Flask-Migrate==3.1.0
Werkzeug==2.0.1
itsdangerous==2.0.1
Jinja2==3.0.1
MarkupSafe==2.0.1
SQLAlchemy==1.4.23
```

#### 4.4 数据库连接配置
在config.py文件中配置数据库连接：
```python
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'hard-to-guess-string'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'mysql+mysqlconnector://username:password@localhost/elderly_care'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
```

## 二、业务

### 1. 接口定义

#### 1.1 用户端接口

| 接口名称 | 用途 | 目标用户 |
|---------|------|---------|
| /register | 用户注册 | 老人/家属/社区工作人员 |
| /login | 用户登录 | 老人/家属/社区工作人员 |
| /elderly/center | 老人个人中心 | 老人 |
| /elderly/health | 健康记录查看 | 老人 |
| /elderly/monitor | 健康监测面板 | 老人 |
| /elderly/order_meal | 订餐服务 | 老人 |
| /elderly/emergency | 紧急求助 | 老人 |
| /elderly/knowledge | 健康知识 | 老人 |
| /family/center | 家属个人中心 | 家属 |
| /family/monitoring | 老人状态监控 | 家属 |
| /family/track | 活动轨迹查看 | 家属 |
| /family/alerts | 警报通知 | 家属 |

#### 1.2 管理端接口

| 接口名称 | 用途 | 目标用户 |
|---------|------|---------|
| /worker/dashboard | 工作人员控制台 | 社区工作人员 |
| /worker/elderly_list | 老人列表管理 | 社区工作人员 |
| /worker/meal_orders | 订餐记录管理 | 社区工作人员 |
| /worker/meal_delivery | 配送任务管理 | 社区工作人员 |
| /worker/emergency_calls | 紧急呼叫处理 | 社区工作人员 |
| /worker/statistics | 统计数据分析 | 社区工作人员 |

#### 1.3 API接口

| 接口名称 | 用途 | 目标用户 |
|---------|------|---------|
| /api/health_data | 获取健康数据 | 系统内部/老人/家属 |
| /api/emergency | 紧急呼叫管理 | 系统内部/老人/家属/社区工作人员 |
| /api/location | 位置信息获取 | 系统内部/老人/家属/社区工作人员 |
| /api/meal_order | 订餐管理 | 系统内部/老人/社区工作人员 |
| /api/notifications | 通知管理 | 系统内部/老人/家属/社区工作人员 |

### 2. 业务流程描述

#### 2.1 老年人健康管理流程
老年人登录系统后，可以查看个人健康档案，包括血压、血糖等健康数据。系统会实时获取智能手环上传的数据，如心率、步数、睡眠质量等，并生成健康报告。老年人可以查看自己的运动记录、睡眠情况和饮食习惯，并根据系统提供的健康建议调整生活方式。当健康数据异常时，系统会发出警报并通知家属。

#### 2.2 紧急呼叫处理流程
当老年人遇到紧急情况时，可以通过系统一键发起紧急呼叫。系统会自动获取老年人的GPS位置信息，并同时向家属和社区工作人员发送紧急通知。家属和社区工作人员收到通知后，可以通过系统查看老年人的位置和健康信息，并迅速采取救助措施。紧急呼叫信息将被记录在系统中，直到状态被标记为"已解决"。

#### 2.3 订餐服务流程
老年人可以通过系统浏览提供的餐食选项，并根据个人喜好和健康需求进行订餐。订单提交后，系统会自动将订单信息发送给社区工作人员。社区工作人员收到订单后，安排送餐人员并确定送餐时间。订单状态会实时更新，老年人可以在系统中跟踪订单进度。送餐完成后，老年人可以对服务进行评价，以便系统改进服务质量。

### 业务流程图
```
老年人健康管理流程：
[老年人登录] --> [查看健康档案] --> [实时健康监测] --> [查看健康报告] --> [接收健康建议]
                                      |
                                      v
                        [健康异常] --> [发送警报] --> [家属接收通知]

紧急呼叫处理流程：
[老年人发起紧急呼叫] --> [获取GPS位置] --> [发送紧急通知] --> [家属接收通知]
                                          |
                                          v
                                    [社区工作人员接收通知] --> [提供救助] --> [标记为已解决]

订餐服务流程：
[老年人浏览餐食选项] --> [提交订单] --> [系统记录订单] --> [社区工作人员接收订单]
                                                      |
                                                      v
                                        [安排送餐人员] --> [送餐] --> [老年人评价]
```

## 三、用例分析图

### 1. 用户端用例图

```
老年人用户：
+----------------------------------+
|              老年人               |
+----------------------------------+
           /     |     \     \     \
          /      |      \     \     \
         /       |       \     \     \
        v        v        v     v     v
+------------+ +-------+ +------+ +-------+ +------------+
| 健康档案管理 | | 健康监测| |订餐服务| |紧急呼叫| | 健康知识浏览 |
+------------+ +-------+ +------+ +-------+ +------------+
    |  |  |       |  |     |  |     |  |         |  |
    |  |  |       |  |     |  |     |  |         |  |
    v  v  v       v  v     v  v     v  v         v  v
查看 更新 历史   运动 睡眠  订餐 评价  呼叫 查看位置  浏览 收藏
```

```
家属用户：
+----------------------------------+
|              家属                |
+----------------------------------+
           /        |        \
          /         |         \
         /          |          \
        v           v           v
+----------------+ +------------+ +------------+
|   老人状态监控   | |  紧急通知接收 | |  活动轨迹查看 |
+----------------+ +------------+ +------------+
    |      |           |   |          |    |
    |      |           |   |          |    |
    v      v           v   v          v    v
查看健康数据 查看警报  接收 响应    查看位置 查看记录
```

### 2. 管理端用例图

```
社区工作人员：
+----------------------------------+
|          社区工作人员             |
+----------------------------------+
      /       |       \       \
     /        |        \       \
    v         v         v       v
+----------+ +--------+ +------+ +------------+
| 老人管理  | | 订餐管理 | |送餐管理| | 紧急呼叫处理 |
+----------+ +--------+ +------+ +------------+
   |    |      |   |     |   |       |    |
   |    |      |   |     |   |       |    |
   v    v      v   v     v   v       v    v
查看 更新  查看 处理  分配 跟踪   接收 处理
```

## 四、功能模块设计

### 1. 模块功能描述

#### 1.1 用户管理模块
- 功能：用户注册、登录、信息管理
- 接口对应：/register, /login
- 描述：提供多角色用户（老人、家属、社区工作人员）的注册、登录和个人信息管理功能

#### 1.2 健康档案管理模块
- 功能：健康信息记录、查询、分析
- 接口对应：/elderly/health, /api/health_data
- 描述：提供老年人健康档案的创建、更新、查询功能，支持健康数据的历史记录查看和趋势分析

#### 1.3 健康监测模块
- 功能：实时数据采集、数据可视化、异常监测
- 接口对应：/elderly/monitor, /family/monitoring
- 描述：实时获取智能手环数据，生成健康监测报告，检测异常健康状况并发出警报

#### 1.4 运动睡眠饮食监测模块
- 功能：运动记录、睡眠质量分析、饮食习惯记录
- 接口对应：/elderly/monitor
- 描述：记录和分析老年人的运动情况、睡眠质量和饮食习惯，提供健康建议

#### 1.5 订餐服务模块
- 功能：餐食浏览、订单提交、订单跟踪
- 接口对应：/elderly/order_meal, /worker/meal_orders, /api/meal_order
- 描述：提供老年人在线订餐功能，社区工作人员管理订单和安排送餐

#### 1.6 紧急呼叫模块
- 功能：一键呼叫、GPS定位、紧急通知
- 接口对应：/elderly/emergency, /family/alerts, /worker/emergency_calls, /api/emergency
- 描述：老年人紧急情况一键求助，自动获取位置信息并通知家属和社区工作人员

#### 1.7 位置跟踪模块
- 功能：实时定位、轨迹记录、位置显示
- 接口对应：/family/track, /api/location
- 描述：实时跟踪老年人位置，记录活动轨迹，在地图上显示位置信息

#### 1.8 健康知识模块
- 功能：健康知识浏览、收藏、推送
- 接口对应：/elderly/knowledge
- 描述：提供老年人健康知识浏览和收藏功能，根据健康状况推送相关知识

### 2. 子功能细化

#### 2.1 用户管理模块
- 老年人注册：收集基本信息、健康信息、紧急联系人信息
- 家属注册：收集基本信息、与老人的关系、绑定老人账号
- 社区工作人员注册：收集基本信息、负责区域
- 用户登录：多角色身份验证，根据角色进入不同界面
- 个人信息管理：修改密码、更新联系方式、更新健康信息

#### 2.2 健康档案管理模块
- 基础健康信息记录：血压、血糖、体重、体温等数据录入
- 病史管理：慢性病、手术史、过敏史等记录
- 用药管理：处方药、用药时间、用药剂量记录
- 健康报告生成：基于历史数据生成健康趋势报告
- 健康风险评估：分析健康数据，评估健康风险

#### 2.3 健康监测模块
- 心率监测：实时监测心率变化，异常提醒
- 血压监测：记录血压数据，分析波动情况
- 血糖监测：记录血糖数据，分析餐前餐后变化
- 活动量监测：记录步数、活动时长，评估活动强度
- 数据可视化：将健康数据转化为图表，直观展示健康状况

#### 2.4 运动睡眠饮食监测模块
- 运动记录：记录运动类型、时长、强度
- 运动建议：根据健康状况提供适合的运动建议
- 睡眠质量分析：记录睡眠时长、深睡时间，评估睡眠质量
- 睡眠建议：提供改善睡眠质量的建议
- 饮食记录：记录饮食内容、时间、营养成分
- 饮食建议：根据健康状况提供营养均衡的饮食建议

#### 2.5 订餐服务模块
- 餐食浏览：查看可选餐食类型、价格、营养成分
- 订单提交：选择餐食、指定送餐时间、添加特殊要求
- 订单管理：查看历史订单、取消订单、修改订单
- 送餐安排：社区工作人员分配送餐任务、指定送餐人员
- 送餐跟踪：实时查看送餐状态、预计送达时间
- 服务评价：对送餐服务进行评分和评价

#### 2.6 紧急呼叫模块
- 一键呼叫：通过大按钮或语音指令发起紧急呼叫
- GPS定位：自动获取老年人当前位置坐标
- 位置显示：在地图上显示老年人位置
- 紧急通知：自动向家属和社区工作人员发送紧急通知
- 呼叫记录：记录紧急呼叫时间、位置、处理情况
- 处理跟踪：跟踪紧急呼叫的处理进度和结果

## 五、管理端设计

### 1. 模块分类与功能

#### 1.1 老人管理模块
- 老人信息管理：查看、编辑老人基本信息
- 健康记录查看：查看老人健康档案和健康监测数据
- 老人分组管理：按区域、健康状况等对老人进行分组管理

#### 1.2 订餐管理模块
- 订单接收：接收老人提交的订餐订单
- 订单处理：审核订单、确认订单信息
- 订单统计：统计每日订单数量、餐食类型分布等

#### 1.3 送餐管理模块
- 送餐任务分配：将订单分配给送餐人员
- 送餐进度跟踪：实时查看送餐任务执行情况
- 送餐评价查看：查看老人对送餐服务的评价

#### 1.4 紧急呼叫处理模块
- 呼叫接收：接收老人发起的紧急呼叫
- 位置查看：在地图上查看老人位置
- 救助安排：协调救助资源，安排就近救助
- 处理记录：记录紧急呼叫的处理过程和结果

#### 1.5 统计分析模块
- 健康数据统计：统计辖区内老人健康状况
- 订餐数据分析：分析订餐需求、餐食偏好等
- 紧急呼叫分析：分析紧急呼叫频率、原因等
- 报表生成：生成工作报告、服务质量报告等

### 2. 界面示意图

管理端主界面采用响应式设计，主要包括侧边导航栏、信息统计面板、工作任务列表等组件。界面清晰直观，操作简便，确保社区工作人员能够高效完成日常工作。

## 六、报告格式与附加内容

### 1. 结构要求
本报告按照"整体模块→用户端→管理端→优化美化"的顺序编写，确保内容清晰有序，逻辑分层明确。

### 2. 附加优化内容

#### 2.1 界面优化
- 增加老年人界面的字体大小，提高可读性
- 使用高对比度的配色方案，确保视觉清晰
- 简化操作流程，减少点击次数
- 增加语音交互功能，支持语音指令和语音提示

#### 2.2 功能优化
- 优化紧急呼叫功能，增加GPS位置追踪和地图显示
- 完善订餐服务，增加送餐人员派遣和追踪功能
- 增强健康监测模块，增加运动、睡眠、饮食监测功能
- 调整趋势分析显示，优化"血糖波动"和"血压偏高"指标的显示效果

### 3. 配置信息表

| JAR包名称 | 版本号 | 用途 |
|----------|-------|------|
| Flask | 2.0.1 | Web框架 |
| Flask-SQLAlchemy | 2.5.1 | ORM数据库操作 |
| Flask-Login | 0.5.0 | 用户认证管理 |
| Flask-WTF | 0.15.1 | 表单处理 |
| mysql-connector-python | 8.0.26 | MySQL数据库连接 |
| python-dotenv | 0.19.0 | 环境变量管理 |
| Flask-Migrate | 3.1.0 | 数据库迁移 |
| Werkzeug | 2.0.1 | WSGI工具库 |
| itsdangerous | 2.0.1 | 数据签名 |
| Jinja2 | 3.0.1 | 模板引擎 |
| MarkupSafe | 2.0.1 | 字符转义 |
| SQLAlchemy | 1.4.23 | SQL工具包 |
| Vue.js | 2.6.14 | 前端框架 |
| Bootstrap | 5.1.0 | 前端UI框架 |
| ECharts | 5.1.2 | 图表可视化库 |
| Baidu Maps API | 3.0 | 地图服务 | 