from flask import Blueprint, request, jsonify
from app import db
from app.models.models import Elderly<PERSON>ser, FamilyUser, Smartwatch, EmergencyCall, HealthRecord

# 创建蓝图
family_bp = Blueprint('family_api', __name__)

@family_bp.route('/bind', methods=['POST'])
def bind_elderly():
    """家属绑定老年人用户"""
    data = request.json
    family_id = data.get('family_id')
    elderly_id = data.get('elderly_id')

    if not family_id or not elderly_id:
        return jsonify({'error': '缺少家属ID或老年人ID'}), 400

    try:
        # 验证家属和老年人是否存在
        family = FamilyUser.query.filter_by(family_id=family_id).first()
        elderly = ElderlyUser.query.filter_by(user_id=elderly_id).first()

        if not family:
            return jsonify({'error': '未找到该家属用户'}), 404
        if not elderly:
            return jsonify({'error': '未找到该老年人用户'}), 404

        # 更新家属绑定的老年人ID
        family.bound_elderly_id = elderly_id

        # 更新老年人绑定的家属列表
        if elderly.bound_family_ids:
            family_ids = elderly.bound_family_ids.split(',')
            if family_id not in family_ids:
                elderly.bound_family_ids += f',{family_id}'
        else:
            elderly.bound_family_ids = family_id

        db.session.commit()
        return jsonify({'success': True, 'message': '绑定成功'}), 200
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'绑定失败: {str(e)}'}), 500

@family_bp.route('/elderly/<family_id>', methods=['GET'])
def get_bound_elderly(family_id):
    """获取家属绑定的老年人信息（支持多老人）"""
    try:
        family = FamilyUser.query.filter_by(family_id=family_id).first()
        if not family:
            return jsonify({'error': '未找到该家属用户'}), 404

        # 查找所有绑定了该家属的老年人
        bound_elderly_list = []
        all_elderly = ElderlyUser.query.all()

        for elderly in all_elderly:
            if elderly.bound_family_ids and family_id in elderly.bound_family_ids.split(','):
                # 获取GPS位置信息
                gps_location = None
                last_sync_time = None
                if elderly.smartwatch_id:
                    smartwatch = Smartwatch.query.filter_by(watch_id=elderly.smartwatch_id).first()
                    if smartwatch:
                        gps_location = smartwatch.gps_location
                        last_sync_time = smartwatch.last_sync_time.strftime('%Y-%m-%d %H:%M:%S') if smartwatch.last_sync_time else None

                # 判断是否为主绑定老人
                is_primary = (elderly.user_id == family.bound_elderly_id)

                bound_elderly_list.append({
                    'user_id': elderly.user_id,
                    'name': elderly.name,
                    'age': elderly.age,
                    'phone': elderly.phone,
                    'address': elderly.address,
                    'emergency_contact_name': elderly.emergency_contact_name,
                    'emergency_contact_phone': elderly.emergency_contact_phone,
                    'gps_location': gps_location,
                    'last_sync_time': last_sync_time,
                    'is_primary': is_primary
                })

        if not bound_elderly_list:
            return jsonify({'message': '未绑定任何老年人', 'elderly_list': []}), 200

        return jsonify({'elderly_list': bound_elderly_list}), 200
    except Exception as e:
        return jsonify({'error': f'获取老年人信息失败: {str(e)}'}), 500

@family_bp.route('/health/<elderly_id>', methods=['GET'])
def get_elderly_health(elderly_id):
    """获取老年人健康数据"""
    try:
        elderly = ElderlyUser.query.filter_by(user_id=elderly_id).first()
        if not elderly:
            return jsonify({'error': '未找到该老年人用户'}), 404

        if not elderly.health_record_id:
            return jsonify({'error': '该老年人未创建健康档案'}), 400

        health_record = HealthRecord.query.filter_by(record_id=elderly.health_record_id).first()
        if not health_record:
            return jsonify({'error': '未找到健康档案'}), 404

        # 智能手环数据
        smartwatch_data = {}
        if elderly.smartwatch_id:
            smartwatch = Smartwatch.query.filter_by(watch_id=elderly.smartwatch_id).first()
            if smartwatch:
                smartwatch_data = {
                    'heart_rate': smartwatch.heart_rate,
                    'sleep_score': smartwatch.sleep_score,
                    'step_count': smartwatch.step_count,
                    'last_sync_time': smartwatch.last_sync_time.strftime('%Y-%m-%d %H:%M:%S')
                }

        # 健康档案数据
        health_data = {
            'blood_pressure': health_record.blood_pressure,
            'blood_sugar': health_record.blood_sugar,
            'medication_record': health_record.medication_record,
            'medical_history': health_record.medical_history,
            'update_time': health_record.update_time.strftime('%Y-%m-%d') if health_record.update_time else None
        }

        return jsonify({
            'elderly_id': elderly_id,
            'name': elderly.name,
            'health_record': health_data,
            'smartwatch_data': smartwatch_data
        }), 200
    except Exception as e:
        return jsonify({'error': f'获取健康数据失败: {str(e)}'}), 500

@family_bp.route('/track/<elderly_id>', methods=['GET'])
def get_elderly_track(elderly_id):
    """获取老年人活动轨迹"""
    try:
        elderly = ElderlyUser.query.filter_by(user_id=elderly_id).first()
        if not elderly:
            return jsonify({'error': '未找到该老年人用户'}), 404

        # 检查是否有智能手环
        if not elderly.smartwatch_id:
            return jsonify({'error': '该老年人未绑定智能手环'}), 400

        # 获取紧急呼叫记录中的位置信息（可作为历史轨迹的一部分）
        calls = EmergencyCall.query.filter_by(elderly_id=elderly_id).order_by(EmergencyCall.call_time.desc()).all()

        call_locations = []
        for call in calls:
            if call.gps_location:
                call_locations.append({
                    'time': call.call_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'location': call.gps_location,
                    'type': '紧急呼叫'
                })

        # 获取智能手环当前位置
        smartwatch = Smartwatch.query.filter_by(watch_id=elderly.smartwatch_id).first()
        current_location = {}
        if smartwatch:
            current_location = {
                'time': smartwatch.last_sync_time.strftime('%Y-%m-%d %H:%M:%S'),
                'location': smartwatch.gps_location,
                'type': '实时位置'
            }

        return jsonify({
            'elderly_id': elderly_id,
            'name': elderly.name,
            'current_location': current_location,
            'track_history': call_locations
        }), 200
    except Exception as e:
        return jsonify({'error': f'获取活动轨迹失败: {str(e)}'}), 500

@family_bp.route('/emergency_notices/<family_id>', methods=['GET'])
def get_emergency_notices(family_id):
    """获取紧急通知"""
    try:
        family = FamilyUser.query.filter_by(family_id=family_id).first()
        if not family:
            return jsonify({'error': '未找到该家属用户'}), 404

        if not family.bound_elderly_id:
            return jsonify({'message': '未绑定任何老年人', 'notices': []}), 200

        # 获取绑定老年人的紧急呼叫记录
        calls = EmergencyCall.query.filter_by(
            elderly_id=family.bound_elderly_id,
            status='待处理'
        ).order_by(EmergencyCall.call_time.desc()).all()

        notices = []
        for call in calls:
            elderly = ElderlyUser.query.filter_by(user_id=call.elderly_id).first()

            notices.append({
                'notice_id': call.call_id,
                'notice_time': call.call_time.strftime('%Y-%m-%d %H:%M:%S'),
                'elderly_name': elderly.name if elderly else '未知',
                'type': '紧急求助',
                'status': call.status,
                'location': call.gps_location
            })

        return jsonify({'notices': notices}), 200
    except Exception as e:
        return jsonify({'error': f'获取紧急通知失败: {str(e)}'}), 500

@family_bp.route('/bound_elderly_list/<family_id>', methods=['GET'])
def get_bound_elderly_list(family_id):
    """获取家属绑定的所有老年人列表"""
    try:
        family = FamilyUser.query.filter_by(family_id=family_id).first()
        if not family:
            return jsonify({'error': '未找到该家属用户'}), 404

        # 主绑定老人
        primary_elderly_id = family.bound_elderly_id

        # 查找所有绑定了该家属的老年人
        bound_elderly_list = []
        all_elderly = ElderlyUser.query.all()

        for elderly in all_elderly:
            if elderly.bound_family_ids and family_id in elderly.bound_family_ids.split(','):
                # 判断是否为主绑定老人
                is_primary = (elderly.user_id == primary_elderly_id)

                bound_elderly_list.append({
                    'user_id': elderly.user_id,
                    'name': elderly.name,
                    'age': elderly.age,
                    'phone': elderly.phone,
                    'address': elderly.address,
                    'is_primary': is_primary
                })

        return jsonify({
            'success': True,
            'elderly_list': bound_elderly_list
        }), 200
    except Exception as e:
        return jsonify({'error': f'获取绑定老年人列表失败: {str(e)}'}), 500