#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
添加老年人数据脚本
用于修复社区人员工作端地图显示问题
"""

import mysql.connector
from datetime import datetime

def add_elderly_data():
    """添加三位老年人的数据"""
    app = create_app()

    with app.app_context():
        try:
            # 首先检查是否已存在这些用户
            existing_users = ElderlyUser.query.filter(
                ElderlyUser.user_id.in_(['E01', 'E02', 'E03'])
            ).all()

            if existing_users:
                print("发现已存在的用户，正在更新数据...")
                for user in existing_users:
                    db.session.delete(user)
                    # 同时删除对应的智能手环数据
                    if user.smartwatch:
                        db.session.delete(user.smartwatch)
                db.session.commit()
                print("已删除旧数据")

            # 添加新的老年人数据
            elderly_data = [
                {
                    'user_id': 'E01',
                    'password': '123456',
                    'name': '张建国',
                    'age': 78,
                    'phone': '13800138001',
                    'address': '浙江省杭州市余杭区龙湖天街1号',
                    'emergency_contact_name': '张小明',
                    'emergency_contact_phone': '13900139001',
                    'health_record_id': 'HR01',
                    'smartwatch_id': 'SW01',
                    'region': 'A',
                    'gps_location': '30.2741,120.1551'  # 余杭区龙湖天街附近坐标
                },
                {
                    'user_id': 'E02',
                    'password': '123456',
                    'name': '李淑兰',
                    'age': 75,
                    'phone': '13800138002',
                    'address': '浙江省杭州市西湖区西溪路518号',
                    'emergency_contact_name': '李小华',
                    'emergency_contact_phone': '13900139002',
                    'health_record_id': 'HR02',
                    'smartwatch_id': 'SW02',
                    'region': 'A',
                    'gps_location': '30.2592,120.1136'  # 西湖区西溪路附近坐标
                },
                {
                    'user_id': 'E03',
                    'password': '123456',
                    'name': '王福海',
                    'age': 80,
                    'phone': '13800138003',
                    'address': '浙江省杭州市拱墅区湖墅南路88号',
                    'emergency_contact_name': '王小丽',
                    'emergency_contact_phone': '13900139003',
                    'health_record_id': 'HR03',
                    'smartwatch_id': 'SW03',
                    'region': 'B',
                    'gps_location': '30.3089,120.1553'  # 拱墅区湖墅南路附近坐标
                }
            ]

            # 创建老年人用户
            for data in elderly_data:
                gps_location = data.pop('gps_location')  # 取出GPS坐标，用于智能手环

                elderly = ElderlyUser(**data)
                db.session.add(elderly)

                # 创建对应的智能手环数据
                smartwatch = Smartwatch(
                    watch_id=data['smartwatch_id'],
                    bound_user_id=data['user_id'],
                    last_sync_time=datetime.now(),
                    gps_location=gps_location,
                    heart_rate=75,
                    sleep_score=80,
                    step_count=3000,
                    battery=90
                )
                db.session.add(smartwatch)

                print(f"已添加老年人: {data['name']} ({data['user_id']}) - 区域: {data['region']}")
                print(f"  地址: {data['address']}")
                print(f"  GPS坐标: {gps_location}")
                print(f"  智能手环: {data['smartwatch_id']}")
                print()

            # 提交所有更改
            db.session.commit()
            print("✅ 所有老年人数据添加成功！")

            # 验证数据
            print("\n验证添加的数据:")
            for user_id in ['E01', 'E02', 'E03']:
                elderly = ElderlyUser.query.filter_by(user_id=user_id).first()
                if elderly and elderly.smartwatch:
                    print(f"✅ {elderly.name} - GPS: {elderly.smartwatch.gps_location}")
                else:
                    print(f"❌ {user_id} 数据不完整")

        except Exception as e:
            db.session.rollback()
            print(f"❌ 添加数据时出错: {str(e)}")
            return False

    return True

if __name__ == '__main__':
    print("开始添加老年人数据...")
    success = add_elderly_data()
    if success:
        print("\n🎉 数据添加完成！现在可以测试地图显示功能了。")
    else:
        print("\n💥 数据添加失败，请检查错误信息。")
