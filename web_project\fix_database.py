#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app, db
    from app.models.models import ElderlyUser, FamilyUser
    
    app = create_app()
    
    with app.app_context():
        print("🔧 修复数据库用户绑定关系...")
        
        # 查找用户
        elderly_e01 = ElderlyUser.query.filter_by(user_id='E01').first()
        family_f01 = FamilyUser.query.filter_by(family_id='F01').first()
        
        if elderly_e01 and family_f01:
            print(f"📋 找到老人: {elderly_e01.name} (ID: {elderly_e01.user_id})")
            print(f"📋 找到家属: {family_f01.name} (ID: {family_f01.family_id})")
            
            # 修复绑定关系
            elderly_e01.bound_family_ids = 'F01,F02'
            family_f01.bound_elderly_id = 'E01'
            
            # 同样修复其他用户
            elderly_e02 = ElderlyUser.query.filter_by(user_id='E02').first()
            family_f03 = FamilyUser.query.filter_by(family_id='F03').first()
            
            if elderly_e02 and family_f03:
                elderly_e02.bound_family_ids = 'F03,F04'
                family_f03.bound_elderly_id = 'E02'
                
            # 修复F02
            family_f02 = FamilyUser.query.filter_by(family_id='F02').first()
            if family_f02:
                family_f02.bound_elderly_id = 'E01'
                
            # 修复F04
            family_f04 = FamilyUser.query.filter_by(family_id='F04').first()
            if family_f04:
                family_f04.bound_elderly_id = 'E02'
            
            db.session.commit()
            print("✅ 用户绑定关系修复成功！")
            
            # 验证修复结果
            print("\n📋 验证修复结果:")
            print(f"老人E01绑定的家属: {elderly_e01.bound_family_ids}")
            print(f"家属F01绑定的老人: {family_f01.bound_elderly_id}")
            
        else:
            print("❌ 未找到用户数据")
            
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
