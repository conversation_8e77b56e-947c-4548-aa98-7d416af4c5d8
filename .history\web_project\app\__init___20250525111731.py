# app包初始化文件
# 此文件使app目录成为一个Python包

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS

# 初始化数据库
db = SQLAlchemy()

def create_app():
    # 创建Flask应用实例
    app = Flask(__name__,
                static_folder='static',
                template_folder='templates')

    # 启用跨域支持
    CORS(app)

    # 加载配置
    app.config.from_object('config.Config')

    # 初始化数据库与应用绑定
    db.init_app(app)

    # 注册蓝图
    from app.api.emergency import emergency_bp as emergency_api_bp
    from app.api.family import family_bp as family_api_bp
    from app.api.auth import auth_bp as auth_api_bp
    from app.api.health_articles import health_articles_bp as health_articles_api_bp
    from app.api.food_orders import food_orders_bp as food_orders_api_bp
    from app.api.worker import worker_bp as worker_api_bp
    from app.routes import main_bp

    app.register_blueprint(emergency_api_bp, url_prefix='/api/emergency')
    app.register_blueprint(family_api_bp, url_prefix='/api/family')
    app.register_blueprint(auth_api_bp, url_prefix='/api/auth')
    app.register_blueprint(health_articles_api_bp, url_prefix='/api/health')
    app.register_blueprint(food_orders_api_bp, url_prefix='/api/food_orders')
    app.register_blueprint(worker_api_bp, url_prefix='/api/worker')
    app.register_blueprint(main_bp)

    return app