    /* 整体布局样式 */
    .elderly-container {
        display: flex;
        min-height: calc(100vh - 56px);
    }

    /* 侧边导航栏样式 */
    .side-nav {
        width: 260px;
        background-color: #f8f9fa;
        border-right: 1px solid #dee2e6;
        transition: all 0.3s;
    }

    /* 侧边栏折叠时的样式 */
    .side-nav.collapsed {
        width: 60px;
    }

    .side-nav.collapsed .nav-text {
        display: none;
    }

    .side-nav.collapsed .nav-item {
        padding: 0.75rem 0;
        text-align: center;
    }

    /* 主内容区样式 */
    .main-content {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
    }

    /* 健康科普中心样式 */
    .sticky-sidebar {
        position: sticky;
        top: 20px;
        height: calc(100vh - 100px);
        overflow-y: auto;
    }

    .scrollable-content {
        max-height: calc(100vh - 100px);
        overflow-y: auto;
    }

    .category-pill {
        display: inline-flex;
        align-items: center;
        padding: 8px 15px;
        margin: 5px;
        border-radius: 20px;
        background-color: #f8f9fa;
        cursor: pointer;
        transition: all 0.2s;
    }

    .category-pill:hover, .category-pill.active {
        background-color: #0d6efd;
        color: white;
    }

    /* 侧边栏导航项 */
    .nav-item {
        padding: 0.75rem 1rem;
        display: flex;
        align-items: center;
        color: #495057;
        border-radius: 0.25rem;
        margin: 0.25rem 0.5rem;
        cursor: pointer;
        transition: all 0.2s;
    }

    .nav-item:hover, .nav-item.active {
        background-color: rgba(13, 110, 253, 0.1);
        color: #0d6efd;
    }

    .nav-item i {
        font-size: 1.25rem;
        min-width: 28px;
    }

    .nav-text {
        margin-left: 10px;
        font-size: 1rem;
    }

    .nav-text.menu-large {
        font-size: 1.18rem;
        font-weight: 600;
        letter-spacing: 1px;
    }

    /* 健康看板样式 */
    .health-dashboard .card {
        height: 100%;
        border-radius: 10px;
        transition: all 0.3s;
    }

    .health-dashboard .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    /* 紧急呼叫按钮样式 */
    .emergency-call-btn {
        background-color: #FF4D4F;
        color: white;
        padding: 40px;
        border-radius: 12px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s;
        box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
    }

    .emergency-call-btn:hover {
        background-color: #ff2b2e;
        transform: scale(1.05);
    }

    .emergency-call-btn i {
        font-size: 2.5rem;
        margin-bottom: 10px;
    }

    /* 设备连接状态卡片 */
    .device-card {
        border-radius: 10px;
        overflow: hidden;
        transition: all 0.3s;
    }

    .device-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .device-icon {
        font-size: 2rem;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* 健康仪表盘 */
    .gauge-container {
        position: relative;
        width: 160px;
        height: 160px;
    }

    /* 悬浮助手 */
    .floating-assistant {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 60px;
        height: 60px;
        background-color: #5B8FF9;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        cursor: pointer;
        z-index: 1000;
        transition: all 0.3s;
    }

    .floating-assistant:hover {
        transform: scale(1.1);
    }

    /* 健康日历 */
    .health-calendar .reminder-item {
        padding: 10px;
        border-radius: 8px;
        margin-bottom: 10px;
        border-left: 4px solid;
    }

    .reminder-medication { border-left-color: #5B8FF9; background-color: rgba(91, 143, 249, 0.1); }
    .reminder-checkup { border-left-color: #6DD400; background-color: rgba(109, 212, 0, 0.1); }
    .reminder-measurement { border-left-color: #FF4D4F; background-color: rgba(255, 77, 79, 0.1); }

    /* 健康档案样式 */
    .health-record-container {
        display: flex;
        gap: 20px;
        font-size: 1.2rem; /* 增加整体字体大小 */
    }

    .health-timeline {
        min-width: 350px;
        max-width: 350px;
        border-right: 1px solid #dee2e6;
        transition: all 0.3s ease;
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        overflow: hidden;
    }

    .health-timeline.collapsed {
        min-width: 50px;
        max-width: 50px;
        overflow: hidden;
    }

    .health-timeline.collapsed .timeline-content {
        display: none;
    }

    .health-timeline.collapsed .timeline-toggle-btn {
        transform: rotate(180deg);
    }

    .timeline-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 0;
    }

    .vertical-text {
        writing-mode: vertical-lr;
        text-align: center;
        white-space: nowrap;
        display: none;
    }

    .health-timeline.collapsed .vertical-text {
        display: block;
    }

    .timeline-toggle-btn {
        background: none;
        border: none;
        cursor: pointer;
        transition: transform 0.3s;
        font-size: 1.2rem;
    }

    /* 医疗时间轴样式 */
    .medical-timeline {
        position: relative;
        padding: 0;
    }

    .medical-timeline-item {
        position: relative;
        display: flex;
        margin-bottom: 25px;
        animation: slideInLeft 0.6s ease-out;
    }

    .medical-timeline-item:last-child {
        margin-bottom: 0;
    }

    @keyframes slideInLeft {
        from {
            opacity: 0;
            transform: translateX(-30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    .timeline-marker-wrapper {
        position: relative;
        margin-right: 15px;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .timeline-marker {
        width: 45px;
        height: 45px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.3rem;
        z-index: 2;
        box-shadow: 0 3px 10px rgba(0,0,0,0.2);
        position: relative;
    }

    .timeline-line {
        width: 3px;
        height: 100%;
        background: linear-gradient(to bottom, #e9ecef, #dee2e6);
        position: absolute;
        top: 45px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 1;
    }

    .medical-timeline-item:last-child .timeline-line {
        display: none;
    }

    .timeline-card {
        flex: 1;
        background: white;
        border-radius: 12px;
        box-shadow: 0 3px 15px rgba(0,0,0,0.08);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .timeline-card:hover {
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        transform: translateY(-3px);
        border-color: #007bff;
    }

    .timeline-card-header {
        padding: 15px 20px 10px;
        border-bottom: 1px solid #f8f9fa;
    }

    .timeline-card-body {
        padding: 15px 20px 20px;
    }

    .timeline-date {
        font-size: 0.9rem;
        color: #6c757d;
        font-weight: 500;
    }

    .medical-info {
        margin-bottom: 15px;
    }

    .info-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;
        font-size: 0.95rem;
    }

    .info-item:last-child {
        margin-bottom: 0;
    }

    .info-label {
        font-weight: 600;
        color: #495057;
        margin-right: 5px;
        min-width: 50px;
    }

    .info-value {
        color: #6c757d;
        line-height: 1.4;
    }

    .timeline-actions {
        border-top: 1px solid #f8f9fa;
        padding-top: 15px;
        margin-top: 15px;
    }

    .add-record-card {
        border: 2px dashed #dee2e6;
        background: #f8f9fa;
        transition: all 0.3s ease;
    }

    .add-record-card:hover {
        border-color: #007bff;
        background: rgba(0, 123, 255, 0.05);
        transform: translateY(-2px);
    }

    .health-data-chart {
        height: 300px;
        width: 100%;
    }

    /* 设备管理样式优化 */
    .device-management {
        animation: fadeInUp 0.6s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* 设备卡片样式 */
    .device-card {
        border-radius: 15px;
        overflow: hidden;
        transition: all 0.3s ease;
        position: relative;
    }

    .device-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    }

    .device-card.device-offline {
        opacity: 0.8;
    }

    .device-icon-large {
        font-size: 2.5rem;
        width: 70px;
        height: 70px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
    }

    /* 设备状态指示器 */
    .device-status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        position: relative;
    }

    .device-status-indicator.online {
        background-color: #52C41A;
    }

    .device-status-indicator.offline {
        background-color: #FF4D4F;
    }

    .pulse-animation {
        animation: pulse-dot 2s infinite;
    }

    @keyframes pulse-dot {
        0% {
            box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(82, 196, 26, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
        }
    }

    /* 设备指标样式 */
    .device-metrics .metric-item {
        padding: 8px;
        border-radius: 8px;
        transition: all 0.2s;
    }

    .device-metrics .metric-item:hover {
        background-color: rgba(0,0,0,0.05);
    }

    .metric-value {
        font-size: 1.1rem;
        font-weight: bold;
        color: #333;
        margin: 2px 0;
    }

    .metric-label {
        font-size: 0.8rem;
        color: #666;
    }

    /* 同步记录时间线样式 */
    .sync-timeline {
        position: relative;
    }

    .timeline-item-enhanced {
        position: relative;
        padding-left: 60px;
        margin-bottom: 30px;
    }

    .timeline-item-enhanced:last-child {
        margin-bottom: 0;
    }

    .timeline-marker {
        position: absolute;
        left: 0;
        top: 0;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        z-index: 2;
    }

    .timeline-item-enhanced:not(:last-child)::before {
        content: '';
        position: absolute;
        left: 19px;
        top: 40px;
        width: 2px;
        height: calc(100% - 10px);
        background: linear-gradient(to bottom, #e9ecef, #dee2e6);
        z-index: 1;
    }

    .timeline-content {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .timeline-content:hover {
        box-shadow: 0 5px 20px rgba(0,0,0,0.12);
        transform: translateY(-2px);
    }

    .sync-details {
        margin-top: 10px;
    }

    .sync-stat-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .sync-stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    /* 健康简报样式 */
    .health-brief-card {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        transition: all 0.3s;
    }

    .health-brief-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 18px rgba(0,0,0,0.1);
    }

    .health-brief-card .card-header {
        padding: 12px 15px;
        border-bottom: none;
        font-size: 1.2rem; /* 增加卡片标题字体大小 */
    }

    .abnormal-indicator {
        position: relative;
        height: 120px;
        margin-bottom: 10px;
    }

    .measure-comparison {
        position: relative;
        height: 120px;
        margin-bottom: 10px;
    }

    .monitoring-reminder {
        background-color: rgba(255, 77, 79, 0.05);
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 10px;
        border-left: 3px solid #FF4D4F;
        font-size: 1.1rem; /* 增加提醒内容字体大小 */
    }

    .monitoring-reminder.warning {
        border-left-color: #FAAD14;
        background-color: rgba(250, 173, 20, 0.05);
    }

    .monitoring-reminder h6 {
        color: #FF4D4F;
        margin-bottom: 8px;
        font-size: 1.15rem; /* 增加提醒标题字体大小 */
    }

    .monitoring-reminder.warning h6 {
        color: #FAAD14;
    }

    .countdown-progress {
        height: 6px;
        margin-top: 8px;
        margin-bottom: 5px;
        background-color: rgba(0,0,0,0.05);
    }

    .countdown-progress .progress-bar {
        background-color: #FF4D4F;
    }

    .device-status-indicator {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 5px;
    }

    .device-status-indicator.online {
        background-color: #52C41A;
    }

    .device-status-indicator.offline {
        background-color: #FF4D4F;
    }

    /* 适老化健康科普中心样式 */
    .elderly-edu-container {
        width: 100%;
        max-width: 1200px;
        margin: 0 auto;
        position: relative;
    }

    .smart-nav {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #fff;
        padding: 15px;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        margin-bottom: 25px;
        flex-wrap: wrap;
        gap: 10px;
    }

    .voice-search {
        display: flex;
        align-items: center;
        padding: 12px 20px;
        background-color: #f8f9fa;
        border: none;
        border-radius: 50px;
        color: #333;
        font-size: 1.1rem;
        cursor: pointer;
        transition: all 0.2s;
    }

    .voice-search:hover {
        background-color: #e9ecef;
    }

    .font-control {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .font-control button {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: 2px solid #dee2e6;
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        transition: all 0.2s;
    }

    .font-control button:hover {
        background-color: #f8f9fa;
        border-color: #adb5bd;
    }

    .read-aloud {
        display: flex;
        align-items: center;
        padding: 10px 20px;
        background-color: #5B8FF9;
        border: none;
        border-radius: 50px;
        color: white;
        cursor: pointer;
        transition: all 0.2s;
    }

    .read-aloud:hover {
        background-color: #4a7de0;
    }

    .edu-reading-area {
        width: 90%;
        margin: 0 auto;
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 15px rgba(0,0,0,0.06);
        padding: 30px;
        margin-bottom: 30px;
    }

    .article-title-large {
        font-size: 36px;
        font-weight: bold;
        color: #333;
        margin-bottom: 20px;
        line-height: 1.3;
    }

    .key-points {
        margin-bottom: 25px;
    }

    .key-point {
        background-color: rgba(91, 143, 249, 0.1);
        border-left: 4px solid #5B8FF9;
        padding: 15px;
        border-radius: 0 8px 8px 0;
        margin-bottom: 12px;
        position: relative;
    }

    .key-point-title {
        font-size: 1.1rem;
        font-weight: bold;
        margin-bottom: 5px;
        color: #333;
    }

    .key-point-content {
        color: #555;
        font-size: 1.05rem;
        line-height: 1.6;
    }

    .key-point-audio {
        position: absolute;
        right: 15px;
        top: 15px;
        background: none;
        border: none;
        color: #5B8FF9;
        cursor: pointer;
    }

    .article-content {
        font-size: var(--user-font-size, 22px);
        line-height: 2;
        letter-spacing: 1.2px;
        color: #333;
        margin-bottom: 30px;
    }

    /* 健康科普模块样式 */
    .health-edu-sidebar {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        height: fit-content;
        position: sticky;
        top: 20px;
    }

    .category-filters .category-btn {
        text-align: left;
        border-radius: 8px;
        transition: all 0.3s ease;
        font-size: 16px;
        padding: 12px 16px;
    }

    .category-filters .category-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .health-articles-container {
        min-height: 400px;
    }

    .article-card {
        border: none;
        border-radius: 12px;
        transition: all 0.3s ease;
        cursor: pointer;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .article-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .article-image {
        width: 80px;
        height: 80px;
        overflow: hidden;
        border-radius: 8px;
        flex-shrink: 0;
    }

    .article-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .article-title {
        color: #333;
        font-size: 16px;
        line-height: 1.4;
        margin-bottom: 8px;
    }

    .article-summary {
        font-size: 14px;
        line-height: 1.5;
        color: #666;
        margin-bottom: 10px;
    }

    .article-meta {
        font-size: 12px;
    }

    .article-meta .badge {
        font-size: 11px;
        padding: 4px 8px;
    }

    /* 文章详情模态窗口样式 */
    .modal-dialog {
        max-width: 800px;
    }

    .modal-body .article-content {
        font-size: 16px;
        line-height: 1.8;
        color: #333;
        text-align: justify;
    }

    /* 分页样式 */
    .pagination .page-link {
        color: #007bff;
        border-color: #dee2e6;
        padding: 8px 16px;
    }

    .pagination .page-item.active .page-link {
        background-color: #007bff;
        border-color: #007bff;
    }

    .pagination .page-link:hover {
        color: #0056b3;
        background-color: #e9ecef;
        border-color: #dee2e6;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .health-edu-sidebar {
            margin-bottom: 20px;
            position: static;
        }

        .article-image {
            width: 60px;
            height: 60px;
        }

        .article-title {
            font-size: 14px;
        }

        .article-summary {
            font-size: 12px;
        }

        .category-filters .category-btn {
            font-size: 14px;
            padding: 10px 12px;
        }
    }

    .content-section {
        margin-bottom: 30px;
    }

    .content-section h3 {
        font-size: 1.5rem;
        margin-bottom: 15px;
        color: #333;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
    }

    .content-section p {
        margin-bottom: 15px;
    }

    .float-tools {
        position: fixed;
        right: 30px;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        flex-direction: column;
        gap: 15px;
        z-index: 100;
    }

    .tool-card {
        width: 70px;
        height: 70px;
        border-radius: 15px;
        background-color: white;
        box-shadow: 0 3px 15px rgba(0,0,0,0.1);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s;
    }

    .tool-card:hover {
        transform: scale(1.1);
        box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    }

    .tool-card img {
        width: 28px;
        height: 28px;
        margin-bottom: 5px;
    }

    .tool-card span {
        font-size: 0.7rem;
        text-align: center;
        color: #666;
    }

    .color-mode-switcher {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
    }

    .color-mode {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        cursor: pointer;
        opacity: 0.6;
        transition: all 0.2s;
    }

    .color-mode:hover, .color-mode.active {
        opacity: 1;
        transform: scale(1.1);
    }

    .color-mode-default {
        background-color: #fff;
        border: 2px solid #dee2e6;
    }

    .color-mode-high-contrast {
        background-color: #000;
        border: 2px solid #fff;
    }

    .color-mode-low-vision {
        background-color: #C7E4F2;
        border: 2px solid #1A3658;
    }

    .home-button {
        position: fixed;
        left: 30px;
        bottom: 30px;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: #5B8FF9;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        box-shadow: 0 3px 15px rgba(0,0,0,0.2);
        cursor: pointer;
        z-index: 100;
        transition: all 0.3s;
    }

    .home-button:hover {
        transform: scale(1.1);
        background-color: #4a7de0;
    }

    .article-difficulty {
        display: inline-flex;
        align-items: center;
        margin-bottom: 15px;
        color: #f1c40f;
    }

    .practice-steps {
        background-color: #f8f9fa;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .practice-steps h4 {
        margin-bottom: 15px;
        color: #333;
    }

    .practice-step {
        display: flex;
        align-items: flex-start;
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px dashed #dee2e6;
    }

    .practice-step:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
    }

    .practice-step-number {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #5B8FF9;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 15px;
        flex-shrink: 0;
    }

    .practice-step-content {
        flex: 1;
    }

    .practice-step-action {
        margin-left: 15px;
    }

    .highlight-sentence {
        background-color: yellow;
        padding: 2px 0;
    }

    /* 分类导航栏样式 */
    .category-nav {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 20px;
        justify-content: center;
        align-items: center;
    }

    .category-item {
        padding: 10px 20px;
        background-color: #f8f9fa;
        border-radius: 50px;
        cursor: pointer;
        transition: all 0.2s;
        font-size: 1.1rem;
        display: flex;
        align-items: center;
    }

    .category-item:hover, .category-item.active {
        background-color: #5B8FF9;
        color: white;
    }

    /* 文章列表样式 */
    .article-card {
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid #e9ecef;
    }

    .article-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        border-color: #5B8FF9;
    }

    .article-thumbnail {
        width: 100%;
        height: 120px;
        object-fit: cover;
        border-radius: 8px;
    }

    .article-meta {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 10px;
    }

    .article-excerpt {
        display: -webkit-box;

        -webkit-box-orient: vertical;
        overflow: hidden;
        line-height: 1.5;
    }

    .article-title-large {
        font-size: 1.8rem;
        font-weight: bold;
        color: #333;
        cursor: pointer;
        transition: color 0.3s;
    }

    .article-title-large:hover {
        color: #5B8FF9;
    }

    .article-text {
        font-size: var(--user-font-size, 18px);
        line-height: 1.8;
        color: #333;
    }

    .article-actions {
        border-top: 1px solid #e9ecef;
        padding-top: 15px;
    }

    /* 分页样式 */
    .pagination .page-link {
        color: #5B8FF9;
        border-color: #dee2e6;
        padding: 10px 15px;
        font-size: 1.1rem;
    }

    .pagination .page-item.active .page-link {
        background-color: #5B8FF9;
        border-color: #5B8FF9;
    }

    .pagination .page-link:hover {
        color: #4a7de0;
        background-color: #f8f9fa;
    }

    /* 功能面板样式 */
    .function-panel {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        padding: 25px;
        z-index: 1000;
        max-width: 500px;
        width: 90%;
        display: none;
    }

    .function-panel.show {
        display: block;
        animation: fadeIn 0.3s ease;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translate(-50%, -48%); }
        to { opacity: 1; transform: translate(-50%, -50%); }
    }

    .panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #eee;
    }

    .panel-header h4 {
        margin: 0;
        color: #333;
        display: flex;
        align-items: center;
    }

    .panel-close {
        background: none;
        border: none;
        font-size: 1.25rem;
        color: #999;
        cursor: pointer;
        transition: all 0.2s;
    }

    .panel-close:hover {
        color: #333;
        transform: scale(1.1);
    }

    .panel-content {
        max-height: 60vh;
        overflow-y: auto;
    }

    .collection-item {
        display: flex;
        padding: 12px 15px;
        border-radius: 8px;
        margin-bottom: 10px;
        background-color: #f8f9fa;
        transition: all 0.2s;
    }

    .collection-item:hover {
        background-color: rgba(91, 143, 249, 0.1);
    }

    .collection-icon {
        margin-right: 15px;
        color: #5B8FF9;
        font-size: 1.5rem;
    }

    .progress-tree {
        position: relative;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 10px;
        text-align: center;
    }

    .progress-tree img {
        max-width: 100%;
        height: 200px;
    }

    .progress-info {
        margin-top: 15px;
    }

    .medication-list {
        list-style: none;
        padding: 0;
    }

    .medication-item {
        display: flex;
        align-items: center;
        padding: 12px;
        border-radius: 8px;
        margin-bottom: 10px;
        background-color: #f8f9fa;
    }

    .medication-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 1.25rem;
    }

    .question-form {
        margin-top: 15px;
    }

    .question-form textarea {
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 15px;
        resize: none;
    }

    .question-form button {
        background-color: #5B8FF9;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        cursor: pointer;
        transition: all 0.2s;
    }

    .question-form button:hover {
        background-color: #4a7de0;
    }

    .overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0,0,0,0.5);
        z-index: 999;
        display: none;
    }

    .overlay.show {
        display: block;
    }

    /* 紧急呼叫浮动按钮样式 */
    .floating-emergency-btn {
        display: none; /* 隐藏浮动紧急呼叫按钮 */
    }

    /* 侧边栏紧急呼叫按钮样式 */
    .emergency-nav-btn {
        background-color: #FF4D4F;
        color: white !important;
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        margin: 1rem 0.5rem;
        border-radius: 0.25rem;
        cursor: pointer;
        transition: all 0.2s;
        box-shadow: 0 2px 8px rgba(255, 77, 79, 0.4);
    }

    .emergency-nav-btn:hover {
        background-color: #ff2b2e;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(255, 77, 79, 0.6);
    }

    .emergency-nav-btn .emergency-icon {
        margin-right: 10px;
        font-size: 1.5rem;
    }

    .emergency-nav-btn .emergency-text {
        display: flex;
        flex-direction: column;
    }

    .emergency-nav-btn .emergency-title {
        font-size: 1.2rem;
        font-weight: bold;
        line-height: 1.2;
    }

    .emergency-nav-btn .emergency-subtitle {
        font-size: 0.8rem;
        opacity: 0.9;
    }

    /* 紧急呼叫动画 */
    .emergency-calling-animation {
        position: relative;
        width: 100px;
        height: 100px;
        margin: 0 auto;
        background-color: rgba(255, 77, 79, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .emergency-calling-animation::before {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background-color: rgba(255, 77, 79, 0.3);
        animation: pulse-outward 1.5s infinite;
    }

    @keyframes pulse-outward {
        0% {
            transform: scale(0.9);
            opacity: 1;
        }
        100% {
            transform: scale(1.5);
            opacity: 0;
        }
    }

    /* 紧急联系人样式 */
    .contact-item {
        transition: all 0.2s;
    }

    .contact-item:hover {
        background-color: #f8f9fa;
        transform: translateY(-2px);
    }

    .floating-order-btns {
        position: fixed;
        bottom: 120px;
        right: 30px;
        display: flex;
        flex-direction: column;
        gap: 18px;
        z-index: 1200;
    }
    .order-btn {
        width: 64px;
        height: 64px;
        background: #fff;
        border-radius: 50%;
        box-shadow: 0 2px 10px rgba(0,0,0,0.12);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #5B8FF9;
        font-weight: bold;
        font-size: 1rem;
        cursor: pointer;
        border: 2px solid #e9ecef;
        transition: all 0.2s;
    }
    .order-btn:hover {
        background: #5B8FF9;
        color: #fff;
        border-color: #5B8FF9;
    }
    .order-btn span {
        font-size: 0.95rem;
        margin-top: 2px;
    }

    /* 添加GPS定位和地图相关样式 */
    .emergency-map-container {
        height: 300px;
        width: 100%;
        border-radius: 8px;
        overflow: hidden;
        margin-top: 20px;
        border: 1px solid #dee2e6;
    }

    .location-info {
        padding: 12px;
        background-color: #f8f9fa;
        border-radius: 8px;
        margin-top: 15px;
        display: flex;
        align-items: center;
        font-size: 1.1rem;
    }

    .location-info i {
        font-size: 1.5rem;
        margin-right: 10px;
        color: #5B8FF9;
    }

    .emergency-status {
        padding: 15px;
        border-radius: 8px;
        margin: 15px 0;
        background-color: rgba(255, 77, 79, 0.1);
        border-left: 4px solid #FF4D4F;
        font-size: 1.2rem;
    }

    .emergency-contacts {
        margin-top: 20px;
    }

    .contact-item {
        margin-bottom: 15px;
        font-size: 1.1rem;
    }

    .emergency-options {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 15px;
    }

    .emergency-option {
        flex: 1;
        min-width: 120px;
        padding: 15px;
        border-radius: 8px;
        text-align: center;
        background-color: #f8f9fa;
        cursor: pointer;
        transition: all 0.2s;
        font-size: 1.1rem;
    }

    .emergency-option:hover {
        background-color: #e9ecef;
        transform: translateY(-3px);
    }

    .emergency-option i {
        font-size: 1.8rem;
        margin-bottom: 8px;
        display: block;
    }

    .emergency-option.medical {
        color: #FF4D4F;
    }

    .emergency-option.fall {
        color: #FAAD14;
    }

    .emergency-option.help {
        color: #5B8FF9;
    }

    /* 添加紧急呼叫模态框样式 */
    .emergency-modal {
        border-radius: 12px;
        overflow: hidden;
    }

    .emergency-modal .modal-header {
        background-color: #FF4D4F;
        color: white;
        padding: 1.2rem;
    }

    .emergency-modal .modal-footer {
        border-top: none;
        padding: 1rem 1.5rem 1.5rem;
    }

    .emergency-buzzer {
        animation: buzzer-flash 0.5s infinite;
    }

    @keyframes buzzer-flash {
        0%, 100% {
            background-color: #FF4D4F;
        }
        50% {
            background-color: #d73435;
        }
    }

    .emergency-countdown {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background-color: #FF4D4F;
        color: white;
        font-size: 2rem;
        font-weight: bold;
        margin: 0 auto;
        animation: pulse-outward 1.5s infinite;
    }

    /* 紧急呼叫状态指示器 */
    .emergency-status-progress {
        height: 10px;
        border-radius: 5px;
        margin-top: 15px;
        margin-bottom: 5px;
        background-color: #eee;
        overflow: hidden;
    }

    .emergency-status-progress .progress-bar {
        background-color: #FF4D4F;
    }

    /* 紧急呼叫选项样式强化 */
    .emergency-options {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 15px;
    }

    .emergency-option {
        flex: 1;
        min-width: 120px;
        padding: 15px;
        border-radius: 8px;
        text-align: center;
        background-color: #f8f9fa;
        cursor: pointer;
        transition: all 0.2s;
        font-size: 1.1rem;
        border: 2px solid transparent;
    }

    .emergency-option:hover, .emergency-option.active {
        background-color: #e9ecef;
        transform: translateY(-3px);
        border-color: #007bff;
    }

    .emergency-option i {
        font-size: 1.8rem;
        margin-bottom: 8px;
        display: block;
    }

    .emergency-option.medical {
        color: #FF4D4F;
    }

    .emergency-option.fall {
        color: #FAAD14;
    }

    .emergency-option.help {
        color: #5B8FF9;
    }

    /* 新增的紧急呼叫样式 */
    .emergency-countdown {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        background-color: #FF4D4F;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.5rem;
        font-weight: bold;
        margin: 0 auto;
        position: relative;
        animation: pulse-animation 1s infinite;
    }

    @keyframes pulse-animation {
        0% {
            box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.7);
        }
        70% {
            box-shadow: 0 0 0 15px rgba(255, 77, 79, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(255, 77, 79, 0);
        }
    }

    .emergency-map-container {
        height: 250px;
        width: 100%;
        border-radius: 10px;
        overflow: hidden;
        margin: 20px 0;
        border: 1px solid #dee2e6;
    }

    .location-info {
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 8px;
        margin: 15px 0;
        display: flex;
        align-items: center;
    }

    .location-info i {
        font-size: 1.8rem;
        margin-right: 15px;
        color: #FF4D4F;
    }

    .emergency-status-progress {
        height: 8px;
        border-radius: 4px;
        margin: 20px 0;
        background-color: #eee;
        overflow: hidden;
    }

    .emergency-status-progress .progress-bar {
        background-color: #FF4D4F;
    }

    /* 健康科普模块新增样式 */
    /* 顶部分类筛选样式 */
    .category-filters-top {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border: 1px solid #e9ecef;
    }

    .category-btn-top {
        transition: all 0.3s ease;
        border-radius: 20px;
        font-size: 14px;
        padding: 8px 16px;
        margin: 2px;
    }

    .category-btn-top:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* 文章详情页样式 */
    .article-title-large {
        font-size: 2.5rem;
        font-weight: bold;
        color: #333;
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .article-difficulty {
        color: #6c757d;
        font-size: 1rem;
        margin-bottom: 2rem;
    }

    .article-difficulty .bi-star-fill {
        color: #ffc107;
    }

    /* 关键知识点样式 */
    .key-points {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 2rem;
    }

    .key-point {
        background: white;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 15px;
        border-left: 4px solid #007bff;
        position: relative;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .key-point:last-child {
        margin-bottom: 0;
    }

    .key-point-title {
        color: #007bff;
        font-size: 1.1rem;
        font-weight: bold;
        margin-bottom: 10px;
    }

    .key-point-content {
        color: #555;
        line-height: 1.6;
        margin-bottom: 0;
    }

    .key-point-audio {
        position: absolute;
        top: 15px;
        right: 15px;
        background: #007bff;
        color: white;
        border: none;
        border-radius: 50%;
        width: 35px;
        height: 35px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .key-point-audio:hover {
        background: #0056b3;
        transform: scale(1.1);
    }

    /* 文章内容样式 */
    .article-content {
        font-size: 1.1rem;
        line-height: 1.8;
        color: #333;
    }

    .content-section {
        margin-bottom: 2rem;
    }

    .content-section h3 {
        color: #007bff;
        font-size: 1.5rem;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #e9ecef;
    }

    .content-section p {
        margin-bottom: 1rem;
    }

    .content-section ul {
        padding-left: 1.5rem;
    }

    .content-section li {
        margin-bottom: 0.5rem;
    }

    /* 实践步骤样式 */
    .practice-steps {
        background: #f8f9fa;
        padding: 25px;
        border-radius: 10px;
        margin: 2rem 0;
    }

    .practice-steps h4 {
        color: #007bff;
        margin-bottom: 20px;
        font-size: 1.3rem;
    }

    .practice-step {
        display: flex;
        align-items: flex-start;
        background: white;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 15px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .practice-step:last-child {
        margin-bottom: 0;
    }

    .practice-step-number {
        background: #007bff;
        color: white;
        width: 35px;
        height: 35px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 15px;
        flex-shrink: 0;
    }

    .practice-step-content {
        flex-grow: 1;
    }

    .practice-step-content strong {
        color: #333;
        font-size: 1.1rem;
        display: block;
        margin-bottom: 5px;
    }

    .practice-step-content p {
        color: #666;
        margin-bottom: 0;
        line-height: 1.5;
    }

    .practice-step-action {
        margin-left: 15px;
    }

    .practice-step-action .btn {
        font-size: 0.9rem;
        padding: 5px 12px;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .article-title-large {
            font-size: 2rem;
        }

        .category-filters-top .d-flex {
            flex-direction: column;
            gap: 10px;
        }

        .category-btn-top {
            width: 100%;
            text-align: center;
        }

        .practice-step {
            flex-direction: column;
            text-align: center;
        }

        .practice-step-number {
            margin: 0 auto 15px auto;
        }

        .practice-step-action {
            margin: 15px 0 0 0;
        }
    }

    /* 新增：顶部分类筛选栏样式 */
    .category-filters-top {
        background-color: #f8f9fa;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .category-btn-top {
        border-radius: 25px;
        transition: all 0.3s ease;
        font-size: 14px;
        padding: 8px 16px;
        margin: 4px;
        border: 1px solid #e9ecef;
        white-space: nowrap;
    }

    .category-btn-top:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .category-btn-top.btn-primary {
        background-color: #007bff;
        border-color: #007bff;
        color: white;
    }

    .category-btn-top.btn-outline-primary {
        color: #007bff;
        background-color: white;
    }

    .category-btn-top.btn-outline-primary:hover {
        background-color: #007bff;
        color: white;
    }

    /* 新增：文章卡片顶部图片布局 */
    .article-image-top {
        position: relative;
        overflow: hidden;
        border-radius: 12px 12px 0 0;
    }

    .article-thumbnail {
        width: 100%;
        height: 250px;
        object-fit: cover;
        transition: transform 0.3s ease;

    }

    .article-card:hover .article-thumbnail {
        transform: scale(1.05);
    }

    /* 新增：微信公众号风格的文章详情页 */
    .wechat-style {
        max-width: 800px;
        margin: 0 auto;
        font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei UI", "Microsoft YaHei", Arial, sans-serif;
    }

    .article-header-image {
        text-align: center;
        margin-bottom: 20px;
    }

    .article-summary-box {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 12px;
        padding: 20px;
        color: white;
        margin-bottom: 25px;
    }

    .summary-header {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
    }

    .summary-text {
        font-size: 14px;
        line-height: 1.6;
        margin: 0;
        opacity: 0.9;
    }

    .health-tip-box {
        background-color: #f0f9ff;
        border-left: 4px solid #0ea5e9;
        border-radius: 8px;
        padding: 16px;
        margin: 20px 0;
        display: flex;
        align-items: flex-start;
    }

    .tip-icon {
        color: #0ea5e9;
        font-size: 20px;
        margin-right: 12px;
        margin-top: 2px;
    }

    .tip-content h6 {
        color: #0369a1;
        margin-bottom: 8px;
        font-weight: 600;
    }

    .tip-content p {
        color: #0c4a6e;
        margin: 0;
        font-size: 14px;
        line-height: 1.5;
    }

    .recommendations-box {
        background-color: #f0fdf4;
        border-radius: 12px;
        padding: 20px;
        margin: 25px 0;
    }

    .recommendations-box h5 {
        color: #166534;
        margin-bottom: 15px;
        font-weight: 600;
    }

    .recommendation-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .recommendation-list li {
        position: relative;
        padding-left: 25px;
        margin-bottom: 10px;
        color: #15803d;
        font-size: 14px;
        line-height: 1.5;
    }

    .recommendation-list li:before {
        content: "✓";
        position: absolute;
        left: 0;
        top: 0;
        color: #22c55e;
        font-weight: bold;
    }

    .content-text {
        font-size: 18px;
        line-height: 1.8;
        color: #374151;
        text-align: justify;
    }

    .content-text p {
        margin-bottom: 18px;
        font-size: 18px;
    }

    /* 新增：文章分段样式 */
    .article-section {
        margin-bottom: 35px;
        padding-bottom: 25px;
        border-bottom: 1px solid #e5e7eb;
    }

    .article-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .section-title {
        color: #1f2937;
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 18px;
        padding-left: 15px;
        border-left: 4px solid #3b82f6;
        line-height: 1.4;
    }

    .section-content {
        font-size: 18px;
        line-height: 1.8;
        color: #374151;
        text-align: justify;
    }

    .section-content p {
        margin-bottom: 18px;
        font-size: 18px;
    }

    .section-content p:last-child {
        margin-bottom: 0;
    }

