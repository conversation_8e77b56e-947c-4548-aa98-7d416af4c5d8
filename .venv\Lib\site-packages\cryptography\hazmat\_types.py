# This file is dual licensed under the terms of the Apache License, Version
# 2.0, and the BSD License. See the LICENSE file in the root of this repository
# for complete details.

import typing

from cryptography.hazmat.primitives.asymmetric import (
    dsa,
    ec,
    ed25519,
    ed448,
    rsa,
)


_PUBLIC_KEY_TYPES = typing.Union[
    dsa.DSAPublicKey,
    rsa.RSAPublicKey,
    ec.EllipticCurvePublicKey,
    ed25519.Ed25519<PERSON><PERSON><PERSON><PERSON><PERSON>,
    ed448.Ed448P<PERSON><PERSON><PERSON><PERSON>,
]
_PRIVATE_KEY_TYPES = typing.Union[
    ed25519.Ed25519<PERSON>rivate<PERSON><PERSON>,
    ed448.Ed448<PERSON><PERSON><PERSON><PERSON><PERSON>,
    rsa.RSAPrivateKey,
    dsa.DSAPrivateKey,
    ec.EllipticCurvePrivateKey,
]
