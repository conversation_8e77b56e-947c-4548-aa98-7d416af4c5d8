# -*- coding: utf-8 -*-
from app import create_app, db
import traceback

# 创建应用实例
app = create_app()

if __name__ == '__main__':
    try:
        # 创建所有数据库表
        print("正在创建数据库表...")
        with app.app_context():
            db.create_all()
            print("数据库表创建成功！")
        print("正在启动应用...")
        app.run(debug=True)
    except Exception as e:
        print(f"启动失败: {e}")
        traceback.print_exc() 