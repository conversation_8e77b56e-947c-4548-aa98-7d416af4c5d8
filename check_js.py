#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查JavaScript文件内容
"""

def check_js_file():
    """检查JavaScript文件是否包含健康科普功能"""
    try:
        with open('web_project/app/static/js/elderly_center.js', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键功能
        checks = [
            ('健康科普数据', 'selectedCategory' in content and 'articles' in content),
            ('加载文章方法', 'loadHealthArticles' in content),
            ('分类选择方法', 'selectCategory' in content),
            ('文章详情方法', 'showArticleDetail' in content),
            ('收藏功能', 'toggleFavorite' in content),
            ('分页功能', 'changePage' in content)
        ]
        
        print("JavaScript文件检查结果:")
        for check_name, result in checks:
            status = "✅" if result else "❌"
            print(f"{status} {check_name}: {result}")
        
        # 统计行数
        lines = content.split('\n')
        print(f"\nJavaScript文件总行数: {len(lines)}")
        
        # 查找健康科普相关行
        health_edu_lines = []
        for i, line in enumerate(lines, 1):
            if any(keyword in line for keyword in ['selectedCategory', 'articles', 'loadHealthArticles', 'health_edu']):
                health_edu_lines.append(f"第{i}行: {line.strip()}")
        
        print(f"\n找到 {len(health_edu_lines)} 行包含健康科普相关内容:")
        for line in health_edu_lines[:10]:  # 只显示前10行
            print(line)
        
        if len(health_edu_lines) > 10:
            print(f"... 还有 {len(health_edu_lines) - 10} 行")
            
    except Exception as e:
        print(f"检查失败: {e}")

if __name__ == "__main__":
    check_js_file()
