from flask import Blueprint, request, jsonify, session, redirect, url_for
from app import db
from app.models.models import <PERSON>ly<PERSON><PERSON>, Family<PERSON>ser, CommunityWorker, HealthRecord, Smartwatch
import random
import string

# 创建蓝图
auth_bp = Blueprint('auth_api', __name__)

@auth_bp.route('/register', methods=['POST'])
def register():
    """用户注册API"""
    data = request.json
    user_type = data.get('user_type')

    try:
        if user_type == 'elderly':
            # 生成ID和健康记录ID
            user_id = generate_id('E')
            health_record_id = f'HR{random.randint(100, 999)}'
            smartwatch_id = f'SW{random.randint(100, 999)}'

            # 创建健康记录
            health_record = HealthRecord(
                record_id=health_record_id,
                blood_pressure='120/80',
                blood_sugar='5.6',
                update_time=None  # 设置为当前日期
            )
            db.session.add(health_record)

            # 创建智能手环记录
            smartwatch = Smartwatch(
                watch_id=smartwatch_id,
                bound_user_id=user_id,
                gps_location='30.2729,120.1626',  # 默认杭州位置
                heart_rate=75,
                sleep_score=80,
                step_count=0,
                battery=100
            )
            db.session.add(smartwatch)

            # 创建老年人用户
            elderly = ElderlyUser(
                user_id=user_id,
                password=data.get('password', '123456'),
                name=data.get('name', ''),
                age=data.get('age', 70),
                phone=data.get('phone', ''),
                address=data.get('address', '浙江省杭州市余杭区'),
                emergency_contact_name=data.get('emergencyContactName', ''),
                emergency_contact_phone=data.get('emergencyContactPhone', ''),
                health_record_id=health_record_id,
                smartwatch_id=smartwatch_id,
                bound_family_ids=''
            )
            db.session.add(elderly)
            db.session.commit()

            return jsonify({
                'success': True,
                'message': '注册成功',
                'user_id': user_id
            }), 201

        elif user_type == 'family':
            # 生成ID
            user_id = generate_id('F')

            # 创建家属用户
            family = FamilyUser(
                family_id=user_id,
                password=data.get('password', '123456'),
                name=data.get('name', ''),
                relationship=data.get('relationship', '子女'),
                phone=data.get('phone', ''),
                bound_elderly_id=None  # 初始化为NULL，而不是空字符串
            )
            db.session.add(family)

            # 处理绑定的老年人ID，可能是多个ID用逗号分隔
            elderly_ids_str = data.get('boundElderlyId', '')
            if elderly_ids_str:
                elderly_ids = [eid.strip() for eid in elderly_ids_str.split(',')]
                # 保存第一个有效ID作为主绑定ID
                first_valid_id = None

                for elderly_id in elderly_ids:
                    if not elderly_id:  # 跳过空ID
                        continue

                    elderly = ElderlyUser.query.filter_by(user_id=elderly_id).first()
                    if elderly:
                        # 记录第一个有效ID
                        if not first_valid_id:
                            first_valid_id = elderly_id
                            family.bound_elderly_id = elderly_id

                        # 更新老年人的家属绑定记录
                        if elderly.bound_family_ids:
                            family_ids = elderly.bound_family_ids.split(',')
                            if user_id not in family_ids:
                                elderly.bound_family_ids += f',{user_id}'
                        else:
                            elderly.bound_family_ids = user_id

            db.session.commit()

            return jsonify({
                'success': True,
                'message': '注册成功',
                'user_id': user_id
            }), 201

        elif user_type == 'worker':
            # 生成ID
            region = data.get('region', 'A')
            user_id = f'W_{region}{random.randint(100, 999)}'

            # 创建社区工作人员
            worker = CommunityWorker(
                worker_id=user_id,
                password=data.get('password', '123456'),  # 添加密码字段
                name=data.get('name', ''),
                region=region,
                phone=data.get('phone', '')
            )
            db.session.add(worker)
            db.session.commit()

            return jsonify({
                'success': True,
                'message': '注册成功',
                'user_id': user_id
            }), 201

        else:
            return jsonify({
                'error': '无效的用户类型'
            }), 400

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'error': f'注册失败: {str(e)}'
        }), 500

@auth_bp.route('/login', methods=['POST'])
def login():
    """用户登录API - 支持多端同时登录"""
    data = request.json
    user_type = data.get('user_type')
    user_id = data.get('user_id')
    password = data.get('password')

    try:
        if user_type == 'elderly':
            user = ElderlyUser.query.filter_by(user_id=user_id).first()
        elif user_type == 'family':
            user = FamilyUser.query.filter_by(family_id=user_id).first()
        elif user_type == 'worker':
            user = CommunityWorker.query.filter_by(worker_id=user_id).first()
        else:
            return jsonify({'error': '无效的用户类型'}), 400

        if not user or user.password != password:
            return jsonify({'error': '用户ID或密码错误'}), 401

        # 🔧 使用不同的session键，支持多端同时登录
        session_key_id = f'{user_type}_user_id'
        session_key_type = f'{user_type}_user_type'
        session[session_key_id] = user_id
        session[session_key_type] = user_type

        print(f"🔐 用户登录成功: {user_type} - {user_id}")
        print(f"📝 Session键: {session_key_id} = {user_id}")

        # 根据用户类型返回不同的重定向URL
        redirect_url = '/'
        if user_type == 'elderly':
            redirect_url = '/elderly'
        elif user_type == 'family':
            redirect_url = '/family'
        elif user_type == 'worker':
            redirect_url = '/worker'

        return jsonify({
            'success': True,
            'message': '登录成功',
            'redirect_url': redirect_url,
            'user_id': user_id
        }), 200

    except Exception as e:
        return jsonify({'error': f'登录失败: {str(e)}'}), 500

@auth_bp.route('/current_user', methods=['GET'])
def get_current_user():
    """获取当前登录用户信息 - 支持多端同时登录"""
    # 从请求参数获取用户类型，如果没有则尝试所有类型
    requested_user_type = request.args.get('user_type')

    if requested_user_type:
        # 如果指定了用户类型，直接查找对应的session
        session_key_id = f'{requested_user_type}_user_id'
        session_key_type = f'{requested_user_type}_user_type'
        user_id = session.get(session_key_id)
        user_type = session.get(session_key_type)

        print(f"🔍 查找指定用户类型: {requested_user_type}")
        print(f"📝 Session键: {session_key_id} = {user_id}")
    else:
        # 如果没有指定，尝试查找所有可能的用户类型
        user_id = None
        user_type = None

        for utype in ['elderly', 'family', 'worker']:
            session_key_id = f'{utype}_user_id'
            session_key_type = f'{utype}_user_type'
            temp_user_id = session.get(session_key_id)
            temp_user_type = session.get(session_key_type)

            if temp_user_id and temp_user_type:
                user_id = temp_user_id
                user_type = temp_user_type
                print(f"🔍 找到登录用户: {user_type} - {user_id}")
                break

    if not user_id or not user_type:
        return jsonify({
            'logged_in': False,
            'message': '未登录'
        }), 401

    try:
        if user_type == 'elderly':
            user = ElderlyUser.query.filter_by(user_id=user_id).first()
        elif user_type == 'family':
            user = FamilyUser.query.filter_by(family_id=user_id).first()
        elif user_type == 'worker':
            user = CommunityWorker.query.filter_by(worker_id=user_id).first()
        else:
            return jsonify({'error': '无效的用户类型'}), 400

        if not user:
            return jsonify({'error': '用户不存在'}), 404

        return jsonify({
            'logged_in': True,
            'user_id': user_id,
            'user_type': user_type,
            'name': user.name
        }), 200

    except Exception as e:
        return jsonify({'error': f'获取用户信息失败: {str(e)}'}), 500

@auth_bp.route('/logout', methods=['POST'])
def logout():
    """用户退出登录"""
    try:
        # 清除session
        session.clear()
        return jsonify({
            'success': True,
            'message': '退出成功',
            'redirect_url': '/'
        }), 200
    except Exception as e:
        return jsonify({'error': f'退出失败: {str(e)}'}), 500

def generate_id(prefix):
    """生成唯一ID"""
    # 生成6位随机字符（数字和字母组合）
    random_chars = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
    return f"{prefix}{random_chars}"
