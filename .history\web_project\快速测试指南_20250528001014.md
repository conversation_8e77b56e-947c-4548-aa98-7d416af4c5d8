# 🚨 紧急呼叫系统快速测试指南

## 🔧 重要更新：支持多端同时登录

现在系统支持在同一浏览器的不同标签页中同时登录不同类型的用户：
- **标签页1**：老年端（E01登录）
- **标签页2**：家属端（F01登录）
- **标签页3**：工作人员端（W_A01登录）

## 🎯 完整测试步骤

### 方法1：使用多端登录测试页面（推荐）

1. **打开测试页面**
   ```
   http://127.0.0.1:5000/test_multi_login.html
   ```

2. **分别登录各端**
   - 老年端：E01 / 123456
   - 家属端：F01 / 123456
   - 工作人员端：W_A01 / 123456

3. **验证同时登录**
   - 点击"检查所有登录状态"
   - 确保三端都显示"已登录"

4. **测试紧急呼叫**
   - 点击"老年端发起紧急呼叫"
   - 观察日志确认发送成功

### 方法2：使用实际页面测试

1. **标签页1：老年端**
   ```
   http://127.0.0.1:5000/elderly
   ```
   - 登录：E01 / 123456
   - 点击紧急呼叫按钮

2. **标签页2：家属端**
   ```
   http://127.0.0.1:5000/family
   ```
   - 登录：F01 / 123456
   - 进入"应急响应中心"
   - 等待接收紧急通知

### 方法3：快速测试弹窗功能

1. **打开家属端页面**
   ```
   http://127.0.0.1:5000/family
   ```

2. **进入应急响应中心**
   - 点击左侧菜单的"应急响应中心"

3. **测试紧急弹窗功能**
   - 点击右上角的"**测试紧急弹窗**"按钮（黄色按钮）
   - 立即会看到：
     - ✅ 紧急事件弹窗通知
     - ✅ 声音提醒
     - ✅ 浏览器通知（如果授权）
     - ✅ 模拟紧急事件添加到列表

4. **测试响应功能**
   - 在弹出的紧急事件卡片中
   - 点击"立即响应"按钮
   - 事件状态会变为"已处理"

## 🔧 按钮说明

### 测试紧急弹窗（黄色按钮）
- 创建模拟紧急事件
- 立即显示弹窗通知
- 测试完整的通知流程

### 测试API（灰色按钮）
- 测试服务器API连接
- 如果API失败，会自动调用测试弹窗功能

### 刷新（蓝色按钮）
- 手动刷新紧急事件列表
- 从服务器获取最新数据

## 🎉 预期效果

当您点击"测试紧急弹窗"按钮后，应该看到：

1. **弹窗通知**：
   ```
   🚨 紧急呼叫通知！

   老人姓名：张建国
   事件类型：医疗紧急
   发生时间：[当前时间]
   位置信息：杭州市西湖区西溪路518号

   请立即查看应急响应中心并进行处理！
   ```

2. **事件卡片**：
   - 红色边框的紧急事件卡片
   - 显示事件详情
   - "立即响应"按钮

3. **声音提醒**：
   - 播放提示音（如果浏览器支持）

4. **浏览器通知**：
   - 系统级通知（需要授权）

## 🔍 故障排除

### 如果看不到按钮
1. 确保访问的是 `http://127.0.0.1:5000/family`
2. 点击左侧菜单的"应急响应中心"
3. 按钮应该在右上角显示

### 如果弹窗不出现
1. 检查浏览器控制台是否有错误
2. 确保JavaScript已启用
3. 尝试刷新页面

### 如果没有声音
- 浏览器可能阻止了音频播放
- 这是正常的，弹窗通知仍然会显示

## 📱 完整测试流程

1. **家属端测试**：
   - 访问 `/family`
   - 进入应急响应中心
   - 点击"测试紧急弹窗"
   - 查看弹窗和事件卡片
   - 点击"立即响应"

2. **老年端测试**（如果服务器运行）：
   - 访问 `/elderly`
   - 点击紧急呼叫按钮
   - 选择紧急类型
   - 家属端应该收到通知

## 🎯 核心功能验证

✅ **弹窗通知** - 点击测试按钮立即显示
✅ **事件列表** - 模拟事件添加到列表
✅ **响应功能** - 点击响应按钮更新状态
✅ **实时轮询** - 每10秒自动检查（后台运行）
✅ **声音提醒** - 播放提示音
✅ **浏览器通知** - 系统级通知

---

**现在就去测试吧！访问 `http://127.0.0.1:5000/family` 并点击"测试紧急弹窗"按钮！**
