{% extends "base.html" %}

{% block title %}老年人中心 - 居家养老健康管理系统{% endblock %}

{% block head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/elderly_center.css') }}?v=2024010602">
<link rel="stylesheet" href="{{ url_for('static', filename='css/food_order_styles.css') }}?v=2024010602">
<link rel="stylesheet" href="{{ url_for('static', filename='css/emergency_call_styles.css') }}?v=2024010602">
{% endblock %}

{% block content %}
<div id="elderly-app" class="elderly-container">
    <!-- 侧边导航栏 -->
    <div class="side-nav" :class="{ 'collapsed': navCollapsed }">
        <div class="p-3 d-flex justify-content-between align-items-center">
            <h5 v-if="!navCollapsed" class="mb-0">老年人中心</h5>
            <button class="btn btn-sm btn-light" @click="toggleNav">
                <i class="bi" :class="navCollapsed ? 'bi-chevron-right' : 'bi-chevron-left'"></i>
            </button>
        </div>

        <div class="nav-items mt-2">
            <div class="nav-item" :class="{ 'active': currentTab === 'health' }" @click="changeTab('health')">
                <i class="bi bi-heart-pulse-fill"></i>
                <span class="nav-text menu-large">健康看板</span>
            </div>
            <div class="nav-item" :class="{ 'active': currentTab === 'devices' }" @click="changeTab('devices')">
                <i class="bi bi-smartwatch"></i>
                <span class="nav-text menu-large">设备管理</span>
            </div>
            <div class="nav-item" :class="{ 'active': currentTab === 'services' }" @click="changeTab('services')">
                <i class="bi bi-cup-hot-fill"></i>
                <span class="nav-text menu-large">订餐服务</span>
            </div>
            <div class="nav-item" :class="{ 'active': currentTab === 'profile' }" @click="changeTab('profile')">
                <i class="bi bi-person-vcard-fill"></i>
                <span class="nav-text menu-large">个人档案</span>
            </div>
            <div class="nav-item" :class="{ 'active': currentTab === 'health_edu' }" @click="changeTab('health_edu')">
                <i class="bi bi-book-half"></i>
                <span class="nav-text menu-large">健康科普</span>
            </div>

            <!-- 新增紧急呼叫按钮 -->
            <div class="emergency-nav-btn" @click="showEmergencyModal()">
                <div class="emergency-icon">
                    <i class="bi bi-telephone-fill"></i>
                </div>
                <div class="emergency-text">
                    <span class="emergency-title">紧急呼叫</span>
                    <span class="emergency-subtitle">Emergency Call</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 健康看板 -->
        <div v-if="currentTab === 'health'" class="health-dashboard">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="fw-bold">健康看板</h2>
                <div>
                    <button class="btn btn-outline-primary me-2">
                        <i class="bi bi-printer me-1"></i> 打印报告
                    </button>
                    <button class="btn btn-outline-secondary">
                        <i class="bi bi-grid-3x3-gap me-1"></i> 自定义布局
                    </button>
                </div>
            </div>

            <!-- 上部区域：运动、睡眠、饮食情况 -->
            <div class="row mb-4">
                <!-- 运动情况 -->
                <div class="col-md-4 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                            <h4 class="mb-0 fw-bold">运动情况</h4>
                            <i class="bi bi-person-walking fs-3"></i>
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-4">
                                <div class="position-relative d-inline-block">
                                    <canvas id="stepsProgressChart" width="180" height="180"></canvas>
                                    <div class="position-absolute top-50 start-50 translate-middle text-center">
                                        <h2 class="mb-0 fs-1">5,246</h2>
                                        <span class="fs-5">今日步数</span>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between border-bottom pb-2 mb-2">
                                <span class="fs-5">目标步数</span>
                                <span class="fs-5 fw-bold">8,000 步</span>
                            </div>
                            <div class="d-flex justify-content-between border-bottom pb-2 mb-2">
                                <span class="fs-5">活动时长</span>
                                <span class="fs-5 fw-bold">45 分钟</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span class="fs-5">消耗热量</span>
                                <span class="fs-5 fw-bold">156 千卡</span>
                            </div>

                            <div class="alert alert-info mt-3">
                                <i class="bi bi-info-circle-fill me-2"></i>
                                <span>建议每天至少走7000步，有助于心血管健康</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 睡眠情况 -->
                <div class="col-md-4 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                            <h4 class="mb-0 fw-bold">睡眠情况</h4>
                            <i class="bi bi-moon-stars-fill fs-3"></i>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="fw-bold mb-0">昨晚睡眠</h5>
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-star-fill text-warning me-1"></i>
                                    <span class="fs-5 fw-bold">7.5</span>
                                    <span class="fs-6 text-muted">/10</span>
                                </div>
                            </div>

                            <div class="sleep-phases mb-3">
                                <canvas id="sleepPhaseChart" height="100"></canvas>
                            </div>

                            <div class="row text-center mt-2">
                                <div class="col-4">
                                    <h2 class="text-primary mb-0">7.2</h2>
                                    <span class="fs-6">睡眠时长(小时)</span>
                                </div>
                                <div class="col-4">
                                    <h2 class="text-success mb-0">93%</h2>
                                    <span class="fs-6">睡眠效率</span>
                                </div>
                                <div class="col-4">
                                    <h2 class="text-info mb-0">2</h2>
                                    <span class="fs-6">起床次数</span>
                                </div>
                            </div>

                            <div class="alert alert-success mt-3">
                                <i class="bi bi-check-circle-fill me-2"></i>
                                <span>深度睡眠时间充足，有助于身体恢复</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 饮食情况 -->
                <div class="col-md-4 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                            <h4 class="mb-0 fw-bold">饮食情况</h4>
                            <i class="bi bi-cup-hot-fill fs-3"></i>
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-3">
                                <canvas id="nutritionChart" height="150"></canvas>
                            </div>

                            <div class="d-flex justify-content-around text-center mb-3">
                                <div>
                                    <h5 class="text-primary mb-1">蛋白质</h5>
                                    <span class="badge bg-primary p-2 fs-6">65g</span>
                                </div>
                                <div>
                                    <h5 class="text-success mb-1">碳水</h5>
                                    <span class="badge bg-success p-2 fs-6">220g</span>
                                </div>
                                <div>
                                    <h5 class="text-warning mb-1">脂肪</h5>
                                    <span class="badge bg-warning p-2 fs-6">45g</span>
                                </div>
                            </div>

                            <div class="border-top pt-3">
                                <h5 class="fw-bold mb-2">今日饮食建议</h5>
                                <ul class="mb-0">
                                    <li class="mb-1">增加膳食纤维摄入，可多吃蔬菜水果</li>
                                    <li class="mb-1">降低钠盐摄入，控制在5g以内</li>
                                    <li>补充钙质，建议饮用一杯低脂牛奶</li>
                                </ul>
                            </div>

                            <div class="alert alert-warning mt-3">
                                <i class="bi bi-droplet-fill me-2"></i>
                                <span>今日水分摄入不足，建议多饮水</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 中部区域：核心健康数据 -->
            <div class="row mb-4">
                <!-- 实时健康数据 -->
                <div class="col-md-6 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-primary text-white">
                            <h4 class="mb-0 fw-bold">实时健康数据</h4>
                        </div>
                        <div class="card-body">
                            <div class="d-flex flex-column align-items-center">
                                <div class="gauge-container mb-3">
                                    <canvas id="heartRateGauge" width="160" height="160"></canvas>
                                    <div class="position-absolute top-50 start-50 translate-middle text-center">
                                        <h2 class="mb-0 fs-1">72</h2>
                                        <span class="fs-5">心率/分钟</span>
                                    </div>
                                </div>

                                <div class="row w-100 mt-3">
                                    <div class="col-6">
                                        <div class="d-flex flex-column align-items-center p-3 border rounded">
                                            <h5 class="text-primary mb-2">血压</h5>
                                            <h3 class="mb-0">120/80</h3>
                                            <span class="text-success fs-5">正常</span>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="d-flex flex-column align-items-center p-3 border rounded">
                                            <h5 class="text-primary mb-2">血糖</h5>
                                            <h3 class="mb-0">5.6</h3>
                                            <span class="text-success fs-5">正常</span>
                                        </div>
                                    </div>
                                </div>

                                <p class="text-center mt-3 fs-5 text-muted">
                                    最后更新时间: 2025-05-06 10:30
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 健康日历 -->
                <div class="col-md-6 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-success text-white">
                            <h4 class="mb-0 fw-bold">用药提醒</h4>
                        </div>
                        <div class="card-body health-calendar">
                            <h5 class="mb-3 fw-bold">今日提醒</h5>

                            <div class="reminder-item reminder-medication p-3 mb-3">
                                <div class="d-flex justify-content-between">
                                    <strong class="fs-5">高血压药物</strong>
                                    <span class="fs-5">08:00</span>
                                </div>
                                <span>服用1粒，饭后服用</span>
                            </div>

                            <div class="reminder-item reminder-measurement p-3 mb-3">
                                <div class="d-flex justify-content-between">
                                    <strong class="fs-5">血压测量</strong>
                                    <span class="fs-5">10:00</span>
                                </div>
                                <span>早晨测量血压，记录数值</span>
                            </div>

                            <div class="reminder-item reminder-medication p-3 mb-3">
                                <div class="d-flex justify-content-between">
                                    <strong class="fs-5">高血压药物</strong>
                                    <span class="fs-5">18:00</span>
                                </div>
                                <span>服用1粒，饭后服用</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 下部区域：异常指标简报 -->
            <div class="card shadow-sm mb-4">
                        <div class="card-header bg-danger text-white">
                            <h4 class="mb-0 fw-bold">异常指标简报板</h4>
                        </div>
                        <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="fw-bold"><i class="bi bi-activity me-2"></i>今日血压波动曲线</h5>
                            <div class="abnormal-indicator">
                                <canvas id="bloodPressureFluctuation"></canvas>
                            </div>
                            <div class="alert alert-danger py-3 fs-5">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                10:45-11:30 期间血压偏高，建议休息
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5 class="mt-md-0 mt-4 fw-bold"><i class="bi bi-bar-chart-fill me-2"></i>最近三次测量对比</h5>
                            <div class="measure-comparison">
                                <canvas id="comparisionChart"></canvas>
                            </div>
                            <div class="text-center mt-3">
                                <button class="btn btn-outline-primary fs-5">
                                    <i class="bi bi-clipboard-data me-1"></i> 查看完整报告
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设备管理 -->
        <div v-if="currentTab === 'devices'" class="device-management">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="fw-bold mb-1">智能设备管理中心</h2>
                    <p class="text-muted mb-0">管理您的健康监测设备，实时同步健康数据</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-info">
                        <i class="bi bi-wifi me-1"></i> 设备扫描
                    </button>
                    <button class="btn btn-primary">
                        <i class="bi bi-plus-lg me-1"></i> 添加新设备
                    </button>
                </div>
            </div>



            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="device-card card border-0 shadow-lg">
                        <div class="card-header device-header-smartwatch text-white border-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0 fw-bold">智能手环</h6>
                                <div class="device-status-indicator online pulse-animation"></div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="device-icon-large bg-primary bg-opacity-10 text-primary me-3">
                                    <i class="bi bi-smartwatch"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <span class="fw-bold text-success">
                                            <i class="bi bi-check-circle-fill me-1"></i> 已连接
                                        </span>
                                        <span class="badge bg-success">在线</span>
                                    </div>
                                    <div class="device-info">
                                        <small class="text-muted d-block">型号: Mi Band 7</small>
                                        <small class="text-muted d-block">电量: 78%</small>
                                        <small class="text-muted">最后同步: 5分钟前</small>
                                    </div>
                                </div>
                            </div>
                            <div class="device-metrics row text-center">
                                <div class="col-4">
                                    <div class="metric-item">
                                        <i class="bi bi-heart-pulse text-danger"></i>
                                        <div class="metric-value">72</div>
                                        <div class="metric-label">心率</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="metric-item">
                                        <i class="bi bi-speedometer text-info"></i>
                                        <div class="metric-value">5246</div>
                                        <div class="metric-label">步数</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="metric-item">
                                        <i class="bi bi-moon-stars text-primary"></i>
                                        <div class="metric-value">7.2h</div>
                                        <div class="metric-label">睡眠</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer bg-light border-0">
                            <div class="d-flex gap-2">
                                <button class="btn btn-sm btn-primary flex-fill">
                                    <i class="bi bi-arrow-repeat me-1"></i> 同步数据
                                </button>
                                <button class="btn btn-sm btn-outline-secondary">
                                    <i class="bi bi-gear"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-4">
                    <div class="device-card card border-0 shadow-lg">
                        <div class="card-header device-header-bloodpressure text-white border-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0 fw-bold">智能血压计</h6>
                                <div class="device-status-indicator online pulse-animation"></div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="device-icon-large bg-success bg-opacity-10 text-success me-3">
                                    <i class="bi bi-activity"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <span class="fw-bold text-success">
                                            <i class="bi bi-check-circle-fill me-1"></i> 已连接
                                        </span>
                                        <span class="badge bg-success">在线</span>
                                    </div>
                                    <div class="device-info">
                                        <small class="text-muted d-block">型号: Omron HEM-7136</small>
                                        <small class="text-muted d-block">电量: 92%</small>
                                        <small class="text-muted">最后同步: 2小时前</small>
                                    </div>
                                </div>
                            </div>
                            <div class="device-metrics row text-center">
                                <div class="col-6">
                                    <div class="metric-item">
                                        <i class="bi bi-heart text-danger"></i>
                                        <div class="metric-value">120/80</div>
                                        <div class="metric-label">血压</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="metric-item">
                                        <i class="bi bi-speedometer2 text-success"></i>
                                        <div class="metric-value">正常</div>
                                        <div class="metric-label">状态</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer bg-light border-0">
                            <div class="d-flex gap-2">
                                <button class="btn btn-sm btn-success flex-fill">
                                    <i class="bi bi-arrow-repeat me-1"></i> 同步数据
                                </button>
                                <button class="btn btn-sm btn-outline-secondary">
                                    <i class="bi bi-gear"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-4">
                    <div class="device-card card border-0 shadow-lg device-offline">
                        <div class="card-header device-header-glucose text-white border-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0 fw-bold">血糖仪</h6>
                                <div class="device-status-indicator offline"></div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="device-icon-large bg-warning bg-opacity-10 text-warning me-3">
                                    <i class="bi bi-droplet"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <span class="fw-bold text-danger">
                                            <i class="bi bi-x-circle-fill me-1"></i> 未连接
                                        </span>
                                        <span class="badge bg-danger">离线</span>
                                    </div>
                                    <div class="device-info">
                                        <small class="text-muted d-block">型号: Accu-Chek Guide</small>
                                        <small class="text-muted d-block">电量: 未知</small>
                                        <small class="text-muted">最后同步: 2天前</small>
                                    </div>
                                </div>
                            </div>
                            <div class="device-metrics row text-center">
                                <div class="col-12">
                                    <div class="metric-item">
                                        <i class="bi bi-exclamation-triangle text-warning"></i>
                                        <div class="metric-value text-muted">--</div>
                                        <div class="metric-label">设备离线</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer bg-light border-0">
                            <div class="d-flex gap-2">
                                <button class="btn btn-sm btn-warning flex-fill">
                                    <i class="bi bi-bluetooth me-1"></i> 重新连接
                                </button>
                                <button class="btn btn-sm btn-outline-secondary">
                                    <i class="bi bi-gear"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据同步记录 -->
            <div class="card mt-4 border-0 shadow-lg">
                <div class="card-header  <div class="card-header device-header-glucose text-white border-0"> text-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-0 fw-bold">数据同步记录</h5>
                            <small class="opacity-75">实时监控设备数据同步状态</small>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-light">
                                <i class="bi bi-arrow-clockwise me-1"></i> 刷新
                            </button>
                            <button class="btn btn-sm btn-outline-light">
                                <i class="bi bi-funnel me-1"></i> 筛选
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 水平同步记录时间线 -->
                    <div class="horizontal-sync-timeline">
                        <div class="timeline-scroll-container">
                            <div class="horizontal-timeline-track">
                                <!-- 时间线项目1 -->
                                <div class="horizontal-timeline-item">
                                    <div class="timeline-marker-horizontal bg-success">
                                        <i class="bi bi-smartwatch text-white"></i>
                                    </div>
                                    <div class="timeline-card-horizontal">
                                        <div class="timeline-time">10:30</div>
                                        <h6 class="timeline-title">智能手环数据同步</h6>
                                        <span class="badge bg-success mb-2">同步成功</span>
                                        <p class="timeline-desc">同步心率、步数、睡眠数据成功</p>
                                        <div class="sync-details-horizontal">
                                            <span class="badge bg-light text-dark">心率: 72</span>
                                            <span class="badge bg-light text-dark">步数: 5246</span>
                                            <span class="badge bg-light text-dark">睡眠: 7.2h</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 时间线项目2 -->
                                <div class="horizontal-timeline-item">
                                    <div class="timeline-marker-horizontal bg-primary">
                                        <i class="bi bi-activity text-white"></i>
                                    </div>
                                    <div class="timeline-card-horizontal">
                                        <div class="timeline-time">08:15</div>
                                        <h6 class="timeline-title">血压计数据同步</h6>
                                        <span class="badge bg-success mb-2">同步成功</span>
                                        <p class="timeline-desc">血压数据同步完成，数值正常</p>
                                        <div class="sync-details-horizontal">
                                            <span class="badge bg-light text-dark">收缩压: 120</span>
                                            <span class="badge bg-light text-dark">舒张压: 80</span>
                                            <span class="badge bg-success text-white">正常</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 时间线项目3 -->
                                <div class="horizontal-timeline-item">
                                    <div class="timeline-marker-horizontal bg-warning">
                                        <i class="bi bi-droplet text-white"></i>
                                    </div>
                                    <div class="timeline-card-horizontal">
                                        <div class="timeline-time">07:45</div>
                                        <h6 class="timeline-title">血糖仪连接尝试</h6>
                                        <span class="badge bg-warning mb-2">连接失败</span>
                                        <p class="timeline-desc">设备无响应，请检查设备电源</p>
                                        <div class="sync-details-horizontal">
                                            <span class="badge bg-danger text-white">设备离线</span>
                                            <button class="btn btn-sm btn-outline-warning">
                                                <i class="bi bi-arrow-repeat"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- 时间线项目4 -->
                                <div class="horizontal-timeline-item">
                                    <div class="timeline-marker-horizontal bg-info">
                                        <i class="bi bi-smartwatch text-white"></i>
                                    </div>
                                    <div class="timeline-card-horizontal">
                                        <div class="timeline-time">昨天 23:30</div>
                                        <h6 class="timeline-title">夜间睡眠数据</h6>
                                        <span class="badge bg-success mb-2">同步成功</span>
                                        <p class="timeline-desc">夜间睡眠监测数据已同步</p>
                                        <div class="sync-details-horizontal">
                                            <span class="badge bg-light text-dark">深度: 2.5h</span>
                                            <span class="badge bg-light text-dark">浅度: 4.7h</span>
                                            <span class="badge bg-light text-dark">效率: 93%</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 时间线项目5 -->
                                <div class="horizontal-timeline-item">
                                    <div class="timeline-marker-horizontal bg-secondary">
                                        <i class="bi bi-plus text-white"></i>
                                    </div>
                                    <div class="timeline-card-horizontal add-more-card">
                                        <div class="text-center py-3">
                                            <i class="bi bi-three-dots fs-4 text-muted"></i>
                                            <p class="mb-0 text-muted">查看更多记录</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <button class="btn btn-outline-primary btn-lg">
                            <i class="bi bi-clock-history me-2"></i> 查看完整同步历史
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 生活服务 - 智能订餐系统 -->
        <div v-if="currentTab === 'services'">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>订餐服务 - 智能订餐系统</h2>
                <div>
                    <button class="btn btn-outline-primary" @click="showMyOrders">
                        <i class="bi bi-list-ul me-2"></i>查看我的订单
                    </button>
                </div>
            </div>

            <div class="row">
                <!-- 左栏：饮食导航塔（25%） -->
                <div class="col-md-3">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="bi bi-signpost-2-fill me-2"></i>饮食导航塔</h5>
                        </div>
                        <div class="card-body">
                            <!-- 本周营养计划 -->
                            <h6 class="mb-3 fw-bold"><i class="bi bi-calendar-check me-2 text-success"></i>本周营养计划</h6>
                            <div class="nav flex-column nav-pills mb-4" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                                <button class="nav-link text-start mb-2"
                                        :class="{ active: filters.category === 'all' }"
                                        @click="setCategoryFilter('all')" type="button">
                                    <i class="bi bi-grid-3x3-gap me-1 text-info"></i> 全部菜品
                                </button>
                                <button class="nav-link text-start mb-2"
                                        :class="{ active: filters.category === 'doctor_recommended' }"
                                        @click="setCategoryFilter('doctor_recommended')" type="button">
                                    <i class="bi bi-star-fill me-1 text-warning"></i> 医生推荐食谱
                                </button>
                                <button class="nav-link text-start mb-2"
                                        :class="{ active: filters.category === 'low_salt' }"
                                        @click="setCategoryFilter('low_salt')" type="button">
                                    <i class="bi bi-heart me-1 text-danger"></i> 低盐健康餐
                                </button>
                                <button class="nav-link text-start mb-2"
                                        :class="{ active: filters.category === 'diabetic_friendly' }"
                                        @click="setCategoryFilter('diabetic_friendly')" type="button">
                                    <i class="bi bi-droplet me-1 text-primary"></i> 血糖友好餐
                                </button>
                            </div>

                            <!-- 智能筛选器 -->
                            <h6 class="mb-3 fw-bold"><i class="bi bi-funnel-fill me-2 text-primary"></i>忌口闪电过滤</h6>
                            <div class="form-check form-switch mb-2 fs-5">
                                <input class="form-check-input" type="checkbox" id="noSpicy"
                                       v-model="filters.noSpicy" @change="toggleFilter('noSpicy')">
                                <label class="form-check-label" for="noSpicy">不吃辣</label>
                            </div>
                            <div class="form-check form-switch mb-2 fs-5">
                                <input class="form-check-input" type="checkbox" id="noSeafood"
                                       v-model="filters.noSeafood" @change="toggleFilter('noSeafood')">
                                <label class="form-check-label" for="noSeafood">海鲜过敏</label>
                            </div>
                            <div class="form-check form-switch mb-2 fs-5">
                                <input class="form-check-input" type="checkbox" id="lowSalt"
                                       v-model="filters.lowSalt" @change="toggleFilter('lowSalt')">
                                <label class="form-check-label" for="lowSalt">低盐饮食</label>
                            </div>
                            <div class="form-check form-switch mb-4 fs-5">
                                <input class="form-check-input" type="checkbox" id="lowSugar"
                                       v-model="filters.lowSugar" @change="toggleFilter('lowSugar')">
                                <label class="form-check-label" for="lowSugar">低糖饮食</label>
                            </div>

                            <!-- 应急通道 -->
                            <h6 class="mb-3 fw-bold"><i class="bi bi-shield-plus me-2 text-danger"></i>应急通道</h6>
                            <button class="btn btn-danger btn-lg w-100 py-3 mb-3">
                                <i class="bi bi-cup-straw me-2"></i> 流食送餐
                                <div class="small">发烧/牙痛/咀嚼困难</div>
                            </button>

                            <div class="alert alert-info mt-4">
                                <i class="bi bi-info-circle-fill me-2"></i>
                                <small>根据您的血压数据，建议本周采用低盐健康饮食方案</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 中右动态区（75%） -->
                <div class="col-md-9">
                    <!-- 菜品展示区域 -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0 fs-3">
                                <span v-if="filters.category === 'all'">全部菜品</span>
                                <span v-else-if="filters.category === 'doctor_recommended'">医生推荐食谱</span>
                                <span v-else-if="filters.category === 'low_salt'">低盐健康餐</span>
                                <span v-else-if="filters.category === 'diabetic_friendly'">血糖友好餐</span>
                                <span v-else>今日推荐菜品</span>
                                <small class="text-muted ms-2">(共 ${ applyFilters().length }$ 道菜品)</small>
                            </h5>
                            <div>
                                <button class="btn btn-lg btn-outline-primary me-2">
                                    <i class="bi bi-mic-fill me-1"></i> 语音点餐
                                </button>
                                <div class="btn-group">
                                    <button class="btn btn-lg btn-outline-success active">午餐</button>
                                    <button class="btn btn-lg btn-outline-success">晚餐</button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 筛选结果为空时的提示 -->
                            <div v-if="getCurrentPageFoods().length === 0" class="text-center py-5">
                                <i class="bi bi-search fs-1 text-muted"></i>
                                <h4 class="mt-3 text-muted">没有找到符合条件的菜品</h4>
                                <p class="text-muted">请尝试调整筛选条件</p>
                                <button class="btn btn-primary" @click="filters = { category: 'all', noSpicy: false, noSeafood: false, lowSalt: false, lowSugar: false }; pagination.currentPage = 1;">
                                    <i class="bi bi-arrow-clockwise me-2"></i>重置筛选
                                </button>
                            </div>

                            <!-- 菜品网格 -->
                            <div v-else class="food-menu-container">
                                <!-- 动态菜品列表 -->
                                <div v-for="food in getCurrentPageFoods()" :key="'food-' + food.id"
                                     class="food-item" @click="showFoodDetail(food.id)">
                                    <div class="food-img-container">
                                        <img :src="food.image" class="food-img" :alt="food.name">
                                        <span v-if="food.badge"
                                              class="position-absolute top-0 end-0 badge m-2 fs-6 p-2"
                                              :class="food.badgeClass">${ food.badge }$</span>
                                    </div>
                                    <div class="p-3">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <h4 class="mb-0 fw-bold">${ food.name }$</h4>
                                            <button class="btn btn-sm btn-outline-primary rounded-circle">
                                                <i class="bi bi-volume-up-fill fs-5"></i>
                                            </button>
                                        </div>
                                        <div class="mb-2">
                                            <span v-for="(tag, tagIndex) in food.tags" :key="'tag-' + tagIndex"
                                                  class="nutrition-tag tag-protein fs-6 me-1">${ tag }$</span>
                                        </div>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <span class="fw-bold fs-4 me-2">¥${ food.price }$</span>
                                                <button class="btn btn-primary btn-lg" @click.stop="addToCart(food.id)">
                                                    <i class="bi bi-plus-lg"></i> 加入
                                                </button>
                                            </div>
                                            <span class="text-success fs-6">${ food.info }$</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 分页控件 -->
                            <div v-if="getTotalPages() > 1" class="mt-4">
                                <nav aria-label="菜品分页">
                                    <ul class="pagination justify-content-center">
                                        <li class="page-item" :class="{ disabled: pagination.currentPage === 1 }">
                                            <button class="page-link" @click="previousPage()" :disabled="pagination.currentPage === 1">
                                                <i class="bi bi-chevron-left"></i> 上一页
                                            </button>
                                        </li>
                                        <li v-for="page in getTotalPages()" :key="'page-' + page"
                                            class="page-item" :class="{ active: pagination.currentPage === page }">
                                            <button class="page-link" @click="changePage(page)">${ page }$</button>
                                        </li>
                                        <li class="page-item" :class="{ disabled: pagination.currentPage === getTotalPages() }">
                                            <button class="page-link" @click="nextPage()" :disabled="pagination.currentPage === getTotalPages()">
                                                下一页 <i class="bi bi-chevron-right"></i>
                                            </button>
                                        </li>
                                    </ul>
                                </nav>
                                <div class="text-center text-muted">
                                    第 ${ pagination.currentPage }$ 页，共 ${ getTotalPages() }$ 页，每页显示 ${ pagination.itemsPerPage }$ 道菜品
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 点餐态 - 沉浸式选购（初始时隐藏） -->
            <div class="modal fade" id="foodDetailModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-xl modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h4 class="modal-title"><i class="bi bi-info-circle me-2"></i>菜品详情</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body p-0">
                            <div class="row g-0">
                                <!-- 左侧（60%）：菜品详情 -->
                                <div class="col-md-7 p-4">
                                    <div class="food-detail-container">
                                        <div class="text-center mb-3">
                                            <img v-if="currentFood" :src="currentFood.image" class="img-fluid rounded" style="max-height: 300px;" :alt="currentFood.name">
                                            <img v-else src="https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg" class="img-fluid rounded" style="max-height: 300px;" alt="食品图片">
                                            <div class="mt-2">
                                                <button class="btn btn-outline-primary me-2">
                                                    <i class="bi bi-arrows-angle-expand"></i> 放大查看
                                                </button>
                                                <button class="btn btn-outline-success">
                                                    <i class="bi bi-volume-up-fill"></i> 语音介绍
                                                </button>
                                            </div>
                                        </div>

                                        <h3 class="fw-bold mb-3" v-if="currentFood">${ currentFood.name }$</h3>
                                        <h3 class="fw-bold mb-3" v-else>菜品详情</h3>

                                        <div class="nutrition-info mb-4">
                                            <h5 class="border-bottom pb-2"><i class="bi bi-pie-chart-fill me-2 text-primary"></i>营养金字塔</h5>
                                            <div class="row text-center" v-if="currentFood && currentFood.nutrition">
                                                <div class="col-3">
                                                    <div class="py-2 px-1 bg-primary bg-opacity-10 rounded mb-1">
                                                        <span class="fw-bold d-block fs-4">${ currentFood.nutrition.protein }$g</span>
                                                        <small>蛋白质</small>
                                                    </div>
                                                    <small class="text-muted">≈ ${ Math.round(currentFood.nutrition.protein/10) }$个鸡蛋</small>
                                                </div>
                                                <div class="col-3">
                                                    <div class="py-2 px-1 bg-success bg-opacity-10 rounded mb-1">
                                                        <span class="fw-bold d-block fs-4">${ currentFood.nutrition.fat }$g</span>
                                                        <small>脂肪</small>
                                                    </div>
                                                    <small class="text-muted">≈ ${ currentFood.nutrition.fat }$茶匙油</small>
                                                </div>
                                                <div class="col-3">
                                                    <div class="py-2 px-1 bg-warning bg-opacity-10 rounded mb-1">
                                                        <span class="fw-bold d-block fs-4">${ currentFood.nutrition.carbs }$g</span>
                                                        <small>碳水</small>
                                                    </div>
                                                    <small class="text-muted">≈ ${ Math.round(currentFood.nutrition.carbs/30) }$碗米饭</small>
                                                </div>
                                                <div class="col-3">
                                                    <div class="py-2 px-1 bg-danger bg-opacity-10 rounded mb-1">
                                                        <span class="fw-bold d-block fs-4">${ currentFood.nutrition.salt }$g</span>
                                                        <small>盐分</small>
                                                    </div>
                                                    <small class="text-muted">≈ ${ currentFood.nutrition.salt }$粒花生米</small>
                                                </div>
                                            </div>
                                            <div class="row text-center" v-else>
                                                <div class="col-12">
                                                    <p class="text-muted">加载中...</p>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="health-benefits mb-4">
                                            <h5 class="border-bottom pb-2"><i class="bi bi-heart-pulse-fill me-2 text-danger"></i>健康益处</h5>
                                            <ul class="list-group list-group-flush" v-if="currentFood && currentFood.benefits">
                                                <li class="list-group-item d-flex align-items-center border-0 ps-0" v-for="(benefit, index) in currentFood.benefits" :key="'benefit-'+index">
                                                    <i class="bi bi-check-circle-fill text-success me-2 fs-5"></i>
                                                    <span class="fs-5">${ benefit }$</span>
                                                </li>
                                            </ul>
                                            <ul class="list-group list-group-flush" v-else>
                                                <li class="list-group-item d-flex align-items-center border-0 ps-0">
                                                    <span class="text-muted">加载中...</span>
                                                </li>
                                            </ul>
                                        </div>

                                        <div class="preparation-info">
                                            <h5 class="border-bottom pb-2"><i class="bi bi-info-circle-fill me-2 text-info"></i>制作与配送</h5>
                                            <p class="fs-5">制作时间：约30分钟 | 当日新鲜配送</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 右侧（40%）：购物车简版 -->
                                <div class="col-md-5 bg-light p-4">
                                    <h4 class="mb-3 fw-bold"><i class="bi bi-cart4 me-2 text-primary"></i>您的购物车</h4>

                                    <div class="cart-items-container mb-4">
                                        <div v-if="!cart.items || cart.items.length === 0" class="text-center py-4">
                                            <i class="bi bi-cart-x fs-1 text-muted"></i>
                                            <p class="fs-5 mt-2">购物车还是空的</p>
                                            <button class="btn btn-outline-primary fs-5 mt-2" data-bs-dismiss="modal">
                                                <i class="bi bi-plus-circle me-1"></i> 去选择美味佳肴
                                            </button>
                                        </div>

                                        <div v-for="item in cart.items" :key="'cart-item-'+item.id" class="cart-item bg-white p-3 rounded mb-3 shadow-sm">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <img :src="item.image" class="rounded me-3" width="60" height="60" :alt="item.name">
                                                    <div>
                                                        <h5 class="mb-0 fw-bold">${ item.name }$</h5>
                                                        <span v-for="(tag, tagIndex) in item.tags" :key="'tag-'+tagIndex" class="badge bg-primary me-1">${ tag }$</span>
                                                    </div>
                                                </div>
                                                <div class="text-end">
                                                    <span class="fw-bold fs-5">¥${ item.price }$</span>
                                                    <div class="btn-group mt-1">
                                                        <button class="btn btn-outline-secondary" @click="decreaseQuantity(item.id)">
                                                            <i class="bi bi-dash"></i>
                                                        </button>
                                                        <span class="btn btn-outline-secondary disabled">${ item.quantity }$</span>
                                                        <button class="btn btn-outline-secondary" @click="increaseQuantity(item.id)">
                                                            <i class="bi bi-plus"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div v-if="cart.items && cart.items.length > 0" class="text-center mb-3">
                                            <button class="btn btn-outline-primary fs-5" data-bs-dismiss="modal">
                                                <i class="bi bi-plus-circle me-1"></i> 继续添加其他菜品
                                            </button>
                                        </div>
                                    </div>

                                    <div class="price-summary border-top border-bottom py-3 my-3">
                                        <div class="d-flex justify-content-between fs-5 mb-2">
                                            <span>商品小计</span>
                                            <span>¥${ cart.totalPrice || 0 }$</span>
                                        </div>
                                        <div class="d-flex justify-content-between fs-5 mb-2">
                                            <span>配送费</span>
                                            <span>¥5</span>
                                        </div>
                                        <div class="d-flex justify-content-between fw-bold fs-4">
                                            <span>总计</span>
                                            <span class="text-danger">¥${ (cart.totalPrice || 0) + 5 }$</span>
                                        </div>
                                    </div>

                                    <div class="delivery-preferences mb-3">
                                        <label class="form-label fs-5 fw-bold">希望送达时间</label>
                                        <select class="form-select form-select-lg fs-5 mb-3">
                                            <option selected>11:30-12:00 (推荐)</option>
                                            <option>12:00-12:30</option>
                                            <option>12:30-13:00</option>
                                        </select>

                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="callBeforeDelivery" checked>
                                            <label class="form-check-label fs-5" for="callBeforeDelivery">
                                                送餐前电话通知
                                            </label>
                                        </div>
                                    </div>

                                    <button class="btn btn-success btn-lg w-100 py-3 fs-4" @click="checkout()" :disabled="!cart.items || cart.items.length === 0">
                                        <i class="bi bi-wallet2 me-2"></i> 去结算
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 支付态 - 无忧确认（初始时隐藏） -->
            <div class="modal fade" id="paymentModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-lg modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-success text-white">
                            <h4 class="modal-title"><i class="bi bi-wallet2 me-2"></i>订单确认</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body p-4">
                            <div class="row">
                                <div class="col-md-6">
                                    <h4 class="mb-3"><i class="bi bi-geo-alt-fill me-2 text-danger"></i>配送信息</h4>
                                    <!-- 使用语音播报的配送时间选择 -->
                                    <div class="mb-3">
                                        <label class="form-label fs-5">配送时间</label>
                                        <div class="d-flex align-items-center">
                                            <select class="form-select form-select-lg fs-5">
                                                <option selected>11:30-12:00 (推荐)</option>
                                                <option>12:00-12:30</option>
                                                <option>12:30-13:00</option>
                                            </select>
                                            <button class="btn btn-outline-primary ms-2" title="语音播报可选时段">
                                                <i class="bi bi-volume-up-fill"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-house-door-fill fs-1 text-primary me-3"></i>
                                                <div>
                                                    <h5 class="mb-1">默认地址: 和平小区5号楼3单元102室</h5>
                                                    <p class="mb-0">王大伯 (185****1234)</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label fs-5">配送备注</label>
                                        <div class="d-flex align-items-center">
                                            <select class="form-select form-select-lg fs-5">
                                                <option selected>放门口，不用按门铃</option>
                                                <option>送到家里，需要帮忙打开</option>
                                                <option>请提前电话联系</option>
                                            </select>
                                            <button class="btn btn-outline-primary ms-2">
                                                <i class="bi bi-pencil-fill"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <h4 class="mb-3"><i class="bi bi-credit-card-fill me-2 text-success"></i>支付方式</h4>
                                    <!-- 仅显示3个最常用选项 -->
                                    <div class="payment-options">
                                        <div class="card mb-2 border-primary">
                                            <div class="card-body">
                                                <div class="form-check d-flex align-items-center">
                                                    <input class="form-check-input me-3" type="radio" name="paymentMethod" id="paymentBalance" checked style="width: 25px; height: 25px;">
                                                    <label class="form-check-label d-flex align-items-center justify-content-between w-100" for="paymentBalance">
                                                        <div class="d-flex align-items-center">
                                                            <i class="bi bi-wallet2-fill fs-1 text-primary me-3"></i>
                                                            <div>
                                                                <h5 class="mb-0 fs-4">账户余额支付</h5>
                                                                <p class="mb-0">余额: ¥208.50</p>
                                                            </div>
                                                        </div>
                                                        <i class="bi bi-check-circle-fill text-primary fs-4"></i>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="card mb-2">
                                            <div class="card-body">
                                                <div class="form-check d-flex align-items-center">
                                                    <input class="form-check-input me-3" type="radio" name="paymentMethod" id="paymentFamily" style="width: 25px; height: 25px;">
                                                    <label class="form-check-label d-flex align-items-center justify-content-between w-100" for="paymentFamily">
                                                        <div class="d-flex align-items-center">
                                                            <i class="bi bi-people-fill fs-1 text-success me-3"></i>
                                                            <div>
                                                                <h5 class="mb-0 fs-4">子女代付</h5>
                                                                <p class="mb-0">微信通知家人支付</p>
                                                            </div>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="card mb-2">
                                            <div class="card-body">
                                                <div class="form-check d-flex align-items-center">
                                                    <input class="form-check-input me-3" type="radio" name="paymentMethod" id="paymentSubsidy" style="width: 25px; height: 25px;">
                                                    <label class="form-check-label d-flex align-items-center justify-content-between w-100" for="paymentSubsidy">
                                                        <div class="d-flex align-items-center">
                                                            <i class="bi bi-building-fill fs-1 text-warning me-3"></i>
                                                            <div>
                                                                <h5 class="mb-0 fs-4">政府补贴</h5>
                                                                <p class="mb-0">每月补贴剩余: ¥180</p>
                                                            </div>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 订单摘要 -->
                                    <div class="order-summary mt-4">
                                        <h5 class="border-bottom pb-2">订单摘要</h5>
                                        <div v-if="cart.items && cart.items.length > 0">
                                            <div v-for="(item, itemIndex) in cart.items" :key="'summary-item-'+itemIndex" class="d-flex justify-content-between fs-5 mb-2">
                                                <span>${ item.name }$ x${ item.quantity }$</span>
                                                <span>¥${ item.price * item.quantity }$</span>
                                            </div>
                                        </div>
                                        <div v-else>
                                            <p class="text-muted">购物车为空</p>
                                        </div>
                                        <div class="d-flex justify-content-between fs-5 mb-2">
                                            <span>配送费</span>
                                            <span>¥5</span>
                                        </div>
                                        <div class="d-flex justify-content-between fw-bold fs-4 text-danger">
                                            <span>总计</span>
                                            <span>¥${ (cart.totalPrice || 0) + 5 }$</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 应急联系浮窗 -->
                            <div class="alert alert-info d-flex align-items-center mt-4">
                                <i class="bi bi-info-circle-fill fs-4 me-3"></i>
                                <div>
                                    <h5 class="mb-1">订单通知已自动发送给家属</h5>
                                    <p class="mb-0">王女士(女儿) 将收到订单消息提醒</p>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-secondary btn-lg fs-5" data-bs-dismiss="modal">返回修改</button>
                            <button type="button" class="btn btn-success btn-lg fs-4 px-5" @click="confirmPayment()">
                                <i class="bi bi-check-circle-fill me-2"></i> 确认支付 ¥${ (cart.totalPrice || 0) + 5 }$
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 个人档案 -->
        <div v-if="currentTab === 'profile'">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>个人健康档案</h2>
                <div>
                    <button class="btn btn-outline-primary me-2">
                        <i class="bi bi-upload me-1"></i> 导入数据
                    </button>
                    <button class="btn btn-outline-secondary">
                        <i class="bi bi-printer me-1"></i> 打印档案
                    </button>
                </div>
            </div>

            <div class="health-record-container">
                <!-- 左侧时间轴 -->
                <div class="health-timeline" :class="{ 'collapsed': timelineCollapsed }">
                    <div class="timeline-header p-3 bg-gradient-primary text-white rounded-top">
                        <h5 class="mb-0 d-flex align-items-center">
                            <i class="bi bi-clock-history me-2" v-if="!timelineCollapsed"></i>
                            <span v-if="!timelineCollapsed">病史记录时间轴</span>
                            <span class="vertical-text">病史记录</span>
                        </h5>
                        <button class="timeline-toggle-btn text-white" @click="toggleTimeline">
                            <i class="bi" :class="timelineCollapsed ? 'bi-chevron-right' : 'bi-chevron-left'"></i>
                        </button>
                    </div>

                    <div class="timeline-content p-3">
                        <!-- 医疗时间轴 -->
                        <div class="medical-timeline">
                            <!-- 时间轴项目1 -->
                            <div class="medical-timeline-item">
                                <div class="timeline-marker-wrapper">
                                    <div class="timeline-marker bg-danger">
                                        <i class="bi bi-person-check-fill text-white"></i>
                                    </div>
                                    <div class="timeline-line"></div>
                                </div>
                                <div class="timeline-card">
                                    <div class="timeline-card-header">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0 fw-bold text-danger">社区医生随访</h6>
                                            <span class="timeline-date">2023-05-07</span>
                                        </div>
                                        <span class="badge bg-danger bg-opacity-10 text-danger">随访记录</span>
                                    </div>
                                    <div class="timeline-card-body">
                                        <div class="medical-info">
                                            <div class="info-item">
                                                <i class="bi bi-chat-dots text-primary me-2"></i>
                                                <span class="info-label">主诉:</span>
                                                <span class="info-value">近期偶有头晕</span>
                                            </div>
                                            <div class="info-item">
                                                <i class="bi bi-clipboard-check text-success me-2"></i>
                                                <span class="info-label">诊断:</span>
                                                <span class="info-value">轻度高血压，建议监测血压</span>
                                            </div>
                                            <div class="info-item">
                                                <i class="bi bi-prescription2 text-warning me-2"></i>
                                                <span class="info-label">医嘱:</span>
                                                <span class="info-value">继续服用降压药，每日测量血压</span>
                                            </div>
                                        </div>
                                        <div class="timeline-actions mt-2">
                                            <button class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye me-1"></i>查看详情
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 时间轴项目2 -->
                            <div class="medical-timeline-item">
                                <div class="timeline-marker-wrapper">
                                    <div class="timeline-marker bg-success">
                                        <i class="bi bi-file-medical-fill text-white"></i>
                                    </div>
                                    <div class="timeline-line"></div>
                                </div>
                                <div class="timeline-card">
                                    <div class="timeline-card-header">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0 fw-bold text-success">季度体检报告</h6>
                                            <span class="timeline-date">2023-04-25</span>
                                        </div>
                                        <span class="badge bg-success bg-opacity-10 text-success">体检报告</span>
                                    </div>
                                    <div class="timeline-card-body">
                                        <div class="medical-info">
                                            <div class="info-item">
                                                <i class="bi bi-list-check text-info me-2"></i>
                                                <span class="info-label">检查项目:</span>
                                                <span class="info-value">血常规、尿常规、肝功能、肾功能</span>
                                            </div>
                                            <div class="info-item">
                                                <i class="bi bi-exclamation-triangle text-warning me-2"></i>
                                                <span class="info-label">异常项:</span>
                                                <span class="info-value">总胆固醇轻度升高(5.8mmol/L)</span>
                                            </div>
                                            <div class="info-item">
                                                <i class="bi bi-prescription2 text-primary me-2"></i>
                                                <span class="info-label">医嘱:</span>
                                                <span class="info-value">低脂饮食，增加运动，三个月后复查</span>
                                            </div>
                                        </div>
                                        <div class="timeline-actions mt-2">
                                            <button class="btn btn-sm btn-outline-success">
                                                <i class="bi bi-file-earmark-pdf me-1"></i>查看报告
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 时间轴项目3 -->
                            <div class="medical-timeline-item">
                                <div class="timeline-marker-wrapper">
                                    <div class="timeline-marker bg-info">
                                        <i class="bi bi-heart-pulse-fill text-white"></i>
                                    </div>
                                    <div class="timeline-line"></div>
                                </div>
                                <div class="timeline-card">
                                    <div class="timeline-card-header">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0 fw-bold text-info">心脏科门诊</h6>
                                            <span class="timeline-date">2023-04-05</span>
                                        </div>
                                        <span class="badge bg-info bg-opacity-10 text-info">专科门诊</span>
                                    </div>
                                    <div class="timeline-card-body">
                                        <div class="medical-info">
                                            <div class="info-item">
                                                <i class="bi bi-chat-dots text-primary me-2"></i>
                                                <span class="info-label">主诉:</span>
                                                <span class="info-value">心悸，心率不齐</span>
                                            </div>
                                            <div class="info-item">
                                                <i class="bi bi-activity text-danger me-2"></i>
                                                <span class="info-label">检查:</span>
                                                <span class="info-value">心电图示窦性心律不齐</span>
                                            </div>
                                            <div class="info-item">
                                                <i class="bi bi-clipboard-check text-success me-2"></i>
                                                <span class="info-label">诊断:</span>
                                                <span class="info-value">窦性心律不齐</span>
                                            </div>
                                            <div class="info-item">
                                                <i class="bi bi-capsule text-warning me-2"></i>
                                                <span class="info-label">处方:</span>
                                                <span class="info-value">倍他乐克 25mg 每日1次</span>
                                            </div>
                                        </div>
                                        <div class="timeline-actions mt-2">
                                            <button class="btn btn-sm btn-outline-info">
                                                <i class="bi bi-journal-medical me-1"></i>查看病历
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 添加记录按钮 -->
                            <div class="medical-timeline-item">
                                <div class="timeline-marker-wrapper">
                                    <div class="timeline-marker bg-secondary">
                                        <i class="bi bi-plus-lg text-white"></i>
                                    </div>
                                </div>
                                <div class="timeline-card add-record-card">
                                    <button class="btn btn-outline-primary w-100 py-3">
                                        <i class="bi bi-plus-circle me-2"></i>
                                        <span class="fw-bold">添加新的医疗记录</span>
                                        <div class="small text-muted mt-1">记录您的就医信息</div>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧健康数据详情 -->
                <div class="flex-fill">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">健康档案</h5>
                        </div>
                        <div class="card-body">
                            <ul class="nav nav-tabs mb-3" id="healthDataTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="examinations-tab" data-bs-toggle="tab" data-bs-target="#examinations" type="button">检查报告</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="medications-tab" data-bs-toggle="tab" data-bs-target="#medications" type="button">用药记录</button>
                                </li>
                            </ul>

                            <div class="tab-content" id="healthDataContent">


                                <!-- 用药记录标签页 -->
                                <div class="tab-pane fade" id="medications" role="tabpanel">
                                    <div class="alert alert-info mb-4">
                                        <i class="bi bi-info-circle-fill me-2"></i>
                                        您目前有3种长期服用的药物，包括1种心脏用药需要按时服用
                                    </div>

                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>药物名称</th>
                                                    <th>用量</th>
                                                    <th>频率</th>
                                                    <th>开始日期</th>
                                                    <th>剩余药量</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <i class="bi bi-capsule text-primary me-2"></i>
                                                            <div>
                                                                <strong>阿司匹林</strong><br>
                                                                <small class="text-muted">抗血栓</small>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>100mg</td>
                                                    <td>每日1次</td>
                                                    <td>2023-05-15</td>
                                                    <td>
                                                        <div class="progress" style="height: 8px; width: 100px;">
                                                            <div class="progress-bar bg-success" role="progressbar" style="width: 65%;" aria-valuenow="65" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                        <small>剩余19天</small>
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-sm btn-outline-primary">记录服用</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <i class="bi bi-capsule text-danger me-2"></i>
                                                            <div>
                                                                <strong>硝苯地平缓释片</strong><br>
                                                                <small class="text-muted">降血压</small>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>30mg</td>
                                                    <td>每日1次</td>
                                                    <td>2023-06-20</td>
                                                    <td>
                                                        <div class="progress" style="height: 8px; width: 100px;">
                                                            <div class="progress-bar bg-warning" role="progressbar" style="width: 25%;" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                        <small>剩余7天</small>
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-sm btn-outline-primary">记录服用</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <i class="bi bi-capsule text-info me-2"></i>
                                                            <div>
                                                                <strong>甲钴胺片</strong><br>
                                                                <small class="text-muted">神经营养</small>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>0.5mg</td>
                                                    <td>每日3次</td>
                                                    <td>2023-07-05</td>
                                                    <td>
                                                        <div class="progress" style="height: 8px; width: 100px;">
                                                            <div class="progress-bar bg-danger" role="progressbar" style="width: 10%;" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                        <small>剩余3天</small>
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-sm btn-outline-primary">记录服用</button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>

                                    <div class="d-flex justify-content-end mt-3">
                                        <button class="btn btn-primary">
                                            <i class="bi bi-bag-plus me-1"></i> 添加新药物
                                        </button>
                                    </div>
                                </div>

                                <!-- 检查报告标签页 -->
                                <div class="tab-pane fade" id="examinations" role="tabpanel">
                                    <div class="alert alert-success mb-4">
                                        <i class="bi bi-check-circle-fill me-2"></i>
                                        您已完成本季度的健康体检，整体情况良好
                                    </div>

                                    <div class="row mb-4">
                                        <div class="col-md-4">
                                            <div class="card h-100">
                                                <div class="card-body text-center">
                                                    <i class="bi bi-file-earmark-medical text-primary fs-1 mb-3"></i>
                                                    <h5>季度体检报告</h5>
                                                    <p class="text-muted small">2023年第三季度</p>
                                                    <p class="mb-0">
                                                        <span class="badge bg-success me-1">已完成</span>
                                                        <span class="small text-muted">2023-09-15</span>
                                                    </p>
                                                    <button class="btn btn-sm btn-primary mt-3">
                                                        <i class="bi bi-file-earmark-pdf me-1"></i> 查看报告
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="card h-100">
                                                <div class="card-body text-center">
                                                    <i class="bi bi-heart-pulse text-danger fs-1 mb-3"></i>
                                                    <h5>心电图检查</h5>
                                                    <p class="text-muted small">专项检查</p>
                                                    <p class="mb-0">
                                                        <span class="badge bg-success me-1">已完成</span>
                                                        <span class="small text-muted">2023-08-22</span>
                                                    </p>
                                                    <button class="btn btn-sm btn-primary mt-3">
                                                        <i class="bi bi-file-earmark-pdf me-1"></i> 查看报告
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="card h-100">
                                                <div class="card-body text-center">
                                                    <i class="bi bi-lungs text-info fs-1 mb-3"></i>
                                                    <h5>肺功能检查</h5>
                                                    <p class="text-muted small">专项检查</p>
                                                    <p class="mb-0">
                                                        <span class="badge bg-warning me-1">待完成</span>
                                                        <span class="small text-muted">2023-10-25</span>
                                                    </p>
                                                    <button class="btn btn-sm btn-outline-primary mt-3">
                                                        <i class="bi bi-calendar-check me-1"></i> 查看预约
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="mb-0">健康分析报告</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <h6 class="border-bottom pb-2 mb-3">指标异常情况</h6>
                                                    <div class="mb-3">
                                                        <div class="d-flex justify-content-between">
                                                            <span>总胆固醇</span>
                                                            <span class="text-danger">5.8 mmol/L</span>
                                                        </div>
                                                        <div class="progress mt-1" style="height: 6px;">
                                                            <div class="progress-bar bg-danger" role="progressbar" style="width: 65%;" aria-valuenow="65" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                        <small class="text-muted">参考范围: 3.1-5.2 mmol/L</small>
                                                    </div>
                                                    <div class="mb-3">
                                                        <div class="d-flex justify-content-between">
                                                            <span>空腹血糖</span>
                                                            <span class="text-warning">6.3 mmol/L</span>
                                                        </div>
                                                        <div class="progress mt-1" style="height: 6px;">
                                                            <div class="progress-bar bg-warning" role="progressbar" style="width: 45%;" aria-valuenow="45" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                        <small class="text-muted">参考范围: 3.9-6.1 mmol/L</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <h6 class="border-bottom pb-2 mb-3">医生建议</h6>
                                                    <div class="alert alert-warning">
                                                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                                        <span>您的胆固醇偏高，建议减少油腻食物摄入并增加运动。</span>
                                                    </div>
                                                    <div class="alert alert-info">
                                                        <i class="bi bi-info-circle-fill me-2"></i>
                                                        <span>血糖水平轻度升高，建议定期监测，控制碳水化合物摄入。</span>
                                                    </div>
                                                    <button class="btn btn-sm btn-outline-primary mt-2">
                                                        <i class="bi bi-telephone-fill me-1"></i> 咨询医生
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>

        <!-- 健康科普 -->
        <div v-if="currentTab === 'health_edu'">
            <!-- 收藏页面 -->
            <div v-if="showFavorites && !articleExpanded">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="fw-bold">我的收藏</h2>
                    <button class="btn btn-outline-secondary" @click="hideFavoriteArticles">
                        <i class="bi bi-arrow-left me-1"></i> 返回文章列表
                    </button>
                </div>

                <div v-if="favoriteArticles.length === 0" class="text-center py-5">
                    <i class="bi bi-bookmark text-muted" style="font-size: 4rem;"></i>
                    <h4 class="text-muted mt-3">暂无收藏文章</h4>
                    <p class="text-muted">您还没有收藏任何文章，快去发现感兴趣的健康知识吧！</p>
                    <button class="btn btn-primary" @click="hideFavoriteArticles">
                        <i class="bi bi-search me-1"></i> 浏览文章
                    </button>
                </div>

                <div v-else class="row">
                    <div v-for="article in favoriteArticles" :key="article.article_id" class="col-md-6 mb-4">
                        <div class="article-card card h-100" @click="showArticleDetail(article)">
                            <!-- 图片在顶部 -->
                            <div class="article-image-top">
                                <img :src="article.image_url || '/static/images/default-article.jpg'"
                                     :alt="article.title" class="card-img-top article-thumbnail">
                            </div>
                            <div class="card-body">
                                <!-- 标题 -->
                                <h6 class="article-title fw-bold mb-2">${ article.title }$</h6>
                                <!-- 文章开头几句文字 -->
                                <p class="article-summary text-muted mb-3">
                                    ${ article.content.substring(0, 100) }$...
                                </p>
                                <!-- 底部元信息 -->
                                <div class="article-meta d-flex justify-content-between align-items-center">
                                    <span class="badge bg-primary">${ article.category }$</span>
                                    <small class="text-muted">
                                        <i class="bi bi-eye me-1"></i>${ article.read_count || 0 }$
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 文章列表视图 -->
            <div v-if="!articleExpanded && !showFavorites">
                <!-- 标题 -->
                <div class="d-flex justify-content-between align-items-center mb-4">

                    <h2 class="fw-bold">健康科普</h2>
                </div>

                <!-- 搜索栏和收藏按钮 -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div class="flex-grow-1 d-flex justify-content-center">
                        <!-- 搜索框居中 -->
                        <div class="input-group" style="width: 900px;">
                            <input
                                type="text"
                                class="form-control"
                                placeholder="搜索文章标题或内容..."
                                style="font: 1em sans-serif;"
                                v-model="searchKeyword"
                                @input="searchArticles"
                                @keyup.enter="searchArticles">
                            <button
                                class="btn btn-outline-secondary"
                                style="color:black;size:14em;"
                                type="button"
                                @click="searchArticles"
                                :disabled="!searchKeyword.trim()">
                                <i class="bi bi-search"></i>
                            </button>
                            <button
                                class="btn btn-outline-danger"
                                type="button"
                                @click="clearSearch"
                                v-if="searchKeyword.trim()"
                                title="清除搜索">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                    </div>
                    <!-- 我的收藏按钮在最右侧 -->
                    <div>
                        <button class="btn btn-outline-primary" @click="showFavoriteArticles">
                            <i class="bi bi-bookmark me-1"></i> 我的收藏 (${ favoriteArticles.length }$)
                        </button>
                    </div>
                </div>

                <!-- 顶部分类筛选栏 -->
                <div class="category-filters-top mb-4">
                    <div class="d-flex flex-wrap gap-2 justify-content-center">
                        <button
                            class="btn category-btn-top"
                            :class="selectedCategory === 'all' ? 'btn-primary' : 'btn-outline-primary'"
                            @click="selectCategory('all')">
                            <i class="bi bi-grid-3x3-gap me-2"></i>全部分类
                        </button>
                        <button
                            class="btn category-btn-top"
                            :class="selectedCategory === '营养膳食' ? 'btn-primary' : 'btn-outline-primary'"
                            @click="selectCategory('营养膳食')">
                            <i class="bi bi-cup-hot me-2"></i>营养膳食
                        </button>
                        <button
                            class="btn category-btn-top"
                            :class="selectedCategory === '运动健身' ? 'btn-primary' : 'btn-outline-primary'"
                            @click="selectCategory('运动健身')">
                            <i class="bi bi-person-walking me-2"></i>运动健身
                        </button>
                        <button
                            class="btn category-btn-top"
                            :class="selectedCategory === '心理健康' ? 'btn-primary' : 'btn-outline-primary'"
                            @click="selectCategory('心理健康')">
                            <i class="bi bi-heart me-2"></i>心理健康
                        </button>
                        <button
                            class="btn category-btn-top"
                            :class="selectedCategory === '疾病预防' ? 'btn-primary' : 'btn-outline-primary'"
                            @click="selectCategory('疾病预防')">
                            <i class="bi bi-shield-check me-2"></i>疾病预防
                        </button>
                        <button
                            class="btn category-btn-top"
                            :class="selectedCategory === '用药安全' ? 'btn-primary' : 'btn-outline-primary'"
                            @click="selectCategory('用药安全')">
                            <i class="bi bi-capsule me-2"></i>用药安全
                        </button>
                        <button
                            class="btn category-btn-top"
                            :class="selectedCategory === '急救常识' ? 'btn-primary' : 'btn-outline-primary'"
                            @click="selectCategory('急救常识')">
                            <i class="bi bi-bandaid me-2"></i>急救常识
                        </button>
                    </div>
                </div>

                <!-- 文章列表 -->
                <div class="health-articles-container">
                    <div v-if="articlesLoading" class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载健康科普文章...</p>
                    </div>

                    <div v-else>
                        <!-- 搜索结果为空时的提示 -->
                        <div v-if="filteredArticles.length === 0 && searchKeyword.trim()" class="text-center py-5">
                            <div class="mb-3">
                                <i class="bi bi-search text-muted" style="font-size: 3rem;"></i>
                            </div>
                            <h5 class="text-muted">未找到相关文章</h5>
                            <p class="text-muted">
                                没有找到包含 "<strong>${ searchKeyword }$</strong>" 的文章，请尝试其他关键词
                            </p>
                            <button class="btn btn-outline-primary" @click="clearSearch">
                                <i class="bi bi-arrow-clockwise me-1"></i>清除搜索
                            </button>
                        </div>

                        <!-- 正常的文章列表 -->
                        <div v-else class="row">
                            <div v-for="article in filteredArticles" :key="article.article_id" class="col-md-6 mb-4">
                                <div class="article-card card h-100" @click="showArticleDetail(article)">
                                    <!-- 图片在顶部 -->
                                    <div class="article-image-top">
                                        <img :src="article.image_url || '/static/images/default-article.jpg'"
                                             :alt="article.title" class="card-img-top article-thumbnail">
                                    </div>
                                    <div class="card-body">
                                        <!-- 标题 -->
                                        <h6 class="article-title fw-bold mb-2" v-html="highlightSearchKeyword(article.title)"></h6>
                                        <!-- 文章开头几句文字 -->
                                        <p class="article-summary text-muted mb-3" v-html="highlightSearchKeyword(article.content.substring(0, 100) + '...')"></p>
                                        <!-- 底部元信息 -->
                                        <div class="article-meta d-flex justify-content-between align-items-center">
                                            <span class="badge bg-primary">${ article.category }$</span>
                                            <small class="text-muted">
                                                <i class="bi bi-eye me-1"></i>${ article.read_count || 0 }$
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 分页控件 -->
                    <div v-if="totalPages > 1" class="d-flex justify-content-center mt-4">
                        <nav>
                            <ul class="pagination">
                                <li class="page-item" :class="{ disabled: currentPage === 1 }">
                                    <button class="page-link" @click="changePage(currentPage - 1)" :disabled="currentPage === 1">
                                        上一页
                                    </button>
                                </li>
                                <li v-for="page in visiblePages" :key="page" class="page-item" :class="{ active: page === currentPage }">
                                    <button class="page-link" @click="changePage(page)">${ page }$</button>
                                </li>
                                <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                                    <button class="page-link" @click="changePage(currentPage + 1)" :disabled="currentPage === totalPages">
                                        下一页
                                    </button>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- 文章详情页 -->
            <div v-if="articleExpanded">
                <button class="btn btn-outline-secondary mb-4" @click="closeArticle">
                    <i class="bi bi-arrow-left me-2"></i> 返回文章列表
                </button>

                <h1 class="article-title-large">${ selectedArticle.title }$</h1>
                <div class="article-difficulty mb-3">
                    <i class="bi bi-star-fill me-1"></i>
                    <i class="bi bi-star-fill me-1"></i>
                    <span class="ms-1 text-muted">适中难度 | 阅读时间约 5 分钟</span>
                </div>



                <!-- 正文内容 - 参考微信公众号风格 -->
                <div class="article-content wechat-style">
                    <!-- 文章头图 -->
                    <div class="article-header-image mb-4">
                        <img :src="selectedArticle.image_url || '/static/images/default-article.jpg'"
                             class="w-100 rounded" style="max-height: 300px; object-fit: cover;"
                             :alt="selectedArticle.title">
                    </div>

                    <!-- 文章摘要 -->
                    <div class="article-summary-box mb-4">
                        <div class="summary-header">
                            <i class="bi bi-lightbulb-fill text-warning me-2"></i>
                            <strong>文章导读</strong>
                        </div>
                        <p class="summary-text">${ selectedArticle.content ? selectedArticle.content.substring(0, 150) : '' }$...</p>
                    </div>

                    <!-- 详细内容 -->
                    <div class="content-section">
                        <div class="content-text">
                            <!-- 如果有详细分段内容，显示分段内容 -->
                            <div v-if="selectedArticle.detailedContent && selectedArticle.detailedContent.sections">
                                <div v-for="section in selectedArticle.detailedContent.sections" :key="section.title" class="article-section">
                                    <h4 class="section-title">${ section.title }$</h4>
                                    <div class="section-content" v-html="section.content.replace(/\n\n/g, '</p><p>')"></div>
                                </div>
                            </div>
                            <!-- 否则显示普通内容 -->
                            <div v-else>
                                <p>${ selectedArticle.content }$</p>

                                <!-- 健康提示框 -->
                                <div class="health-tip-box">
                                    <div class="tip-icon">
                                        <i class="bi bi-heart-pulse-fill"></i>
                                    </div>
                                    <div class="tip-content">
                                        <h6>健康小贴士</h6>
                                        <p>根据您的年龄和健康状况，建议定期关注相关健康指标，如有异常请及时就医。</p>
                                    </div>
                                </div>

                                <!-- 相关建议 -->
                                <div class="recommendations-box">
                                    <h5><i class="bi bi-clipboard-check me-2 text-success"></i>专家建议</h5>
                                    <ul class="recommendation-list">
                                        <li>保持良好的生活习惯，规律作息</li>
                                        <li>适量运动，增强身体抵抗力</li>
                                        <li>定期体检，及时发现健康问题</li>
                                        <li>遇到疑问时，及时咨询专业医生</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info border-0 bg-light">
                        <i class="bi bi-info-circle-fill me-2 text-primary"></i>
                        <strong>温馨提醒：</strong> 本文内容仅供参考，如有疑问或特殊情况，请及时咨询专业医生。
                    </div>
                </div>



                <!-- 文章操作按钮 -->
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div>
                        <button class="btn btn-outline-primary me-2" @click="toggleFavorite">
                            <i class="bi" :class="isArticleFavorited ? 'bi-heart-fill' : 'bi-heart'"></i>
                            ${ isArticleFavorited ? '已收藏' : '收藏' }$
                        </button>
                        <button class="btn btn-outline-secondary" @click="shareArticle">
                            <i class="bi bi-share me-1"></i>分享
                        </button>
                    </div>
                    <button class="btn btn-secondary" @click="closeArticle">
                        <i class="bi bi-arrow-left me-1"></i>返回列表
                    </button>
                </div>
            </div>
        </div>
    </div>



    {% include "emergency_modal.html" %}
    {% include "food_order_modals.html" %}
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.bootcdn.net/ajax/libs/Chart.js/3.7.0/chart.min.js"></script>
<script src="{{ url_for('static', filename='js/food_order.js') }}?v=2024010602"></script>
<script src="{{ url_for('static', filename='js/emergency_call.js') }}?v=2024010602"></script>
<script src="{{ url_for('static', filename='js/elderly_center.js') }}?v=2024010602"></script>
{% endblock %}

