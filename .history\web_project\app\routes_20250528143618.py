from flask import render_template, redirect, url_for, request, flash, session, Blueprint
import os

# 创建蓝图
main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """网站主页"""
    return render_template('index.html')

@main_bp.route('/elderly')
def elderly_center():
    """老年人中心入口"""
    # 这里应该有登录验证，暂时简化处理
    return render_template('elderly_center.html')

@main_bp.route('/elderly/emergency')
def elderly_emergency():
    """老年人紧急呼叫页面"""
    return render_template('elderly_emergency.html')

@main_bp.route('/family')
def family_center():
    """家属监护中心入口"""
    # 这里应该有登录验证，暂时简化处理
    return render_template('family_center.html')

@main_bp.route('/family/monitoring')
def family_monitoring():
    """家属监控页面（旧版）"""
    return render_template('family_monitoring.html')

@main_bp.route('/worker')
def worker_center():
    """社区工作人员中心入口"""
    # 这里应该有登录验证，暂时简化处理
    return render_template('worker_dashboard.html')

@main_bp.route('/login')
def login():
    """登录页面"""
    return render_template('login.html')

@main_bp.route('/register')
def register():
    """注册页面"""
    return render_template('register.html')

@main_bp.route('/logout')
def logout():
    """退出登录"""
    # 清除会话
    session.clear()
    return redirect(url_for('main.index'))

@main_bp.route('/fix')
def fix_data():
    """修复数据库页面"""
    return render_template('fix_data.html')