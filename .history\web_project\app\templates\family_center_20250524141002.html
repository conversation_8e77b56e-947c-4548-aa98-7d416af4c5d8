{% extends "base.html" %}
{% block title %}家属监护中心{% endblock %}
{% block head %}
<link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&family=Noto+Serif+SC:wght@400;600;700&display=swap" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<style>
body {
    background: #f6f8fa;
    font-family: 'Noto Sans SC', sans-serif;
    color: #333;
}

.monitor-topbar {
    background: #f6f8fa;
    border-bottom: 1px solid #e9ecef;
    padding: 0.8rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
    z-index: 100;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    color: #333;
}

.monitor-topbar .top-actions > * { margin-right: 1.5rem; }
.monitor-topbar .top-actions > *:last-child { margin-right: 0; }

/* 顶栏按钮样式 */
.monitor-topbar .btn {
    border-radius: 8px;
    padding: 0.6rem 1.2rem;
    font-weight: 500;
    font-size: 1.05rem;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.monitor-topbar .btn-danger {
    background-color: #ff4d4f;
    border-color: #ff4d4f;
    box-shadow: 0 2px 6px rgba(255, 77, 79, 0.2);
}

.monitor-topbar .btn-danger:hover {
    background-color: #ff7875;
    border-color: #ff7875;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(255, 77, 79, 0.3);
}

.monitor-topbar .btn-outline-primary {
    color: #1765d5;
    border-color: #1765d5;
    background-color: transparent;
}

.monitor-topbar .btn-outline-primary:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.7);
    transform: translateY(-2px);
}

/* 用户下拉菜单样式 */
.user-dropdown {
    position: relative;
    display: inline-block;
    cursor: pointer;
}

.user-dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.1);
    transition: all 0.3s;
}

.user-dropdown-toggle:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.user-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    width: 220px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    z-index: 1000;
    overflow: hidden;
    margin-top: 0.5rem;
    visibility: hidden;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s;
}

.user-dropdown-menu.show {
    visibility: visible;
    opacity: 1;
    transform: translateY(0);
}

.user-dropdown-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 0.8rem 1.2rem;
    color: #333;
    text-decoration: none;
    transition: all 0.2s;
}

.user-dropdown-item:hover {
    background-color: #f0f7ff;
    color: #1765d5;
}

.user-dropdown-item i {
    font-size: 1.1rem;
    color: #666;
}

.user-dropdown-item:hover i {
    color: #1765d5;
}

.user-dropdown-divider {
    height: 1px;
    background-color: #e9ecef;
    margin: 0.5rem 0;
}

.user-dropdown-header {
    padding: 1rem 1.2rem;
    background-color: #f0f7ff;
    border-bottom: 1px solid #e9ecef;
}

.user-dropdown-header .user-name {
    font-weight: 600;
    color: #1765d5;
    font-size: 1.1rem;
    margin-bottom: 0.3rem;
}

.user-dropdown-header .user-role {
    color: #666;
    font-size: 0.9rem;
}

.monitor-layout {
    display: flex;
    flex-direction: column;  /* 修改为纵向布局 */
    height: calc(100vh - 56px); /* 调整高度，减去navbar的高度 */
    position: relative;
}

.monitor-content-wrapper {
    display: flex;
    flex: 1;
}

/* 侧边栏改进 */
.monitor-sidemenu {
    width: 280px;
    background: #fff;
    border-right: 1px solid #e9ecef;
    padding: 1.8rem 0.5rem 1.5rem 1.5rem;
    overflow-y: auto;
    box-shadow: 2px 0 10px rgba(0,0,0,0.03);
    flex-shrink: 0; /* 防止侧边栏被挤压 */
}
.monitor-sidemenu .menu-title {
    font-family: 'Noto Serif SC', serif;
    font-weight: 600;
    margin: 1.5rem 0 0.8rem 0.5rem;
    color: #1765d5;
    font-size: 1.25rem;
    display: flex;
    align-items: center;
    gap: 10px;
}
.monitor-sidemenu ul {
    list-style: none;
    padding-left: 1.2rem;
    margin-bottom: 1rem;
}
.monitor-sidemenu li {
    margin-bottom: 0.7rem;
    cursor: pointer;
    color: #4a5a6a;
    border-radius: 8px;
    padding: 0.5rem 0.8rem;
    transition: all 0.25s ease;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 10px;
}
.monitor-sidemenu li i {
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
    opacity: 0.8;
}
.monitor-sidemenu li.active, .monitor-sidemenu li:hover {
    background: #e6f0ff;
    color: #1765d5;
    box-shadow: 0 2px 8px rgba(23, 101, 213, 0.15);
    transform: translateX(5px);
}
.monitor-sidemenu li.active i, .monitor-sidemenu li:hover i {
    opacity: 1;
}

.monitor-workspace {
    flex: 1;
    padding: 1.8rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    background-color: #f8fafd;
}
.monitor-split {
    display: flex;
    gap: 1.8rem;
    flex: 1;
}
.monitor-panel {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.06);
    padding: 1.5rem 1.8rem;
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s, box-shadow 0.3s;
    border: 1px solid rgba(0,0,0,0.03);
}
.monitor-panel:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.08);
}
.monitor-panel-header {
    font-family: 'Noto Serif SC', serif;
    font-weight: 700;
    font-size: 1.5rem;
    margin-bottom: 0.8rem;
    color: #1765d5;
    display: flex;
    align-items: center;
    gap: 10px;
}
.monitor-panel-desc {
    color: #666;
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}
.health-matrix {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0 0.6rem;
    margin-bottom: 1.8rem;
}
.health-matrix td {
    border: 1px solid #e9ecef;
    padding: 1rem 1.5rem;
    text-align: center;
    font-size: 1.15rem;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.04);
    transition: transform 0.2s ease;
}
.health-matrix td:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0,0,0,0.08);
}
.health-matrix td[data-status="normal"] {
    background: #f6fff6;
    color: #2e7d32;
    border-left: 4px solid #2e7d32;
}
.health-matrix td[data-status="warning"] {
    background: #fffbe6;
    color: #faad14;
    font-weight: bold;
    border-left: 4px solid #faad14;
}
.health-matrix td[data-status="alert"] {
    background: #fff1f0;
    color: #ff4d4f;
    font-weight: bold;
    animation: blink 1s infinite;
    border-left: 4px solid #ff4d4f;
}
@keyframes blink { 0%{opacity:1;} 50%{opacity:0.5;} 100%{opacity:1;} }
.radar-container {
    width: 100%;
    max-width: 350px;
    margin: 0 auto;
}
.trend-chart-container {
    height: 400px; /* 增大高度 */
    margin-bottom: 2.5rem; /* 增加间距 */
    background-color: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}
.trend-chart-center {
    height: 450px; /* 进一步增大高度 */
    margin-bottom: 3rem; /* 增加间距 */
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 2rem; /* 下移 */
}
.amap-container {
    width: 100%;
    height: 320px;
    border-radius: 15px;
    overflow: hidden;
    margin-bottom: 1.8rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}
.table-responsive {
    max-height: 300px;
    overflow-y: auto;
    border-radius: 10px;
    box-shadow: 0 3px 12px rgba(0,0,0,0.05);
}
.meal-bar-warning {
    color: #fff;
    background: #ff7875;
    border-radius: 50px;
    padding: 0.2rem 0.8rem;
    font-size: 1rem;
    margin-left: 8px;
    box-shadow: 0 2px 6px rgba(255,120,117,0.2);
}
.calendar-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1.5rem;
    box-shadow: 0 3px 12px rgba(0,0,0,0.05);
    border-radius: 10px;
    overflow: hidden;
}
.calendar-table th, .calendar-table td {
    border: 1px solid #e9ecef;
    text-align: center;
    padding: 0.8rem 0.5rem;
    font-size: 1.05rem;
}
.calendar-table th {
    background-color: #f8fafd;
    font-weight: 600;
    color: #1765d5;
}
.calendar-table .conflict {
    background: #fff1f0;
    color: #ff4d4f;
    font-weight: bold;
}
.timeline {
    list-style: none;
    padding-left: 0;
    margin-top: 1rem;
}
.timeline li {
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.05);
    transition: transform 0.2s ease;
    position: relative;
    border-left: 4px solid #1765d5;
    display: flex;
    align-items: center;
}
.timeline li:hover {
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}
.timeline li::before {
    content: '';
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    background-color: #1765d5;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 0 0 2px #1765d5;
}
.timeline .ai-flag {
    color: #faad14;
    font-weight: bold;
    margin-left: 0.8rem;
    background-color: #fffbe6;
    padding: 0.2rem 0.6rem;
    border-radius: 50px;
    font-size: 0.9rem;
}
.timeline .ai-btn {
    margin-left: 1rem;
    font-size: 1rem;
    background-color: #1765d5;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 0.3rem 0.8rem;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    transition: all 0.2s ease;
}
.timeline .ai-btn:hover {
    background-color: #104db3;
    box-shadow: 0 3px 8px rgba(23,101,213,0.3);
}

/* 实时体征看板样式 - 修改为蓝色风格并增大字体 */
.vital-signs-summary {
    background: #f0f8ff;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-left: 6px solid #1765d5;
    box-shadow: 0 5px 15px rgba(23,101,213,0.1);
}

.vital-signs-summary .summary-title {
    color: #1765d5;
    font-weight: bold;
    margin-bottom: 0.8rem;
    font-size: 1.4rem;
    font-family: 'Noto Serif SC', serif;
    display: flex;
    align-items: center;
}

.vital-signs-summary .summary-content {
    color: #333;
    font-size: 1.15rem;
    line-height: 1.6;
}

.vital-signs-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0 1rem;
    margin-bottom: 2rem;
}

.vital-signs-table th {
    text-align: left;
    padding: 0.8rem 1.2rem;
    color: #333;
    font-weight: 600;
    font-size: 1.2rem;
    font-family: 'Noto Serif SC', serif;
    vertical-align: middle;
    white-space: nowrap; /* 添加不换行属性 */
    display: flex;
    align-items: center;
    width: auto;
    min-width: 220px;
}

.vital-signs-table td {
    padding: 1.2rem 1.5rem;
    background: #e6f0ff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(91,143,249,0.15);
    border: 1.5px solid #5B8FF9;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    min-width: 100px;
    vertical-align: middle;
}

.vital-signs-table td:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(91,143,249,0.2);
}

.vital-signs-table td.value {
    font-weight: bold;
    text-align: right;
}

.vital-signs-table td[data-status="normal"] .value {
    color: #2e7d32;
}

.vital-signs-table td[data-status="warning"] .value {
    color: #333;
    font-weight: 700;
}

.vital-signs-table td[data-status="alert"] .value {
    color: #ff4d4f;
    animation: blink 1s infinite;
}

.vital-signs-table td[data-status="warning"],
.vital-signs-table td[data-status="alert"] {
    background: #e6f0ff;
    border: 1.5px solid #5B8FF9;
}

.vital-signs-table td[data-status="alert"] {
    background: #fff1f0;
    border-color: #ff7875;
}

.vital-signs-table .trend {
    font-size: 1.1rem;
    margin-left: 0.6rem;
    font-weight: bold;
}

.vital-signs-table .trend.up {
    color: #ff4d4f;
}

.vital-signs-table .trend.down {
    color: #52c41a;
}

.vital-signs-table .unit {
    font-size: 1.1rem;
    color: #555;
    margin-left: 0.4rem;
}

.vital-signs-table .value {
    font-size: 1.5rem;
    color: #333;
    font-weight: 700;
}

.vital-signs-table .last-update {
    font-size: 0.95rem;
    color: #666;
    margin-top: 0.6rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

/* 用药提醒样式 */
.medication-reminder {
    background: #fff8f0;
    border-radius: 10px;
    padding: 1.2rem 1.5rem;
    margin-bottom: 1.5rem;
    border-left: 4px solid #ffa940;
    box-shadow: 0 4px 12px rgba(255,169,64,0.15);
}

.medication-reminder h4 {
    color: #fa8c16;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.medication-cards {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.medication-card {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    border: 1px solid #ffe7ba;
}

.medication-card h5 {
    color: #fa8c16;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.medication-card p {
    margin-bottom: 0.3rem;
    font-size: 0.95rem;
}

/* 生活监控模块样式 */
.lifestyle-monitor {
    background: #f0f7ff;
    border-radius: 16px;
    padding: 1.8rem 2rem;
    margin-bottom: 2.5rem;
    border-left: 6px solid #1765d5;
    box-shadow: 0 6px 20px rgba(23,101,213,0.12);
}

.lifestyle-monitor h4 {
    color: #1765d5;
    margin-bottom: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.7rem;
    font-size: 1.6rem;
    font-family: 'Noto Serif SC', serif;
    font-weight: 700;
}

.lifestyle-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
}

.lifestyle-card {
    background: white;
    border-radius: 14px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    border: 1px solid rgba(23,101,213,0.1);
    transition: transform 0.3s, box-shadow 0.3s;
}

.lifestyle-card:hover {
    transform: translateY(-7px);
    box-shadow: 0 12px 25px rgba(23,101,213,0.15);
}

.lifestyle-card h5 {
    color: #1765d5;
    margin-bottom: 1rem;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.6rem;
    font-family: 'Noto Serif SC', serif;
    font-weight: 600;
}

.lifestyle-card h5 i {
    font-size: 1.5rem;
}

.lifestyle-card p {
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.lifestyle-card .data-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.7rem;
    font-size: 1.1rem;
}

.lifestyle-card .data-row strong {
    font-weight: 600;
    color: #555;
}

.lifestyle-card .data-row span {
    color: #333;
}

.lifestyle-card .data-row .fw-bold {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1765d5;
}

.lifestyle-card .progress {
    height: 10px;
    margin-top: 0.6rem;
    margin-bottom: 1.2rem;
    border-radius: 5px;
    background-color: #e9ecef;
    overflow: hidden;
}

.lifestyle-card .progress-bar {
    transition: width 0.6s ease;
}

.lifestyle-card .card-footer {
    margin-top: 1.2rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
    font-size: 1rem;
    color: #666;
}

.lifestyle-card .trend-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    font-weight: 500;
}

.trend-indicator.positive {
    color: #52c41a;
}

.trend-indicator.negative {
    color: #ff4d4f;
}

.trend-indicator.neutral {
    color: #faad14;
}

.trend-indicator i {
    font-size: 1.1rem;
}

/* 历史趋势分析样式 */
.trend-analysis-container {
    display: flex;
    gap: 2.5rem;
    margin-bottom: 2rem;
}

.trend-chart-box {
    flex: 1.3;
    background: white;
    border-radius: 16px;
    padding: 1.8rem;
    box-shadow: 0 5px 20px rgba(91,143,249,0.1);
    border: 1px solid rgba(91,143,249,0.1);
    transition: transform 0.3s ease;
}

.trend-analysis-box {
    flex: 1.1;
    min-width: 450px;
    background: #f7faff;
    border-radius: 16px;
    padding: 1.8rem;
    box-shadow: 0 5px 20px rgba(91,143,249,0.08);
    display: flex;
    flex-direction: column;
    border-left: 6px solid #1765d5;
    transition: transform 0.3s ease;
}

.trend-analysis-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 30px rgba(91,143,249,0.12);
}

.analysis-title {
    color: #1765d5;
    font-weight: bold;
    margin-bottom: 1.2rem;
    font-size: 1.5rem;
    font-family: 'Noto Serif SC', serif;
}

.analysis-content {
    flex: 1;
    color: #333;
    font-size: 1.1rem;
    line-height: 1.6;
}

.analysis-content ul {
    padding-left: 1.8rem;
    margin-top: 0.8rem;
    margin-bottom: 1.2rem;
}

.analysis-content li {
    margin-bottom: 0.8rem;
    position: relative;
}

.analysis-content li::marker {
    color: #1765d5;
    font-size: 1.1em;
}

.risk-tags-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2.5rem;
    margin-top: 2rem;
    padding: 1rem 0;
}

.risk-tag {
    min-width: 200px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    font-weight: 600;
    border-radius: 12px;
    padding: 0 1.8rem;
    border: 2px solid;
    text-align: center;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.risk-tag:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.1);
}

.risk-tag.warning {
    background: #fffbe6;
    color: #faad14;
    border-color: #ffe58f;
}

.risk-tag.alert {
    background: #fff1f0;
    color: #ff4d4f;
    border-color: #ffa39e;
}

/* 实时位置与安全围栏样式 - 修改为单一地图 */
.location-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}
.map-container {
    width: 100%;
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 12px rgba(91,143,249,0.08);
}
.map-title {
    color: #1765d5;
    margin-bottom: 1rem;
    font-weight: 600;
}
.fence-controls {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
    margin-bottom: 1rem;
}
.entry-exit-container {
    width: 100%;
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 12px rgba(91,143,249,0.08);
}
.entry-exit-table {
    width: 100%;
    border-collapse: collapse;
}
.entry-exit-table th {
    background: #f0f8ff;
    padding: 0.8rem;
    text-align: left;
    border-bottom: 2px solid #e6f0ff;
    color: #1765d5;
    font-weight: 600;
}
.entry-exit-table td {
    padding: 0.8rem;
    border-bottom: 1px solid #e6f0ff;
}

/* 应急响应中心样式改进 */
.emergency-response {
    padding: 1rem;
}

.emergency-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1.5rem;
    font-size: 1.05rem;
}

.emergency-table th {
    background: #f0f8ff;
    padding: 1rem;
    text-align: left;
    border-bottom: 2px solid #e6f0ff;
    color: #1765d5;
    font-weight: 600;
}

.emergency-table td {
    padding: 1.2rem 1rem;
    border-bottom: 1px solid #e6f0ff;
}

.emergency-badge {
    display: inline-block;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    font-size: 0.95rem;
    font-weight: 500;
}

.emergency-badge.danger {
    background: #fff1f0;
    color: #ff4d4f;
}

.emergency-badge.warning {
    background: #fffbe6;
    color: #faad14;
}

.emergency-progress {
    height: 24px;
    background: #f0f8ff;
    border-radius: 12px;
    overflow: hidden;
    margin-top: 1.5rem;
}

.emergency-progress-bar {
    height: 100%;
    background: #52c41a;
    color: white;
    text-align: center;
    line-height: 24px;
    font-weight: 500;
    transition: width 0.5s;
}

.emergency-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.emergency-actions button {
    padding: 0.8rem 1.2rem;
    border-radius: 8px;
    border: none;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.emergency-actions .primary {
    background: #1765d5;
    color: white;
}

.emergency-actions .danger {
    background: #ff4d4f;
    color: white;
}

/* 医疗档案管理样式改进 */
.medical-records {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 1.8rem;
    margin: 2rem 0;
    width: 100%;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.medical-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: all 0.3s;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(0, 0, 0, 0.05);
    height: 100%;
}

.medical-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
    border-color: #1765d5;
}

.medical-card .card-header {
    padding: 1.2rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-bottom: 1px solid #f0f0f0;
    background: linear-gradient(to right, #e6f0ff, #f0f7ff);
}

.medical-card .card-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #1765d5, #5B8FF9);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: 0 4px 10px rgba(23, 101, 213, 0.2);
}

.medical-card .card-title-wrapper {
    flex: 1;
}

.medical-card .card-title {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
    color: #1765d5;
    font-family: 'Noto Serif SC', serif;
}

.medical-card .card-date {
    font-size: 0.9rem;
    color: #666;
    display: block;
    margin-top: 0.3rem;
}

.medical-card .card-content {
    padding: 1.5rem;
    flex: 1;
}

.medical-card .card-content p {
    font-size: 1.1rem;
    color: #333;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.medical-card .card-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.medical-card .card-tag {
    font-size: 0.85rem;
    padding: 0.3rem 0.8rem;
    border-radius: 1rem;
    background: #f0f0f0;
    color: #555;
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
}

.medical-card .card-tag.warning {
    background: #fffbe6;
    color: #d48806;
}

.medical-card .card-actions {
    padding: 1rem 1.5rem;
    display: flex;
    gap: 0.8rem;
    border-top: 1px solid #f0f0f0;
    background: #fafafa;
}

.medical-card .action-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #d9d9d9;
    background: white;
    border-radius: 0.5rem;
    color: #666;
    flex: 1;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.4rem;
    transition: all 0.2s;
    cursor: pointer;
    font-size: 0.95rem;
}

.medical-card .action-btn:hover {
    border-color: #1765d5;
    color: #1765d5;
    background: #f0f7ff;
}

.medical-card .action-btn.primary {
    background: #1765d5;
    color: white;
    border-color: #1765d5;
}

.medical-card .action-btn.primary:hover {
    background: #0d5bc2;
    box-shadow: 0 3px 8px rgba(23, 101, 213, 0.2);
}

@media (max-width: 768px) {
    .medical-records {
        grid-template-columns: 1fr;
        grid-template-rows: auto;
    }

    .medical-records-header {
        flex-direction: column;
        align-items: stretch;
    }

    .upload-btn {
        width: 100%;
    }
}

/* 医疗档案管理相关样式 */
.medical-records-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.medical-records-search {
    flex: 1;
    min-width: 280px;
}

.upload-btn {
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    border-radius: 0.5rem;
    background: linear-gradient(135deg, #1765d5, #5B8FF9);
    border: none;
    box-shadow: 0 4px 15px rgba(23, 101, 213, 0.2);
    transition: all 0.3s;
    font-size: 1.1rem;
}

.upload-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(23, 101, 213, 0.3);
    background: linear-gradient(135deg, #0d5bc2, #4a7fe6);
}

.medical-records-filter {
    display: flex;
    gap: 0.8rem;
    flex-wrap: wrap;
}

.filter-btn {
    background: #f0f7ff;
    border: 1px solid #d9e8ff;
    color: #1765d5;
    padding: 0.6rem 1.2rem;
    border-radius: 2rem;
    font-weight: 500;
    transition: all 0.2s;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-btn:hover, .filter-btn.active {
    background: #1765d5;
    color: white;
    border-color: #1765d5;
    box-shadow: 0 3px 10px rgba(23, 101, 213, 0.2);
}

.medical-records {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 1.8rem;
    margin: 2rem 0;
    width: 100%;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.medical-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: all 0.3s;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(0, 0, 0, 0.05);
    height: 100%;
}

.medical-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
    border-color: #1765d5;
}

.medical-card .card-header {
    padding: 1.2rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-bottom: 1px solid #f0f0f0;
    background: linear-gradient(to right, #e6f0ff, #f0f7ff);
}

.medical-card .card-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #1765d5, #5B8FF9);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: 0 4px 10px rgba(23, 101, 213, 0.2);
}

.medical-card .card-title-wrapper {
    flex: 1;
}

.medical-card .card-title {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
    color: #1765d5;
    font-family: 'Noto Serif SC', serif;
}

.medical-card .card-date {
    font-size: 0.9rem;
    color: #666;
    display: block;
    margin-top: 0.3rem;
}

.medical-card .card-content {
    padding: 1.5rem;
    flex: 1;
}

.medical-card .card-content p {
    font-size: 1.1rem;
    color: #333;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.medical-card .card-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.medical-card .card-tag {
    font-size: 0.85rem;
    padding: 0.3rem 0.8rem;
    border-radius: 1rem;
    background: #f0f0f0;
    color: #555;
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
}

.medical-card .card-tag.warning {
    background: #fffbe6;
    color: #d48806;
}

.medical-card .card-actions {
    padding: 1rem 1.5rem;
    display: flex;
    gap: 0.8rem;
    border-top: 1px solid #f0f0f0;
    background: #fafafa;
}

.medical-card .action-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #d9d9d9;
    background: white;
    border-radius: 0.5rem;
    color: #666;
    flex: 1;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.4rem;
    transition: all 0.2s;
    cursor: pointer;
    font-size: 0.95rem;
}

.medical-card .action-btn:hover {
    border-color: #1765d5;
    color: #1765d5;
    background: #f0f7ff;
}

.medical-card .action-btn.primary {
    background: #1765d5;
    color: white;
    border-color: #1765d5;
}

.medical-card .action-btn.primary:hover {
    background: #0d5bc2;
    box-shadow: 0 3px 8px rgba(23, 101, 213, 0.2);
}

@media (max-width: 768px) {
    .medical-records {
        grid-template-columns: 1fr;
        grid-template-rows: auto;
    }

    .medical-records-header {
        flex-direction: column;
        align-items: stretch;
    }

    .upload-btn {
        width: 100%;
    }
}

/* 活动行为分析样式 */
.activity-analysis-container {
    display: flex;
    gap: 2.5rem;
    margin-bottom: 2rem;
    width: 100%;
    justify-content: center; /* 添加居中对齐 */
}

.activity-chart-wrapper {
    flex: 1;
    background: #f7faff;
    border-radius: 16px;
    padding: 1.8rem;
    box-shadow: 0 5px 20px rgba(91,143,249,0.08);
    border: 1px solid #e6f0ff;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;
    align-items: center; /* 添加居中对齐 */
}

.activity-chart {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 1rem 0;
    flex: 1;
    width: 100%; /* 确保图表占满容器宽度 */
}

/* 生活服务代理样式 */
.service-container {
    display: flex;
    gap: 2.5rem;
    width: 100%;
    margin-top: 1.5rem;
}

.service-form-wrapper {
    flex: 1;
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(91,143,249,0.08);
    border: 1px solid #e6f0ff;
}

.service-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
    font-size: 1.1rem;
}

.input-wrapper {
    position: relative;
}

.input-wrapper i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #1765d5;
    pointer-events: none;
    z-index: 10; /* 提高图标的z-index以确保显示 */
    text-align: right; /* 确保图标文本靠右 */
}

.form-control, .form-select {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    font-size: 1.1rem;
    transition: all 0.3s;
    position: relative;
    background-color: white;
    -webkit-appearance: auto; /* 恢复为原生样式 */
    -moz-appearance: auto; /* 恢复为原生样式 */
    appearance: auto; /* 恢复为原生样式 */
}

.form-control:focus, .form-select:focus {
    border-color: #1765d5;
    box-shadow: 0 0 0 3px rgba(23,101,213,0.1);
    outline: none;
}

/* 调整日期选择器右侧图标 */
input[type="date"]::-webkit-calendar-picker-indicator {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
}

.btn-submit {
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #1765d5, #5B8FF9);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1rem;
    align-self: center;
}

.btn-submit:hover {
    background: linear-gradient(135deg, #104db3, #4a7fe6);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(23,101,213,0.2);
}

.service-history {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.nutrition-advice {
    background: #f0f8ff;
    border-radius: 12px;
    padding: 1.5rem;
    border-left: 4px solid #1765d5;
    box-shadow: 0 4px 15px rgba(23,101,213,0.1);
}

.nutrition-advice h5 {
    color: #1765d5;
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.history-title {
    margin: 1rem 0;
    color: #333;
    font-weight: 600;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.history-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.history-item {
    display: flex;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 3px 15px rgba(0,0,0,0.05);
    transition: transform 0.2s ease;
    border: 1px solid #f0f0f0;
}

.history-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.08);
}

.history-date {
    background: #f0f7ff;
    padding: 1rem;
    font-weight: 500;
    color: #1765d5;
    display: flex;
    align-items: center;
    min-width: 120px;
    justify-content: center;
    border-right: 1px dashed #d9e8ff;
}

.history-content {
    padding: 1rem 1.5rem;
    flex: 1;
    position: relative;
}

.history-content p {
    margin: 0 0 0.3rem 0;
}

.status-tag {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    padding: 0.3rem 0.8rem;
    border-radius: 50px;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
}

.status-tag.done {
    background: #f6ffed;
    color: #52c41a;
}

.status-tag.pending {
    background: #fff7e6;
    color: #fa8c16;
}

.service-subtitle {
    color: #1765d5;
    font-weight: 600;
    margin-bottom: 1.5rem;
    font-size: 1.4rem;
    border-bottom: 2px solid rgba(23,101,213,0.1);
    padding-bottom: 0.8rem;
    display: flex;
    align-items: center;
}

.form-select {
    -webkit-appearance: menulist !important; /* 强制使用原生下拉菜单样式 */
    -moz-appearance: menulist !important;
    appearance: menulist !important;
    background-image: none !important; /* 移除任何自定义背景箭头 */
}

/* 移除.input-wrapper内的图标显示 */
.input-wrapper i {
    display: none;
}

/* 改善图标和文字的布局 */
.vital-signs-table i {
    margin-right: 10px;
    vertical-align: middle;
    font-size: 1.3rem;
    display: inline-block; /* 确保图标是内联块元素 */
}

/* 确保文本容器足够宽 */
.vital-signs-table td {
    min-width: 200px;
    padding: 0.8rem 1.2rem;
    vertical-align: middle;
}

/* 确保th标签中的内容不会换行，且保持在一行 */
.vital-signs-table th {
    display: flex;
    align-items: center;
    white-space: nowrap;
    width: auto;
    min-width: 220px;
}
</style>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
{% endblock %}

{% block footer %}
<!-- 在家属监护中心不显示页脚 -->
{% endblock %}

{% block content %}
<div id="monitor-app">
    <div class="monitor-layout">
        <div class="monitor-topbar">
            <h1 class="mb-0 fs-4">家属监护中心</h1>
            <div class="top-actions d-flex align-items-center">
                <button class="btn btn-outline-primary">
                    <i class="bi bi-speedometer2"></i> 实时模式
                </button>
                <button class="btn btn-danger">
                    <i class="bi bi-megaphone"></i> 一键呼叫
                </button>
            </div>
        </div>

        <div class="monitor-content-wrapper">
            <aside class="monitor-sidemenu">
                <div class="menu-title"><i class="bi bi-grid-1x2-fill"></i> 监护模块</div>
                <ul>
                    <li :class="{'active': currentMenu === '实时体征看板'}" @click="selectMenu('实时体征看板')">
                        <i class="bi bi-activity"></i> 实时体征看板
                    </li>
                    <li :class="{'active': currentMenu === '实时定位监控'}" @click="selectMenu('实时定位监控')">
                        <i class="bi bi-geo-alt-fill"></i> 实时定位监控
                    </li>
                    <li :class="{'active': currentMenu === '医疗档案管理'}" @click="selectMenu('医疗档案管理')">
                        <i class="bi bi-file-medical"></i> 医疗档案管理
                    </li>
                </ul>
                <div class="menu-title"><i class="bi bi-shield-fill-check"></i> 关怀模块</div>
                <ul>
                    <li :class="{'active': currentMenu === '历史趋势分析'}" @click="selectMenu('历史趋势分析')">
                        <i class="bi bi-graph-up"></i> 历史趋势分析
                    </li>
                    <li :class="{'active': currentMenu === '活动行为分析'}" @click="selectMenu('活动行为分析')">
                        <i class="bi bi-person-lines-fill"></i> 活动行为分析
                    </li>
                    <li :class="{'active': currentMenu === '生活服务代理'}" @click="selectMenu('生活服务代理')">
                        <i class="bi bi-basket-fill"></i> 生活服务代理
                    </li>
                    <li :class="{'active': currentMenu === '应急响应中心'}" @click="selectMenu('应急响应中心')">
                        <i class="bi bi-exclamation-triangle-fill"></i> 应急响应中心
                    </li>
                </ul>
            </aside>

            <div class="monitor-workspace">
                <!-- 删除首页被截图的内容部分 -->

                <section class="monitor-panel" v-if="currentMenu==='历史趋势分析'">
                    <div class="monitor-panel-header">历史趋势分析</div>
                    <div class="monitor-panel-desc">基于长期数据分析健康状况变化趋势，识别潜在风险并生成健康建议。</div>

                    <div class="trend-analysis-container">
                        <div class="trend-chart-box">
                            <h4 class="chart-title">血压与血糖变化趋势（近7天）</h4>
                            <div class="trend-chart-center">
                                <canvas id="trendChart" width="700" height="400"></canvas>
                            </div>
                        </div>
                        <div class="trend-analysis-box">
                            <h4 class="analysis-title">AI健康分析报告</h4>
                            <div class="analysis-content">
                                <p>根据近7天的健康数据分析：</p>
                                <ul>
                                    <li>血压指标总体处于<strong>正常范围</strong>，但波动性略大</li>
                                    <li>血糖值在正常上限附近，有轻微<strong>上升趋势</strong></li>
                                    <li>心率变异性良好，表明心脏功能稳定</li>
                                    <li>活动量略有下降，建议适当增加户外活动</li>
                                </ul>
                                <p>建议：</p>
                                <ol>
                                    <li>保持低盐饮食，控制血压波动</li>
                                    <li>增加蔬菜水果摄入，减少精致碳水化合物</li>
                                    <li>每日固定时间测量血压，记录数据</li>
                                </ol>
                            </div>
                        </div>
                    </div>

                    <div class="risk-tags-container">
                        <div class="risk-tag warning">
                            <i class="bi bi-exclamation-triangle me-2"></i> 血糖波动风险
                        </div>
                        <div class="risk-tag warning">
                            <i class="bi bi-exclamation-triangle me-2"></i> 运动不足风险
                        </div>
                    </div>
                </section>

                <section class="monitor-panel" v-if="currentMenu==='实时体征看板'">
                    <div class="monitor-panel-header"><i class="bi bi-heart-pulse-fill"></i>实时体征看板</div>

                    <!-- 生活监控模块 -->
                    <div class="lifestyle-monitor">
                        <h4><i class="bi bi-activity"></i> 老人生活状态实时监控</h4>
                        <div class="lifestyle-cards">
                            <!-- 运动情况 -->
                            <div class="lifestyle-card">
                                <h5><i class="bi bi-person-walking"></i> 运动情况</h5>
                                <div class="data-row">
                                    <strong>今日步数</strong>
                                    <span class="fw-bold">5,246 / 8,000</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-primary" role="progressbar" style="width: 65%" aria-valuenow="65" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <div class="data-row">
                                    <strong>活动时长</strong>
                                    <span>45 分钟</span>
                                </div>
                                <div class="data-row">
                                    <strong>消耗热量</strong>
                                    <span>156 千卡</span>
                                </div>
                                <div class="card-footer">
                                    <div class="trend-indicator positive">
                                        <i class="bi bi-arrow-up"></i>
                                        <span>较昨日增加 12%</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 睡眠情况 -->
                            <div class="lifestyle-card">
                                <h5><i class="bi bi-moon-stars-fill"></i> 睡眠情况</h5>
                                <div class="data-row">
                                    <strong>昨晚睡眠</strong>
                                    <span class="fw-bold">7.2 小时</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 80%" aria-valuenow="80" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <div class="data-row">
                                    <strong>深度睡眠</strong>
                                    <span>2.5 小时</span>
                                </div>
                                <div class="data-row">
                                    <strong>夜间起床</strong>
                                    <span>2 次</span>
                                </div>
                                <div class="card-footer">
                                    <div class="trend-indicator neutral">
                                        <i class="bi bi-dash"></i>
                                        <span>睡眠质量稳定</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 饮食情况 -->
                            <div class="lifestyle-card">
                                <h5><i class="bi bi-cup-hot-fill"></i> 饮食情况</h5>
                                <div class="data-row">
                                    <strong>总热量摄入</strong>
                                    <span class="fw-bold">1,860 千卡</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-warning" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <div class="data-row">
                                    <strong>蛋白质</strong>
                                    <span>65 克</span>
                                </div>
                                <div class="data-row">
                                    <strong>水分摄入</strong>
                                    <span>1200 毫升</span>
                                </div>
                                <div class="card-footer">
                                    <div class="trend-indicator negative">
                                        <i class="bi bi-arrow-down"></i>
                                        <span>需增加水分摄入</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="vital-signs-summary">
                        <div class="summary-title"><i class="bi bi-clipboard2-pulse-fill me-2"></i>健康状况总结</div>
                        <div class="summary-content">当前老人整体健康状况良好，部分指标（如血糖）存在轻微异常，建议关注饮食与运动。血压、心率等核心指标均在正常范围内。</div>
                    </div>
                    <table class="vital-signs-table">
                        <tr>
                            <th><i class="bi bi-heart-pulse-fill text-danger me-2"></i>血压</th>
                            <td data-status="warning">
                                <span class="value">146/90</span>
                                <span class="unit">mmHg</span>
                                <div class="last-update"><i class="bi bi-clock-history me-1"></i>更新于 10:30</div>
                            </td>
                        </tr>
                        <tr>
                            <th><i class="bi bi-droplet-fill text-primary me-2"></i>血糖</th>
                            <td data-status="warning">
                                <span class="value">7.6</span>
                                <span class="trend up">↑</span>
                                <span class="unit">mmol/L</span>
                                <div class="last-update"><i class="bi bi-clock-history me-1"></i>更新于 09:15</div>
                            </td>
                        </tr>
                        <tr>
                            <th><i class="bi bi-eyedropper text-info me-2"></i>糖化血红蛋白</th>
                            <td data-status="warning">
                                <span class="value">6.9</span>
                                <span class="unit">%</span>
                                <div class="last-update"><i class="bi bi-calendar-check me-1"></i>更新于 2025-05-20</div>
                            </td>
                        </tr>
                        <tr>
                            <th><i class="bi bi-capsule text-warning me-2"></i>总胆固醇</th>
                            <td data-status="warning">
                                <span class="value">5.5</span>
                                <span class="unit">mmol/L</span>
                                <div class="last-update"><i class="bi bi-calendar-check me-1"></i>更新于 2025-05-20</div>
                            </td>
                        </tr>
                        <tr>
                            <th><i class="bi bi-capsule-pill text-danger me-2"></i>低密度脂蛋白</th>
                            <td data-status="warning">
                                <span class="value">3.4</span>
                                <span class="unit">mmol/L</span>
                                <div class="last-update"><i class="bi bi-calendar-check me-1"></i>更新于 2025-05-20</div>
                            </td>
                        </tr>
                    </table>
                </section>
                <section class="monitor-panel" v-if="currentMenu==='活动行为分析'">
                    <div class="monitor-panel-header"><i class="bi bi-bar-chart-line-fill"></i>活动行为分析</div>
                    <div class="monitor-panel-desc">分析老人社交与活动参与度，发现孤独风险与活跃群体，辅助个性化干预。</div>

                    <div class="activity-analysis-container">
                        <div class="activity-chart-wrapper">
                            <h4 class="chart-title"><i class="bi bi-pie-chart-fill"></i>活动参与度</h4>
                            <div class="activity-chart">
                                <canvas id="activityRadar" height="350"></canvas>
                            </div>
                            <div class="analysis-conclusion">
                                <div class="conclusion-title"><i class="bi bi-lightbulb-fill me-2"></i>结论</div>
                                <p><i class="bi bi-info-circle-fill text-primary me-2"></i>老人在家人聚会中表现积极主动，社区活动和锻炼参与度有显著提升空间。</p>
                                <p><i class="bi bi-lightbulb-fill text-warning me-2"></i>建议：安排每周三的社区健身活动，增强社交互动能力，提高整体活动参与度。</p>
                            </div>
                        </div>

                        <div class="activity-chart-wrapper">
                            <h4 class="chart-title"><i class="bi bi-diagram-3-fill"></i>社交关系分析</h4>
                            <div class="activity-chart">
                                <canvas id="contactGraph" height="350"></canvas>
                            </div>
                            <div class="analysis-conclusion">
                                <div class="conclusion-title"><i class="bi bi-lightbulb-fill me-2"></i>结论</div>
                                <p><i class="bi bi-info-circle-fill text-primary me-2"></i>家庭关系是老人社交网络的核心支柱，但与社区医生和朋友的互动频率明显偏低。</p>
                                <p><i class="bi bi-lightbulb-fill text-warning me-2"></i>建议：促进与社区医生的定期健康交流，协助扩展社交圈层，降低社交孤立风险。</p>
                            </div>
                        </div>
                    </div>

                    <div class="activity-summary">
                        <h5 class="summary-title"><i class="bi bi-clipboard2-data-fill me-2"></i>综合评估</h5>
                        <div class="alert alert-info">
                            <p class="mb-2"><strong>观察结果：</strong>老人整体社交状态良好，但存在社交圈局限性。活动参与多样但频率不足，特别是在社区公共活动方面。</p>
                            <p class="mb-0"><strong>行动建议：</strong>每周至少增加2次有组织的外出活动，重点关注社区健康讲座和老年兴趣小组，丰富社交互动，提高生活质量。</p>
                        </div>
                    </div>
                </section>
                <section class="monitor-panel" v-if="currentMenu==='生活服务代理'">
                    <div class="monitor-panel-header"><i class="bi bi-cup-hot-fill me-2"></i>生活服务代理</div>
                    <div class="monitor-panel-desc">为老人提供多种生活服务，包括远程代订餐、家政服务等</div>

                    <div class="service-container">
                        <div class="service-form-wrapper">
                            <h4 class="service-subtitle mb-3"><i class="bi bi-cup-hot-fill me-2"></i>远程代订餐</h4>
                            <form class="service-form">
                                <div class="form-group">
                                    <label class="form-label"><i class="bi bi-calendar-date me-2"></i>选择日期</label>
                                    <div class="input-wrapper">
                                        <input type="date" class="form-control" value="2025-05-07">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">餐食类型</label>
                                    <div class="input-wrapper">
                                        <select class="form-select">
                                            <option selected>营养套餐A（低盐低油）</option>
                                            <option>营养套餐B（高蛋白）</option>
                                            <option>普通健康餐</option>
                                            <option>糖尿病专用餐</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">送餐时间</label>
                                    <div class="input-wrapper">
                                        <select class="form-select">
                                            <option>早餐 (07:00-08:00)</option>
                                            <option selected>午餐 (11:30-12:30)</option>
                                            <option>晚餐 (17:30-18:30)</option>
                                        </select>
                                    </div>
                                </div>

                                <button type="button" class="btn-submit"><i class="bi bi-send-fill me-2"></i>提交订餐请求</button>
                            </form>
                        </div>

                        <div class="service-history">
                            <div class="nutrition-advice mb-4">
                                <h5><i class="bi bi-info-circle-fill me-2"></i>营养建议</h5>
                                <p class="mb-0">根据老人健康状况，建议选择低盐低油的营养套餐A，有助于控制血压和血糖。每周可适当选择2-3次高蛋白餐，补充必要营养。</p>
                            </div>

                            <h4 class="history-title"><i class="bi bi-clock-history me-2"></i>历史订餐记录</h4>
                            <div class="history-list">
                                <div class="history-item">
                                    <div class="history-date">2025-05-06</div>
                                    <div class="history-content">
                                        <p><strong>营养套餐A</strong></p>
                                        <p>午餐 (11:30-12:30)</p>
                                        <span class="status-tag done"><i class="bi bi-check-circle me-1"></i>已送达</span>
                                    </div>
                                </div>
                                <div class="history-item">
                                    <div class="history-date">2025-05-05</div>
                                    <div class="history-content">
                                        <p><strong>营养套餐B</strong></p>
                                        <p>午餐 (11:30-12:30)</p>
                                        <span class="status-tag done"><i class="bi bi-check-circle me-1"></i>已送达</span>
                                    </div>
                                </div>
                                <div class="history-item">
                                    <div class="history-date">2025-05-04</div>
                                    <div class="history-content">
                                        <p><strong>营养套餐A</strong></p>
                                        <p>晚餐 (17:30-18:30)</p>
                                        <span class="status-tag done"><i class="bi bi-check-circle me-1"></i>已送达</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                <section class="monitor-panel" v-if="currentMenu==='实时定位监控'">
                    <div class="monitor-panel-header">实时位置与安全围栏</div>
                    <div class="monitor-panel-desc">可视化展示老人当前位置和活动轨迹，自定义安全电子围栏，异常行为自动预警。</div>

                    <div class="location-container">
                        <!-- 位置信息卡片 -->
                        <div class="location-info-card mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0"><i class="bi bi-geo-alt-fill me-2 text-primary"></i>当前位置信息</h5>
                                <button class="btn btn-sm btn-outline-primary" @click="refreshLocation">
                                    <i class="bi bi-arrow-clockwise me-1"></i> 刷新位置
                                </button>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <label><i class="bi bi-person-fill me-1"></i>老人姓名：</label>
                                        <span>[[currentLocationInfo.name]]</span>
                                    </div>
                                    <div class="info-item">
                                        <label><i class="bi bi-geo-alt me-1"></i>详细地址：</label>
                                        <span>[[currentLocationInfo.address]]</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <label><i class="bi bi-clock me-1"></i>更新时间：</label>
                                        <span>[[currentLocationInfo.lastUpdate]]</span>
                                    </div>
                                    <div class="info-item">
                                        <label><i class="bi bi-compass me-1"></i>GPS坐标：</label>
                                        <span>[[currentLocationInfo.coordinates]]</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="map-container">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h4 class="map-title mb-0">实时位置与安全围栏</h4>
                                <div class="fence-controls">
                                    <button class="btn btn-sm btn-outline-primary"><i class="bi bi-plus-circle"></i> 添加围栏</button>
                                    <button class="btn btn-sm btn-outline-secondary"><i class="bi bi-gear"></i> 围栏设置</button>
                                </div>
                            </div>
                            <div class="amap-container" id="amapRealtime"></div>
                            <div class="mt-2 text-muted">
                                <i class="bi bi-info-circle me-1"></i>
                                点击地图标记可查看详细位置信息，蓝色圆圈为500米安全围栏范围
                            </div>
                        </div>

                        <div class="entry-exit-container">
                            <h5 class="mb-3">进出记录</h5>
                            <table class="entry-exit-table">
                                <thead>
                                    <tr>
                                        <th>时间</th>
                                        <th>进出类型</th>
                                        <th>位置</th>
                                        <th>停留时长</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>09:30</td>
                                        <td>进入</td>
                                        <td>家</td>
                                        <td>2小时</td>
                                    </tr>
                                    <tr>
                                        <td>12:00</td>
                                        <td>离开</td>
                                        <td>家</td>
                                        <td>--</td>
                                    </tr>
                                    <tr>
                                        <td>12:15</td>
                                        <td>进入</td>
                                        <td>社区餐厅</td>
                                        <td>45分钟</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>
                <section class="monitor-panel" v-if="currentMenu==='应急响应中心'">
                    <div class="monitor-panel-header">应急响应中心</div>
                    <div class="monitor-panel-desc">集中管理所有紧急呼叫与响应流程，支持录音回放与进度跟踪。</div>

                    <div class="emergency-response">
                        <table class="emergency-table">
                            <thead>
                                <tr>
                                    <th>呼叫时间</th>
                                    <th>类型</th>
                                    <th>状态</th>
                                    <th>录音</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>2025-05-06 10:30</td>
                                    <td>跌倒报警</td>
                                    <td><span class="emergency-badge danger">已处理</span></td>
                                    <td><a href="#" class="btn btn-sm btn-outline-primary"><i class="bi bi-play-circle"></i> 播放</a></td>
                                </tr>
                                <tr>
                                    <td>2025-05-05 18:20</td>
                                    <td>紧急呼叫</td>
                                    <td><span class="emergency-badge warning">处理中</span></td>
                                    <td><a href="#" class="btn btn-sm btn-outline-primary"><i class="bi bi-play-circle"></i> 播放</a></td>
                                </tr>
                            </tbody>
                        </table>

                        <div class="emergency-progress">
                            <div class="emergency-progress-bar" style="width: 80%">社区响应进度：80%</div>
                        </div>

                        <div class="emergency-actions">
                            <button class="primary" style="background-color: #1765d5; border-color: #1765d5;"><i class="bi bi-telephone"></i> 呼叫老人</button>
                            <button class="primary" style="background-color: #1765d5; border-color: #1765d5;"><i class="bi bi-headset"></i> 呼叫社区工作人员</button>
                        </div>
                    </div>
                </section>
                <section class="monitor-panel" v-if="currentMenu==='医疗档案管理'">
                    <div class="monitor-panel-header"><i class="bi bi-file-medical-fill me-2"></i>医疗档案管理</div>
                    <div class="monitor-panel-desc">可追溯全部历史病历、体检报告，AI自动标记重点异常，支持上传/下载与详情查看。</div>

                    <div class="medical-records-header">
                        <div class="medical-records-search">
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-search"></i></span>
                                <input type="text" class="form-control form-control-lg" placeholder="搜索病历、报告..." style="border-radius: 0.5rem 0 0 0.5rem;">
                                <button class="btn btn-primary" style="border-radius: 0 0.5rem 0.5rem 0;"><i class="bi bi-search me-1"></i> 搜索</button>
                            </div>
                            <div class="medical-records-filter mt-3">
                                <button class="filter-btn active"><i class="bi bi-grid-3x3-gap me-1"></i> 全部</button>
                                <button class="filter-btn"><i class="bi bi-journal-medical me-1"></i> 病历</button>
                                <button class="filter-btn"><i class="bi bi-file-earmark-medical me-1"></i> 检查报告</button>
                                <button class="filter-btn"><i class="bi bi-capsule me-1"></i> 用药记录</button>
                            </div>
                        </div>
                        <button class="btn btn-primary upload-btn"><i class="bi bi-upload me-2"></i> 上传新报告</button>
                    </div>

                    <div class="medical-records">
                        <div class="medical-card">
                            <div class="card-header">
                                <div class="card-icon"><i class="bi bi-clipboard2-pulse"></i></div>
                                <div class="card-title-wrapper">
                                    <h3 class="card-title">体检报告</h3>
                                    <span class="card-date"><i class="bi bi-calendar3 me-1"></i>2025-05-20</span>
                                </div>
                            </div>
                            <div class="card-content">
                                <p>年度体检报告，发现血压、血糖偏高，血脂轻度异常。</p>
                                <div class="card-tags">
                                    <span class="card-tag"><i class="bi bi-tag-fill me-1"></i>体检</span>
                                    <span class="card-tag warning"><i class="bi bi-exclamation-triangle-fill me-1"></i>高血压</span>
                                    <span class="card-tag warning"><i class="bi bi-exclamation-triangle-fill me-1"></i>糖尿病</span>
                                </div>
                            </div>
                            <div class="card-actions">
                                <button class="action-btn primary"><i class="bi bi-robot me-1"></i> AI解读</button>
                                <button class="action-btn"><i class="bi bi-download me-1"></i> 下载</button>
                                <button class="action-btn"><i class="bi bi-search me-1"></i> 详情</button>
                            </div>
                        </div>

                        <div class="medical-card">
                            <div class="card-header">
                                <div class="card-icon"><i class="bi bi-hospital"></i></div>
                                <div class="card-title-wrapper">
                                    <h3 class="card-title">门诊记录</h3>
                                    <span class="card-date"><i class="bi bi-calendar3 me-1"></i>2025-05-23</span>
                                </div>
                            </div>
                            <div class="card-content">
                                <p>因体检发现血压、血糖偏高就诊，制定治疗方案。</p>
                                <div class="card-tags">
                                    <span class="card-tag"><i class="bi bi-tag-fill me-1"></i>门诊</span>
                                    <span class="card-tag warning"><i class="bi bi-exclamation-triangle-fill me-1"></i>高血压1级</span>
                                    <span class="card-tag warning"><i class="bi bi-exclamation-triangle-fill me-1"></i>2型糖尿病</span>
                                </div>
                            </div>
                            <div class="card-actions">
                                <button class="action-btn primary"><i class="bi bi-robot me-1"></i> AI解读</button>
                                <button class="action-btn"><i class="bi bi-search me-1"></i> 详情</button>
                            </div>
                        </div>

                        <div class="medical-card">
                            <div class="card-header">
                                <div class="card-icon"><i class="bi bi-eyedropper"></i></div>
                                <div class="card-title-wrapper">
                                    <h3 class="card-title">OGTT检查</h3>
                                    <span class="card-date"><i class="bi bi-calendar3 me-1"></i>2025-05-25</span>
                                </div>
                            </div>
                            <div class="card-content">
                                <p>糖耐量试验确诊2型糖尿病，胰岛素抵抗明显。</p>
                                <div class="card-tags">
                                    <span class="card-tag"><i class="bi bi-tag-fill me-1"></i>检查</span>
                                    <span class="card-tag warning"><i class="bi bi-exclamation-triangle-fill me-1"></i>糖尿病确诊</span>
                                </div>
                            </div>
                            <div class="card-actions">
                                <button class="action-btn"><i class="bi bi-download me-1"></i> 下载</button>
                                <button class="action-btn"><i class="bi bi-search me-1"></i> 详情</button>
                            </div>
                        </div>

                        <div class="medical-card">
                            <div class="card-header">
                                <div class="card-icon"><i class="bi bi-building-fill-add"></i></div>
                                <div class="card-title-wrapper">
                                    <h3 class="card-title">住院记录</h3>
                                    <span class="card-date"><i class="bi bi-calendar3 me-1"></i>2025-06-01 至 2025-06-05</span>
                                </div>
                            </div>
                            <div class="card-content">
                                <p>因血糖波动较大住院调整，建议短期住院调整。</p>
                                <div class="card-tags">
                                    <span class="card-tag"><i class="bi bi-tag-fill me-1"></i>住院</span>
                                    <span class="card-tag warning"><i class="bi bi-exclamation-triangle-fill me-1"></i>血糖波动</span>
                                </div>
                            </div>
                            <div class="card-actions">
                                <button class="action-btn"><i class="bi bi-search me-1"></i> 详情</button>
                                <button class="action-btn"><i class="bi bi-download me-1"></i> 下载</button>
                            </div>
                        </div>
                    </div>
                </section>
                <section class="monitor-panel" v-if="!currentMenu">
                    <div class="monitor-panel-header">欢迎使用家属监护 2.0</div>
                    <div class="text-muted">请选择左侧功能模块，右侧将显示对应的多维度监控与分析视图。支持分屏、四宫格、自由拖拽组合。</div>
                </section>
            </div>
        </div>
    </div>
</div>
{% endblock %}
{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://webapi.amap.com/maps?v=2.0&key=3ba2467a72df9bf471cbcbde0ebd9ea7"></script>
<script>
    new Vue({
        el: '#monitor-app',
        delimiters: ['[[', ']]'],
        data: {
            currentMenu: '实时体征看板',
            currentUser: null,
            currentElderly: null,
            boundElderlyList: [],
            // 图表对象
            charts: {
                trendChart: null,
                activityRadar: null,
                contactGraph: null
            },
            // 地图对象
            map: null,
            // 当前位置信息
            currentLocationInfo: {
                name: '加载中...',
                address: '获取地址中...',
                lastUpdate: '',
                coordinates: ''
            }
        },
        mounted() {
            // 获取当前用户信息和绑定的老人列表
            this.getCurrentUser();

            // 添加watch监听当前菜单变化，以便在菜单切换时初始化对应的图表
            this.$watch('currentMenu', (newMenu, oldMenu) => {
                // 在组件更新后初始化相应的图表
                this.$nextTick(() => {
                    switch(newMenu) {
                        case '历史趋势分析':
                            this.initTrendChart();
                            break;
                        case '活动行为分析':
                            this.initActivityCharts();
                            break;
                        case '实时定位监控':
                            this.initMap();
                            break;
                    }
                });
            });
        },
        methods: {
            selectMenu(menu) {
                this.currentMenu = menu;
            },
            getCurrentUser() {
                axios.get('/api/auth/current_user')
                    .then(response => {
                        if (response.data.logged_in) {
                            this.currentUser = {
                                id: response.data.user_id,
                                name: response.data.name,
                                type: response.data.user_type
                            };
                            this.getBoundElderlyList();
                        }
                    })
                    .catch(error => {
                        console.error('获取用户信息失败:', error);
                    });
            },
            getBoundElderlyList() {
                axios.get(`/api/family/bound_elderly_list/${this.currentUser.id}`)
                    .then(response => {
                        if (response.data.success) {
                            this.boundElderlyList = response.data.elderly_list;
                            // 设置当前选中的老人为主绑定老人
                            const primaryElderly = this.boundElderlyList.find(e => e.is_primary);
                            if (primaryElderly) {
                                this.currentElderly = primaryElderly;
                                this.loadElderlyData(primaryElderly.user_id);
                            }
                        }
                    })
                    .catch(error => {
                        console.error('获取绑定老人列表失败:', error);
                    });
            },
            loadElderlyData(elderlyId) {
                // 加载该老人的数据，更新页面内容
                console.log(`加载老人ID ${elderlyId} 的数据`);

                // 根据当前选项卡初始化相应的图表
                this.$nextTick(() => {
                    switch(this.currentMenu) {
                        case '历史趋势分析':
                            this.initTrendChart();
                            break;
                        case '活动行为分析':
                            this.initActivityCharts();
                            break;
                        case '实时定位监控':
                            this.initMap();
                            break;
                    }
                });
            },

            // 初始化历史趋势图表
            initTrendChart() {
                if (document.getElementById('trendChart')) {
                    // 清除旧图表
                    if (this.charts.trendChart) {
                        this.charts.trendChart.destroy();
                    }

                    const ctx = document.getElementById('trendChart').getContext('2d');
                    this.charts.trendChart = new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                            datasets: [
                                {
                                    label: '收缩压',
                                    data: [145, 142, 138, 146, 150, 142, 140],
                                    borderColor: '#1765d5',
                                    backgroundColor: 'rgba(23, 101, 213, 0.1)',
                                    borderWidth: 2,
                                    fill: true,
                                    tension: 0.3
                                },
                                {
                                    label: '舒张压',
                                    data: [92, 88, 85, 90, 93, 87, 85],
                                    borderColor: '#5B8FF9',
                                    backgroundColor: 'rgba(91, 143, 249, 0.1)',
                                    borderWidth: 2,
                                    fill: true,
                                    tension: 0.3
                                },
                                {
                                    label: '空腹血糖',
                                    data: [7.2, 7.4, 7.1, 7.6, 7.8, 7.5, 7.3],
                                    borderColor: '#ff7875',
                                    backgroundColor: 'rgba(255, 120, 117, 0.1)',
                                    borderWidth: 2,
                                    fill: true,
                                    tension: 0.3,
                                    yAxisID: 'y1'
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            scales: {
                                y: {
                                    title: {
                                        display: true,
                                        text: '血压 (mmHg)'
                                    }
                                },
                                y1: {
                                    position: 'right',
                                    title: {
                                        display: true,
                                        text: '血糖 (mmol/L)'
                                    },
                                    grid: {
                                        drawOnChartArea: false
                                    }
                                }
                            },
                            plugins: {
                                title: {
                                    display: true,
                                    text: '近7天健康指标趋势',
                                    font: {
                                        size: 16
                                    }
                                },
                                tooltip: {
                                    mode: 'index',
                                    intersect: false
                                },
                                legend: {
                                    position: 'bottom'
                                }
                            }
                        }
                    });
                }
            },

            // 初始化活动行为分析图表
            initActivityCharts() {
                // 初始化活动雷达图
                if (document.getElementById('activityRadar')) {
                    if (this.charts.activityRadar) {
                        this.charts.activityRadar.destroy();
                    }

                    const radarCtx = document.getElementById('activityRadar').getContext('2d');
                    this.charts.activityRadar = new Chart(radarCtx, {
                        type: 'radar',
                        data: {
                            labels: ['家人聚会', '社区活动', '健康锻炼', '医疗就诊', '生活自理', '朋友交往'],
                            datasets: [{
                                label: '参与度评分',
                                data: [85, 40, 55, 75, 90, 45],
                                backgroundColor: 'rgba(23, 101, 213, 0.2)',
                                borderColor: '#1765d5',
                                pointBackgroundColor: '#1765d5',
                                pointBorderColor: '#fff',
                                pointHoverBackgroundColor: '#fff',
                                pointHoverBorderColor: '#1765d5'
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                title: {
                                    display: true,
                                    text: '老人活动参与度雷达图',
                                    font: {
                                        size: 16
                                    }
                                }
                            },
                            scales: {
                                r: {
                                    angleLines: {
                                        display: true
                                    },
                                    min: 0,
                                    max: 100
                                }
                            }
                        }
                    });
                }

                // 初始化社交关系图
                if (document.getElementById('contactGraph')) {
                    if (this.charts.contactGraph) {
                        this.charts.contactGraph.destroy();
                    }

                    const graphCtx = document.getElementById('contactGraph').getContext('2d');
                    this.charts.contactGraph = new Chart(graphCtx, {
                        type: 'polarArea',
                        data: {
                            labels: ['家庭成员', '邻居', '社区医生', '朋友', '社区工作者'],
                            datasets: [{
                                label: '互动频率',
                                data: [80, 45, 25, 35, 60],
                                backgroundColor: [
                                    'rgba(23, 101, 213, 0.7)',
                                    'rgba(91, 143, 249, 0.7)',
                                    'rgba(96, 183, 96, 0.7)',
                                    'rgba(250, 173, 20, 0.7)',
                                    'rgba(255, 120, 117, 0.7)'
                                ]
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                title: {
                                    display: true,
                                    text: '老人社交互动频率分析',
                                    font: {
                                        size: 16
                                    }
                                },
                                legend: {
                                    position: 'bottom'
                                }
                            }
                        }
                    });
                }
            },

            // 初始化地图
            async initMap() {
                // 确保地图容器存在
                if (document.getElementById('amapRealtime')) {
                    // 创建地图实例
                    if (!this.map) {
                        this.map = new AMap.Map('amapRealtime', {
                            zoom: 14,
                            center: [120.1626, 30.2729]  // 默认杭州位置
                        });

                        // 添加工具条控件
                        this.map.plugin(["AMap.ToolBar"], function() {
                            const toolBar = new AMap.ToolBar();
                            this.map.addControl(toolBar);
                        });
                    }

                    // 加载老人的实时位置
                    await this.loadElderlyLocation();
                }
            },

            async loadElderlyLocation() {
                try {
                    // 获取绑定的老人ID（这里应该从登录信息获取）
                    const elderlyId = 'E01'; // 示例：家属F01绑定的老人E01

                    const response = await axios.get(`/api/emergency/location/${elderlyId}`);

                    if (response.data && response.data.gps_location) {
                        const [lat, lng] = response.data.gps_location.split(',').map(Number);

                        // 更新地图中心
                        this.map.setCenter([lng, lat]);

                        // 清除现有标记
                        this.map.clearMap();

                        // 添加老人位置标记
                        const marker = new AMap.Marker({
                            position: [lng, lat],
                            title: `${response.data.name}的当前位置`,
                            icon: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png'
                        });

                        this.map.add(marker);

                        // 添加安全围栏
                        const circle = new AMap.Circle({
                            center: [lng, lat],
                            radius: 500,  // 500米安全围栏
                            fillColor: 'rgba(23, 101, 213, 0.1)',
                            strokeColor: '#1765d5',
                            strokeWeight: 2
                        });

                        this.map.add(circle);

                        // 获取地址信息
                        this.getAddressFromCoordinates(lng, lat, response.data.name);

                        // 更新位置信息显示
                        this.updateLocationInfo(response.data);

                    } else {
                        console.warn('未获取到老人位置信息');
                        this.showDefaultLocation();
                    }
                } catch (error) {
                    console.error('加载老人位置失败:', error);
                    this.showDefaultLocation();
                }
            },

            showDefaultLocation() {
                // 显示默认位置
                const defaultLng = 120.1626;
                const defaultLat = 30.2729;

                this.map.setCenter([defaultLng, defaultLat]);

                const marker = new AMap.Marker({
                    position: [defaultLng, defaultLat],
                    title: '默认位置',
                    icon: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png'
                });

                this.map.add(marker);

                // 更新位置信息显示
                this.currentLocationInfo = {
                    name: '位置信息获取中...',
                    address: '杭州市西湖区（默认位置）',
                    lastUpdate: new Date().toLocaleString(),
                    coordinates: `${defaultLat}, ${defaultLng}`
                };
            },

            getAddressFromCoordinates(lng, lat, elderlyName) {
                // 使用高德地图API进行逆地理编码
                if (typeof AMap !== 'undefined') {
                    AMap.plugin('AMap.Geocoder', () => {
                        const geocoder = new AMap.Geocoder();
                        geocoder.getAddress([lng, lat], (status, result) => {
                            if (status === 'complete' && result.info === 'OK') {
                                const address = result.regeocode.formattedAddress;

                                // 更新位置信息
                                this.currentLocationInfo = {
                                    name: elderlyName,
                                    address: address,
                                    lastUpdate: new Date().toLocaleString(),
                                    coordinates: `${lat}, ${lng}`
                                };

                                // 添加信息窗口
                                const infoWindow = new AMap.InfoWindow({
                                    content: `
                                        <div style="padding: 10px;">
                                            <h6>${elderlyName}的位置</h6>
                                            <p><strong>地址:</strong> ${address}</p>
                                            <p><strong>坐标:</strong> ${lat}, ${lng}</p>
                                            <p><strong>更新时间:</strong> ${new Date().toLocaleString()}</p>
                                        </div>
                                    `,
                                    offset: new AMap.Pixel(0, -30)
                                });

                                // 点击标记显示信息窗口
                                const markers = this.map.getAllOverlays('marker');
                                if (markers.length > 0) {
                                    markers[0].on('click', () => {
                                        infoWindow.open(this.map, markers[0].getPosition());
                                    });
                                }
                            } else {
                                console.error('地址解析失败');
                                this.currentLocationInfo = {
                                    name: elderlyName,
                                    address: '地址解析失败',
                                    lastUpdate: new Date().toLocaleString(),
                                    coordinates: `${lat}, ${lng}`
                                };
                            }
                        });
                    });
                }
            },

            updateLocationInfo(locationData) {
                // 更新位置信息显示
                this.currentLocationInfo = {
                    name: locationData.name,
                    address: '正在获取地址...',
                    lastUpdate: locationData.last_sync_time || new Date().toLocaleString(),
                    coordinates: locationData.gps_location
                };
            },

            refreshLocation() {
                // 刷新位置信息
                this.loadElderlyLocation();
            }
        }
    });
</script>
{% endblock %}